#!/usr/bin/env node

/**
 * EcoBuilder Demo Script
 * This script demonstrates the core functionality of the EcoBuilder system
 */

const axios = require('axios');
const chalk = require('chalk');

const API_BASE = 'http://localhost:3000/api';

// Demo configuration
const DEMO_CONFIG = {
  partnerSearchCriteria: {
    industry: 'technology',
    size: 'medium',
    region: 'North America',
    keywords: ['AI', 'automation', 'SaaS']
  },
  emailTemplate: {
    subject: 'Partnership Opportunity with EcoBuilder',
    tone: 'professional',
    purpose: 'Initial partnership discussion'
  }
};

class EcoBuilderDemo {
  constructor() {
    this.apiClient = axios.create({
      baseURL: API_BASE,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = `[${timestamp}]`;
    
    switch (type) {
      case 'success':
        console.log(chalk.green(`${prefix} ✅ ${message}`));
        break;
      case 'error':
        console.log(chalk.red(`${prefix} ❌ ${message}`));
        break;
      case 'warning':
        console.log(chalk.yellow(`${prefix} ⚠️  ${message}`));
        break;
      case 'info':
      default:
        console.log(chalk.blue(`${prefix} ℹ️  ${message}`));
        break;
    }
  }

  async checkSystemHealth() {
    this.log('Checking system health...');
    
    try {
      const response = await this.apiClient.get('/health');
      this.log('System is healthy and ready', 'success');
      return true;
    } catch (error) {
      this.log('System health check failed. Make sure the backend is running.', 'error');
      return false;
    }
  }

  async demonstrateAgentSystem() {
    this.log('🤖 Demonstrating Agent System');
    
    try {
      // Get all agents
      const agentsResponse = await this.apiClient.get('/agents');
      const agents = agentsResponse.data.data || [];
      
      this.log(`Found ${agents.length} agents in the system`);
      
      agents.forEach(agent => {
        this.log(`  - ${agent.name} (${agent.type}): ${agent.status}`);
      });

      // Start a Partner Scout agent if available
      const partnerScout = agents.find(agent => agent.type === 'PARTNER_SCOUT');
      if (partnerScout && partnerScout.status === 'IDLE') {
        this.log('Starting Partner Scout agent...');
        await this.apiClient.post(`/agents/${partnerScout.id}/start`);
        this.log('Partner Scout agent started', 'success');
      }

      return agents;
    } catch (error) {
      this.log(`Failed to demonstrate agent system: ${error.message}`, 'error');
      return [];
    }
  }

  async demonstratePartnerDiscovery() {
    this.log('🔍 Demonstrating Partner Discovery');
    
    try {
      // Create a partner discovery task
      const taskData = {
        title: 'Demo: Find Technology Partners',
        type: 'FIND_PARTNERS',
        priority: 'HIGH',
        data: DEMO_CONFIG.partnerSearchCriteria,
        description: 'Demonstration of automated partner discovery'
      };

      const taskResponse = await this.apiClient.post('/tasks', taskData);
      const task = taskResponse.data.data;
      
      this.log(`Created partner discovery task: ${task.id}`);
      this.log('Task will be processed by available agents');
      
      // Monitor task progress
      await this.monitorTask(task.id);
      
      return task;
    } catch (error) {
      this.log(`Failed to demonstrate partner discovery: ${error.message}`, 'error');
      return null;
    }
  }

  async demonstrateWorkflowExecution() {
    this.log('⚡ Demonstrating Workflow Execution');
    
    try {
      // Execute a sample workflow
      const workflowData = {
        workflowId: 'partner-onboarding-demo',
        context: {
          partnerName: 'Demo Tech Corp',
          industry: 'technology',
          contactEmail: '<EMAIL>'
        }
      };

      const executionResponse = await this.apiClient.post('/workflows/execute', workflowData);
      const executionId = executionResponse.data.data.executionId;
      
      this.log(`Started workflow execution: ${executionId}`);
      
      // Monitor workflow progress
      await this.monitorWorkflow(executionId);
      
      return executionId;
    } catch (error) {
      this.log(`Failed to demonstrate workflow execution: ${error.message}`, 'error');
      return null;
    }
  }

  async monitorTask(taskId, maxAttempts = 10) {
    this.log(`Monitoring task ${taskId}...`);
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await this.apiClient.get(`/tasks/${taskId}`);
        const task = response.data.data;
        
        this.log(`Task ${taskId} status: ${task.status} (${task.progress}%)`);
        
        if (task.status === 'COMPLETED') {
          this.log(`Task completed successfully!`, 'success');
          if (task.result) {
            this.log(`Result: ${JSON.stringify(task.result, null, 2)}`);
          }
          break;
        } else if (task.status === 'FAILED') {
          this.log(`Task failed: ${task.error}`, 'error');
          break;
        } else if (task.status === 'CANCELLED') {
          this.log(`Task was cancelled`, 'warning');
          break;
        }
        
        // Wait before next check
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        this.log(`Error monitoring task: ${error.message}`, 'error');
        break;
      }
    }
  }

  async monitorWorkflow(executionId, maxAttempts = 15) {
    this.log(`Monitoring workflow ${executionId}...`);
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await this.apiClient.get(`/workflows/executions/${executionId}`);
        const execution = response.data.data;
        
        this.log(`Workflow ${executionId} status: ${execution.status}`);
        
        if (execution.status === 'COMPLETED') {
          this.log(`Workflow completed successfully!`, 'success');
          if (execution.result) {
            this.log(`Result: ${JSON.stringify(execution.result, null, 2)}`);
          }
          break;
        } else if (execution.status === 'FAILED') {
          this.log(`Workflow failed: ${execution.error}`, 'error');
          break;
        } else if (execution.status === 'CANCELLED') {
          this.log(`Workflow was cancelled`, 'warning');
          break;
        }
        
        // Wait before next check
        await new Promise(resolve => setTimeout(resolve, 3000));
      } catch (error) {
        this.log(`Error monitoring workflow: ${error.message}`, 'error');
        break;
      }
    }
  }

  async demonstrateAnalytics() {
    this.log('📊 Demonstrating Analytics');
    
    try {
      // Get dashboard stats
      const statsResponse = await this.apiClient.get('/analytics/dashboard');
      const stats = statsResponse.data.data;
      
      this.log('Dashboard Statistics:');
      this.log(`  - Total Partners: ${stats.totalPartners || 0}`);
      this.log(`  - Active Agents: ${stats.activeAgents || 0}`);
      this.log(`  - Running Tasks: ${stats.runningTasks || 0}`);
      this.log(`  - Completed Workflows: ${stats.completedWorkflows || 0}`);
      
      // Get agent stats
      const agentStatsResponse = await this.apiClient.get('/analytics/agents');
      const agentStats = agentStatsResponse.data.data;
      
      this.log('Agent Statistics:');
      Object.entries(agentStats).forEach(([agentType, count]) => {
        this.log(`  - ${agentType}: ${count}`);
      });
      
      return { stats, agentStats };
    } catch (error) {
      this.log(`Failed to demonstrate analytics: ${error.message}`, 'error');
      return null;
    }
  }

  async runFullDemo() {
    console.log(chalk.cyan.bold('\n🌱 EcoBuilder Multi-Agent System Demo\n'));
    
    // Check system health
    const isHealthy = await this.checkSystemHealth();
    if (!isHealthy) {
      this.log('Cannot proceed with demo. Please start the backend server first.', 'error');
      return;
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    try {
      // Demonstrate each component
      await this.demonstrateAgentSystem();
      console.log('\n' + '-'.repeat(30) + '\n');
      
      await this.demonstratePartnerDiscovery();
      console.log('\n' + '-'.repeat(30) + '\n');
      
      await this.demonstrateWorkflowExecution();
      console.log('\n' + '-'.repeat(30) + '\n');
      
      await this.demonstrateAnalytics();
      
      console.log('\n' + '='.repeat(50) + '\n');
      this.log('🎉 Demo completed successfully!', 'success');
      this.log('Visit http://localhost:5173 to see the web interface');
      
    } catch (error) {
      this.log(`Demo failed: ${error.message}`, 'error');
    }
  }
}

// Run the demo if this script is executed directly
if (require.main === module) {
  const demo = new EcoBuilderDemo();
  demo.runFullDemo().catch(error => {
    console.error(chalk.red('Demo failed:'), error);
    process.exit(1);
  });
}

module.exports = EcoBuilderDemo;

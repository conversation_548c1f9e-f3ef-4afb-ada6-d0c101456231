-- EcoBuilder Database Initialization Script
-- This script sets up the initial database structure and data

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types
DO $$ BEGIN
    CREATE TYPE agent_status AS ENUM ('IDLE', 'WORKING', 'ERROR', 'OFFLINE', 'MAINTENANCE');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE agent_type AS ENUM ('PARTNER_SCOUT', 'PARTNER_RECRUITER', 'PARTNER_TRAINER', 'OPPORTUNITY_MANAGER', 'MARKET_EXPANDER');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE task_status AS ENUM ('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', 'RETRYING');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE task_type AS ENUM ('FIND_PARTNERS', 'SCORE_PARTNER', 'SEND_EMAIL', 'SCHEDULE_MEETING', 'CREATE_PROPOSAL', 'ANALYZE_OPPORTUNITY', 'GENERATE_REPORT', 'TRAIN_PARTNER', 'MARKET_ANALYSIS');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE priority AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'URGENT');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE partner_status AS ENUM ('PROSPECT', 'CONTACTED', 'QUALIFIED', 'NEGOTIATING', 'ACTIVE', 'INACTIVE', 'REJECTED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE company_size AS ENUM ('STARTUP', 'SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE opportunity_stage AS ENUM ('DISCOVERY', 'QUALIFICATION', 'PROPOSAL', 'NEGOTIATION', 'CLOSED_WON', 'CLOSED_LOST');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE activity_type AS ENUM ('EMAIL', 'CALL', 'MEETING', 'PROPOSAL', 'CONTRACT', 'TRAINING', 'FOLLOW_UP', 'OTHER');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE workflow_execution_status AS ENUM ('RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE message_type AS ENUM ('TASK_UPDATE', 'AGENT_STATUS', 'WORKFLOW_UPDATE', 'NOTIFICATION', 'ALERT', 'SYSTEM');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create function to generate short IDs
CREATE OR REPLACE FUNCTION generate_short_id(prefix TEXT DEFAULT '')
RETURNS TEXT AS $$
BEGIN
    RETURN prefix || LOWER(SUBSTRING(REPLACE(gen_random_uuid()::TEXT, '-', ''), 1, 8));
END;
$$ LANGUAGE plpgsql;

-- Insert initial data (will be handled by Prisma seed)
-- This is just for reference

-- Sample configuration data
INSERT INTO "Config" (key, value, description) VALUES
('system.max_concurrent_tasks', '10', 'Maximum number of concurrent tasks'),
('system.task_timeout_ms', '600000', 'Task timeout in milliseconds'),
('system.retry_delay_ms', '5000', 'Retry delay in milliseconds'),
('agents.partner_scout.enabled', 'true', 'Enable Partner Scout agent'),
('agents.partner_recruiter.enabled', 'true', 'Enable Partner Recruiter agent'),
('agents.partner_trainer.enabled', 'true', 'Enable Partner Trainer agent'),
('agents.opportunity_manager.enabled', 'true', 'Enable Opportunity Manager agent'),
('agents.market_expander.enabled', 'true', 'Enable Market Expander agent')
ON CONFLICT (key) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tasks_status ON "Task"(status);
CREATE INDEX IF NOT EXISTS idx_tasks_agent_id ON "Task"(agent_id);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON "Task"(created_at);
CREATE INDEX IF NOT EXISTS idx_partners_status ON "Partner"(status);
CREATE INDEX IF NOT EXISTS idx_partners_industry ON "Partner"(industry);
CREATE INDEX IF NOT EXISTS idx_opportunities_stage ON "Opportunity"(stage);
CREATE INDEX IF NOT EXISTS idx_activities_type ON "Activity"(type);
CREATE INDEX IF NOT EXISTS idx_activities_created_at ON "Activity"(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_agent_id ON "Message"(agent_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON "Message"(created_at);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_status ON "WorkflowExecution"(status);

-- Create full-text search indexes
CREATE INDEX IF NOT EXISTS idx_partners_search ON "Partner" USING gin(to_tsvector('english', company_name || ' ' || COALESCE(description, '')));
CREATE INDEX IF NOT EXISTS idx_opportunities_search ON "Opportunity" USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ecobuilder;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ecobuilder;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO ecobuilder;

-- Log initialization
INSERT INTO "SystemLog" (level, message, metadata) VALUES
('INFO', 'Database initialized successfully', '{"timestamp": "' || CURRENT_TIMESTAMP || '", "version": "1.0.0"}')
ON CONFLICT DO NOTHING;

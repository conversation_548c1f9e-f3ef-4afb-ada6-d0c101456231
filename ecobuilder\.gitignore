# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Build outputs
build/
dist/
*.tgz

# Logs
logs
*.log

# Database
*.db
*.sqlite
*.sqlite3

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files
tmp/
temp/
.tmp/

# Docker
.dockerignore

# Prisma
backend/prisma/migrations/
backend/prisma/dev.db*

# Generated files
backend/src/generated/
frontend/src/generated/
shared/dist/

# Test files
coverage/
.nyc_output/
test-results/
playwright-report/

# Environment specific
.env.local
.env.development
.env.test
.env.production

# Package manager lock files (keep one, remove others)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Local development
start-backend.bat
start-frontend.bat
stop-dev.sh

# Backup files
*.backup
*.bak

# Redis dump
dump.rdb

# Application specific
uploads/
logs/
data/

# AI/ML models (if any)
models/
*.model
*.pkl
*.h5

# Documentation build
docs/_build/
docs/site/

# Certificates
*.pem
*.key
*.crt
*.csr

# Local configuration
config/local.json
config/development.json
config/production.json

# Monitoring and analytics
.monitoring/
analytics/

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# MacOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# JetBrains
.idea/
*.iml
*.ipr
*.iws

# Visual Studio
.vs/
*.user
*.userosscache
*.sln.docstates

# Sublime Text
*.sublime-workspace
*.sublime-project

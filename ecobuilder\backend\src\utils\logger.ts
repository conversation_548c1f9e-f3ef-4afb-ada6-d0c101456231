import winston from 'winston';
import path from 'path';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define which level to log based on environment
const level = () => {
  const env = process.env.NODE_ENV || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'warn';
};

// Define different log formats
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`,
  ),
);

const fileLogFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
);

// Define transports
const transports = [
  // Console transport
  new winston.transports.Console({
    format: logFormat,
  }),
  
  // File transport for errors
  new winston.transports.File({
    filename: path.join(process.cwd(), 'logs', 'error.log'),
    level: 'error',
    format: fileLogFormat,
    maxsize: 5242880, // 5MB
    maxFiles: 5,
  }),
  
  // File transport for all logs
  new winston.transports.File({
    filename: path.join(process.cwd(), 'logs', 'combined.log'),
    format: fileLogFormat,
    maxsize: 5242880, // 5MB
    maxFiles: 5,
  }),
];

// Create the logger
export const logger = winston.createLogger({
  level: level(),
  levels,
  format: fileLogFormat,
  transports,
  exitOnError: false,
});

// Create a stream object with a 'write' function that will be used by morgan
export const morganStream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

// Helper functions for different log levels
export const logInfo = (message: string, meta?: any) => {
  logger.info(message, meta);
};

export const logError = (message: string, error?: Error | any) => {
  if (error instanceof Error) {
    logger.error(message, { error: error.message, stack: error.stack });
  } else {
    logger.error(message, { error });
  }
};

export const logWarn = (message: string, meta?: any) => {
  logger.warn(message, meta);
};

export const logDebug = (message: string, meta?: any) => {
  logger.debug(message, meta);
};

export const logHttp = (message: string, meta?: any) => {
  logger.http(message, meta);
};

// Agent-specific logging
export const logAgentActivity = (agentId: string, activity: string, meta?: any) => {
  logger.info(`[AGENT:${agentId}] ${activity}`, meta);
};

export const logAgentError = (agentId: string, error: string, errorDetails?: any) => {
  logger.error(`[AGENT:${agentId}] ${error}`, errorDetails);
};

export const logTaskExecution = (taskId: string, status: string, meta?: any) => {
  logger.info(`[TASK:${taskId}] ${status}`, meta);
};

export const logWorkflowExecution = (workflowId: string, status: string, meta?: any) => {
  logger.info(`[WORKFLOW:${workflowId}] ${status}`, meta);
};

// Performance logging
export const logPerformance = (operation: string, duration: number, meta?: any) => {
  logger.info(`[PERFORMANCE] ${operation} completed in ${duration}ms`, meta);
};

// Security logging
export const logSecurityEvent = (event: string, userId?: string, ip?: string, meta?: any) => {
  logger.warn(`[SECURITY] ${event}`, { userId, ip, ...meta });
};

// Database logging
export const logDatabaseOperation = (operation: string, table: string, duration?: number, meta?: any) => {
  logger.debug(`[DATABASE] ${operation} on ${table}${duration ? ` (${duration}ms)` : ''}`, meta);
};

// API logging
export const logApiRequest = (method: string, url: string, userId?: string, ip?: string, meta?: any) => {
  logger.http(`[API] ${method} ${url}`, { userId, ip, ...meta });
};

export const logApiResponse = (method: string, url: string, statusCode: number, duration: number, meta?: any) => {
  logger.http(`[API] ${method} ${url} - ${statusCode} (${duration}ms)`, meta);
};

// Logger class for component-specific logging
export class Logger {
  private component: string;

  constructor(component: string) {
    this.component = component;
  }

  private formatMessage(message: string, meta?: any): [string, any?] {
    const formattedMessage = `[${this.component}] ${message}`;
    return meta ? [formattedMessage, meta] : [formattedMessage];
  }

  info(message: string, meta?: any): void {
    const [msg, metadata] = this.formatMessage(message, meta);
    logger.info(msg, metadata);
  }

  error(message: string, error?: Error | any): void {
    const [msg, metadata] = this.formatMessage(message, error);
    logger.error(msg, metadata);
  }

  warn(message: string, meta?: any): void {
    const [msg, metadata] = this.formatMessage(message, meta);
    logger.warn(msg, metadata);
  }

  debug(message: string, meta?: any): void {
    const [msg, metadata] = this.formatMessage(message, meta);
    logger.debug(msg, metadata);
  }

  http(message: string, meta?: any): void {
    const [msg, metadata] = this.formatMessage(message, meta);
    logger.http(msg, metadata);
  }
}

export default logger;

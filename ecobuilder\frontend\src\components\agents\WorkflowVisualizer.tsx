import React, { useState } from 'react';
import { 
  PlayIcon, 
  PauseIcon, 
  StopIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  ArrowRightIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { WorkflowExecution, WorkflowExecutionStatus } from '../../../../shared/types';

interface WorkflowVisualizerProps {
  workflows: WorkflowExecution[];
  onWorkflowClick?: (workflow: WorkflowExecution) => void;
  onWorkflowAction?: (workflowId: string, action: 'start' | 'pause' | 'stop') => void;
}

const getStatusColor = (status: WorkflowExecutionStatus) => {
  switch (status) {
    case WorkflowExecutionStatus.RUNNING:
      return 'text-blue-600 bg-blue-100';
    case WorkflowExecutionStatus.COMPLETED:
      return 'text-green-600 bg-green-100';
    case WorkflowExecutionStatus.FAILED:
      return 'text-red-600 bg-red-100';
    case WorkflowExecutionStatus.CANCELLED:
      return 'text-gray-600 bg-gray-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

const getStatusIcon = (status: WorkflowExecutionStatus) => {
  switch (status) {
    case WorkflowExecutionStatus.RUNNING:
      return PlayIcon;
    case WorkflowExecutionStatus.COMPLETED:
      return CheckCircleIcon;
    case WorkflowExecutionStatus.FAILED:
      return ExclamationTriangleIcon;
    case WorkflowExecutionStatus.CANCELLED:
      return StopIcon;
    default:
      return ClockIcon;
  }
};

const mockWorkflowStages = [
  { id: '1', name: 'Partner Discovery', status: 'completed', agent: 'Partner Scout' },
  { id: '2', name: 'Partner Scoring', status: 'completed', agent: 'Partner Scout' },
  { id: '3', name: 'Initial Contact', status: 'running', agent: 'Partner Recruiter' },
  { id: '4', name: 'Qualification', status: 'pending', agent: 'Partner Recruiter' },
  { id: '5', name: 'Training Setup', status: 'pending', agent: 'Partner Trainer' },
];

export const WorkflowVisualizer: React.FC<WorkflowVisualizerProps> = ({ 
  workflows, 
  onWorkflowClick,
  onWorkflowAction 
}) => {
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null);

  const handleWorkflowSelect = (workflow: WorkflowExecution) => {
    setSelectedWorkflow(workflow.id);
    onWorkflowClick?.(workflow);
  };

  const handleAction = (workflowId: string, action: 'start' | 'pause' | 'stop') => {
    onWorkflowAction?.(workflowId, action);
  };

  if (workflows.length === 0) {
    return (
      <div className="text-center py-12">
        <ClockIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Workflows</h3>
        <p className="text-gray-500">Start a new workflow to see it here</p>
        <button className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
          Create Workflow
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Workflow List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {workflows.map((workflow) => {
          const StatusIcon = getStatusIcon(workflow.status);
          const statusColor = getStatusColor(workflow.status);
          const isSelected = selectedWorkflow === workflow.id;

          return (
            <div
              key={workflow.id}
              className={`border rounded-lg p-4 cursor-pointer transition-all ${
                isSelected 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300 bg-white'
              }`}
              onClick={() => handleWorkflowSelect(workflow)}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">⚡</div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-900">
                      Workflow #{workflow.id.slice(-6)}
                    </h3>
                    <p className="text-xs text-gray-500">
                      Started {new Date(workflow.startedAt).toLocaleString()}
                    </p>
                  </div>
                </div>
                <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColor}`}>
                  <StatusIcon className="w-3 h-3 mr-1" />
                  {workflow.status}
                </div>
              </div>

              {/* Progress Bar */}
              {workflow.status === WorkflowExecutionStatus.RUNNING && (
                <div className="mb-3">
                  <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                    <span>Progress</span>
                    <span>60%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: '60%' }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex items-center justify-between">
                <div className="flex space-x-2">
                  {workflow.status === WorkflowExecutionStatus.RUNNING && (
                    <>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAction(workflow.id, 'pause');
                        }}
                        className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-yellow-700 bg-yellow-100 hover:bg-yellow-200 transition-colors"
                      >
                        <PauseIcon className="w-3 h-3 mr-1" />
                        Pause
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAction(workflow.id, 'stop');
                        }}
                        className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors"
                      >
                        <StopIcon className="w-3 h-3 mr-1" />
                        Stop
                      </button>
                    </>
                  )}
                </div>
                <button
                  onClick={(e) => e.stopPropagation()}
                  className="inline-flex items-center px-2 py-1 text-xs text-gray-500 hover:text-gray-700"
                >
                  <EyeIcon className="w-3 h-3 mr-1" />
                  Details
                </button>
              </div>

              {/* Error Message */}
              {workflow.status === WorkflowExecutionStatus.FAILED && workflow.error && (
                <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                  <div className="flex items-center">
                    <ExclamationTriangleIcon className="w-3 h-3 mr-1" />
                    <span className="font-medium">Error:</span>
                  </div>
                  <p className="mt-1 truncate">{workflow.error}</p>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Detailed View */}
      {selectedWorkflow && (
        <div className="border-t pt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Workflow Stages
          </h3>
          
          {/* Stage Flow */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center space-x-4 overflow-x-auto pb-4">
              {mockWorkflowStages.map((stage, index) => (
                <React.Fragment key={stage.id}>
                  <div className="flex-shrink-0">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-medium text-sm ${
                      stage.status === 'completed' ? 'bg-green-500' :
                      stage.status === 'running' ? 'bg-blue-500 animate-pulse' :
                      'bg-gray-300'
                    }`}>
                      {stage.status === 'completed' ? (
                        <CheckCircleIcon className="w-6 h-6" />
                      ) : stage.status === 'running' ? (
                        <PlayIcon className="w-6 h-6" />
                      ) : (
                        <ClockIcon className="w-6 h-6" />
                      )}
                    </div>
                    <div className="mt-2 text-center">
                      <p className="text-xs font-medium text-gray-900">{stage.name}</p>
                      <p className="text-xs text-gray-500">{stage.agent}</p>
                    </div>
                  </div>
                  
                  {index < mockWorkflowStages.length - 1 && (
                    <ArrowRightIcon className="w-5 h-5 text-gray-400 flex-shrink-0" />
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>

          {/* Stage Details */}
          <div className="mt-4 space-y-3">
            {mockWorkflowStages.map((stage) => (
              <div
                key={stage.id}
                className={`border rounded-lg p-3 ${
                  stage.status === 'running' ? 'border-blue-200 bg-blue-50' :
                  stage.status === 'completed' ? 'border-green-200 bg-green-50' :
                  'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                      stage.status === 'completed' ? 'bg-green-500 text-white' :
                      stage.status === 'running' ? 'bg-blue-500 text-white' :
                      'bg-gray-300 text-gray-600'
                    }`}>
                      {stage.status === 'completed' ? (
                        <CheckCircleIcon className="w-4 h-4" />
                      ) : stage.status === 'running' ? (
                        <PlayIcon className="w-4 h-4" />
                      ) : (
                        <ClockIcon className="w-4 h-4" />
                      )}
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">{stage.name}</h4>
                      <p className="text-xs text-gray-500">Assigned to {stage.agent}</p>
                    </div>
                  </div>
                  <div className={`px-2 py-1 rounded text-xs font-medium ${
                    stage.status === 'completed' ? 'bg-green-100 text-green-800' :
                    stage.status === 'running' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {stage.status.charAt(0).toUpperCase() + stage.status.slice(1)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

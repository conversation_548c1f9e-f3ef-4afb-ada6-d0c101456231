import { 
  Agent, 
  AgentType, 
  AgentStatus, 
  Task, 
  TaskResult, 
  Message, 
  AgentResponse, 
  Tool,
  BaseAgent as IBaseAgent
} from '../../../shared/types';
import { Logger } from '../utils/logger';
import { EventEmitter } from 'events';

export abstract class BaseAgent extends EventEmitter implements IBaseAgent {
  protected logger: Logger;
  protected tools: Map<string, Tool> = new Map();
  protected isInitialized = false;

  constructor(
    public readonly id: string,
    public readonly name: string,
    public readonly type: AgentType,
    public status: AgentStatus = AgentStatus.IDLE,
    public capabilities: string[] = [],
    public config: Record<string, any> = {}
  ) {
    super();
    this.logger = new Logger(`Agent:${this.name}`);
  }

  /**
   * Initialize the agent with tools and configuration
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing agent', { id: this.id, type: this.type });
      
      await this.loadTools();
      await this.validateConfiguration();
      await this.setupEventHandlers();
      
      this.isInitialized = true;
      this.status = AgentStatus.IDLE;
      
      this.emit('initialized', { agentId: this.id });
      this.logger.info('Agent initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize agent', error);
      this.status = AgentStatus.ERROR;
      throw error;
    }
  }

  /**
   * Execute a task
   */
  async execute(task: Task): Promise<TaskResult> {
    if (!this.isInitialized) {
      throw new Error('Agent not initialized');
    }

    if (!this.validateTask(task)) {
      throw new Error(`Task ${task.id} is not valid for agent ${this.name}`);
    }

    this.logger.info('Executing task', { taskId: task.id, type: task.type });
    this.status = AgentStatus.WORKING;
    
    try {
      this.emit('taskStarted', { agentId: this.id, taskId: task.id });
      
      const result = await this.executeTask(task);
      
      this.status = AgentStatus.IDLE;
      this.emit('taskCompleted', { agentId: this.id, taskId: task.id, result });
      
      this.logger.info('Task completed successfully', { taskId: task.id });
      return result;
    } catch (error) {
      this.status = AgentStatus.ERROR;
      this.emit('taskFailed', { agentId: this.id, taskId: task.id, error });
      
      this.logger.error('Task execution failed', { taskId: task.id, error });
      await this.handleError(error, task);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: { taskId: task.id, agentId: this.id }
      };
    }
  }

  /**
   * Handle inter-agent communication
   */
  async communicate(message: Message): Promise<AgentResponse> {
    this.logger.info('Received message', { messageId: message.id, type: message.type });
    
    try {
      const response = await this.processMessage(message);
      this.emit('messageProcessed', { agentId: this.id, messageId: message.id, response });
      return response;
    } catch (error) {
      this.logger.error('Failed to process message', { messageId: message.id, error });
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to process message'
      };
    }
  }

  /**
   * Get current agent status
   */
  getStatus(): AgentStatus {
    return this.status;
  }

  /**
   * Get agent capabilities
   */
  getCapabilities(): string[] {
    return [...this.capabilities];
  }

  /**
   * Validate if the agent can handle a specific task
   */
  validateTask(task: Task): boolean {
    // Check if task type is supported
    const supportedTaskTypes = this.getSupportedTaskTypes();
    if (!supportedTaskTypes.includes(task.type)) {
      this.logger.warn('Unsupported task type', { taskType: task.type, supported: supportedTaskTypes });
      return false;
    }

    // Check if agent has required capabilities
    const requiredCapabilities = this.getRequiredCapabilities(task);
    const hasCapabilities = requiredCapabilities.every(cap => this.capabilities.includes(cap));
    
    if (!hasCapabilities) {
      this.logger.warn('Missing required capabilities', { 
        required: requiredCapabilities, 
        available: this.capabilities 
      });
      return false;
    }

    return true;
  }

  /**
   * Handle errors during task execution
   */
  async handleError(error: Error, task?: Task): Promise<void> {
    this.logger.error('Handling error', { error: error.message, taskId: task?.id });
    
    // Emit error event for monitoring
    this.emit('error', { agentId: this.id, error, taskId: task?.id });
    
    // Attempt recovery if possible
    if (this.canRecover(error)) {
      await this.recover(error, task);
    } else {
      this.status = AgentStatus.ERROR;
    }
  }

  /**
   * Add a tool to the agent
   */
  addTool(tool: Tool): void {
    this.tools.set(tool.id, tool);
    this.logger.info('Tool added', { toolId: tool.id, toolName: tool.name });
  }

  /**
   * Remove a tool from the agent
   */
  removeTool(toolId: string): void {
    this.tools.delete(toolId);
    this.logger.info('Tool removed', { toolId });
  }

  /**
   * Get a tool by ID
   */
  getTool(toolId: string): Tool | undefined {
    return this.tools.get(toolId);
  }

  /**
   * Shutdown the agent gracefully
   */
  async shutdown(): Promise<void> {
    this.logger.info('Shutting down agent');
    this.status = AgentStatus.OFFLINE;
    this.removeAllListeners();
    await this.cleanup();
  }

  // Abstract methods to be implemented by specific agents
  protected abstract executeTask(task: Task): Promise<TaskResult>;
  protected abstract processMessage(message: Message): Promise<AgentResponse>;
  protected abstract getSupportedTaskTypes(): string[];
  protected abstract getRequiredCapabilities(task: Task): string[];
  protected abstract loadTools(): Promise<void>;
  protected abstract validateConfiguration(): Promise<void>;
  protected abstract setupEventHandlers(): Promise<void>;
  protected abstract canRecover(error: Error): boolean;
  protected abstract recover(error: Error, task?: Task): Promise<void>;
  protected abstract cleanup(): Promise<void>;
}

/**
 * Agent factory for creating specific agent instances
 */
export class AgentFactory {
  private static agents: Map<AgentType, typeof BaseAgent> = new Map();

  static register(type: AgentType, agentClass: typeof BaseAgent): void {
    this.agents.set(type, agentClass);
  }

  static create(
    id: string,
    name: string,
    type: AgentType,
    capabilities: string[] = [],
    config: Record<string, any> = {}
  ): BaseAgent {
    const AgentClass = this.agents.get(type);
    if (!AgentClass) {
      throw new Error(`No agent class registered for type: ${type}`);
    }

    return new AgentClass(id, name, type, AgentStatus.IDLE, capabilities, config);
  }

  static getSupportedTypes(): AgentType[] {
    return Array.from(this.agents.keys());
  }
}

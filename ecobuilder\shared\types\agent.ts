// Shared types for EcoBuilder Multi-Agent System

export interface Agent {
  id: string;
  name: string;
  type: AgentType;
  status: AgentStatus;
  capabilities: string[];
  tools: Tool[];
  config?: AgentConfig;
  isActive: boolean;
  lastActiveAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface AgentConfig {
  maxConcurrentTasks: number;
  retryAttempts: number;
  timeoutMs: number;
  apiKeys?: Record<string, string>;
  preferences?: Record<string, any>;
}

export interface Tool {
  id: string;
  name: string;
  description: string;
  type: ToolType;
  config: ToolConfig;
  isEnabled: boolean;
}

export interface ToolConfig {
  endpoint?: string;
  apiKey?: string;
  parameters?: Record<string, any>;
  rateLimit?: {
    requests: number;
    windowMs: number;
  };
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  type: TaskType;
  status: TaskStatus;
  priority: Priority;
  data?: Record<string, any>;
  result?: Record<string, any>;
  error?: string;
  progress: number;
  maxRetries: number;
  retryCount: number;
  scheduledAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
  agentId: string;
  assignedToId?: string;
  partnerId?: string;
  opportunityId?: string;
  parentTaskId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TaskResult {
  success: boolean;
  data?: Record<string, any>;
  error?: string;
  metadata?: Record<string, any>;
}

export interface Message {
  id: string;
  type: MessageType;
  content: string;
  metadata?: Record<string, any>;
  fromId?: string;
  agentId?: string;
  isRead: boolean;
  createdAt: Date;
}

export interface AgentResponse {
  success: boolean;
  message?: string;
  data?: Record<string, any>;
  actions?: AgentAction[];
}

export interface AgentAction {
  type: ActionType;
  target: string;
  payload: Record<string, any>;
  scheduledAt?: Date;
}

// Enums
export enum AgentType {
  PARTNER_SCOUT = 'PARTNER_SCOUT',
  PARTNER_RECRUITER = 'PARTNER_RECRUITER',
  PARTNER_TRAINER = 'PARTNER_TRAINER',
  OPPORTUNITY_MANAGER = 'OPPORTUNITY_MANAGER',
  MARKET_EXPANDER = 'MARKET_EXPANDER'
}

export enum AgentStatus {
  IDLE = 'IDLE',
  WORKING = 'WORKING',
  ERROR = 'ERROR',
  OFFLINE = 'OFFLINE',
  MAINTENANCE = 'MAINTENANCE'
}

export enum ToolType {
  API = 'API',
  DATABASE = 'DATABASE',
  EMAIL = 'EMAIL',
  CALENDAR = 'CALENDAR',
  CRM = 'CRM',
  AI_MODEL = 'AI_MODEL',
  FILE_STORAGE = 'FILE_STORAGE',
  NOTIFICATION = 'NOTIFICATION'
}

export enum TaskType {
  FIND_PARTNERS = 'FIND_PARTNERS',
  SCORE_PARTNER = 'SCORE_PARTNER',
  SEND_EMAIL = 'SEND_EMAIL',
  SCHEDULE_MEETING = 'SCHEDULE_MEETING',
  CREATE_PROPOSAL = 'CREATE_PROPOSAL',
  ANALYZE_OPPORTUNITY = 'ANALYZE_OPPORTUNITY',
  GENERATE_REPORT = 'GENERATE_REPORT',
  TRAIN_PARTNER = 'TRAIN_PARTNER',
  MARKET_ANALYSIS = 'MARKET_ANALYSIS'
}

export enum TaskStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  RETRYING = 'RETRYING'
}

export enum Priority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export enum MessageType {
  SYSTEM = 'SYSTEM',
  USER = 'USER',
  AGENT = 'AGENT',
  NOTIFICATION = 'NOTIFICATION',
  ERROR = 'ERROR'
}

export enum ActionType {
  CREATE_TASK = 'CREATE_TASK',
  UPDATE_TASK = 'UPDATE_TASK',
  SEND_MESSAGE = 'SEND_MESSAGE',
  SCHEDULE_ACTIVITY = 'SCHEDULE_ACTIVITY',
  UPDATE_PARTNER = 'UPDATE_PARTNER',
  CREATE_OPPORTUNITY = 'CREATE_OPPORTUNITY',
  GENERATE_REPORT = 'GENERATE_REPORT'
}

// Base Agent Interface
export interface BaseAgent {
  execute(task: Task): Promise<TaskResult>;
  communicate(message: Message): Promise<AgentResponse>;
  getStatus(): AgentStatus;
  getCapabilities(): string[];
  validateTask(task: Task): boolean;
  handleError(error: Error, task?: Task): Promise<void>;
}

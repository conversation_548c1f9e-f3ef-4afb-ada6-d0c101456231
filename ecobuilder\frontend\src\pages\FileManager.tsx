import React, { useState } from 'react';
import { 
  DocumentIcon, 
  FolderIcon, 
  TrashIcon,
  EyeIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';
import { FileUpload, UploadedFile } from '../components/common/FileUpload';
import toast from 'react-hot-toast';

interface FileManagerState {
  files: UploadedFile[];
  selectedFiles: string[];
  viewMode: 'grid' | 'list';
}

export const FileManager: React.FC = () => {
  const [state, setState] = useState<FileManagerState>({
    files: [],
    selectedFiles: [],
    viewMode: 'list'
  });

  const handleUploadSuccess = (uploadedFiles: UploadedFile[]) => {
    setState(prev => ({
      ...prev,
      files: [...prev.files, ...uploadedFiles]
    }));
    
    // 显示成功消息
    toast.success(`成功上传 ${uploadedFiles.length} 个文件！`, {
      duration: 4000,
      icon: '✅'
    });
  };

  const handleUploadError = (error: string) => {
    // 显示错误消息
    toast.error(`上传失败：${error}`, {
      duration: 5000,
      icon: '❌'
    });
  };

  const handleFileSelect = (fileId: string) => {
    setState(prev => ({
      ...prev,
      selectedFiles: prev.selectedFiles.includes(fileId)
        ? prev.selectedFiles.filter(id => id !== fileId)
        : [...prev.selectedFiles, fileId]
    }));
  };

  const handleDeleteSelected = () => {
    if (state.selectedFiles.length === 0) return;

    const confirmDelete = window.confirm(
      `确定要删除选中的 ${state.selectedFiles.length} 个文件吗？`
    );

    if (confirmDelete) {
      setState(prev => ({
        ...prev,
        files: prev.files.filter(file => !prev.selectedFiles.includes(file.id)),
        selectedFiles: []
      }));
      
      toast.success('文件删除成功');
    }
  };

  const handleDownloadFile = (file: UploadedFile) => {
    // 创建下载链接
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success(`开始下载 ${file.name}`);
  };

  const handleViewFile = (file: UploadedFile) => {
    // 在新窗口中打开文件
    window.open(file.url, '_blank');
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('image')) return '🖼️';
    if (fileType.includes('pdf')) return '📄';
    if (fileType.includes('word') || fileType.includes('document')) return '📝';
    if (fileType.includes('excel') || fileType.includes('spreadsheet')) return '📊';
    if (fileType.includes('text')) return '📃';
    return '📁';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">文件管理</h1>
              <p className="mt-1 text-sm text-gray-500">
                上传和管理您的文件
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">
                共 {state.files.length} 个文件
              </span>
              {state.selectedFiles.length > 0 && (
                <button
                  onClick={handleDeleteSelected}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 transition-colors"
                >
                  <TrashIcon className="h-4 w-4 mr-2" />
                  删除选中 ({state.selectedFiles.length})
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Upload Area */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">上传文件</h2>
              <FileUpload
                onUploadSuccess={handleUploadSuccess}
                onUploadError={handleUploadError}
                acceptedFileTypes={['.pdf', '.doc', '.docx', '.csv', '.xlsx', '.txt', '.png', '.jpg', '.jpeg']}
                maxFileSize={20 * 1024 * 1024} // 20MB
                maxFiles={10}
                multiple={true}
              />
            </div>
          </div>

          {/* Upload Statistics */}
          <div>
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">统计信息</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">总文件数</span>
                  <span className="text-sm font-medium text-gray-900">{state.files.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">总大小</span>
                  <span className="text-sm font-medium text-gray-900">
                    {formatFileSize(state.files.reduce((total, file) => total + file.size, 0))}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">已选择</span>
                  <span className="text-sm font-medium text-gray-900">{state.selectedFiles.length}</span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="mt-6 bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">快速操作</h3>
              <div className="space-y-2">
                <button
                  onClick={() => setState(prev => ({ ...prev, selectedFiles: prev.files.map(f => f.id) }))}
                  className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
                >
                  全选
                </button>
                <button
                  onClick={() => setState(prev => ({ ...prev, selectedFiles: [] }))}
                  className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
                >
                  取消全选
                </button>
                <button
                  onClick={() => setState(prev => ({ ...prev, viewMode: prev.viewMode === 'list' ? 'grid' : 'list' }))}
                  className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
                >
                  切换视图
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* File List */}
        {state.files.length > 0 && (
          <div className="mt-8 bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">已上传文件</h2>
            </div>
            
            {state.viewMode === 'list' ? (
              <div className="overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input
                          type="checkbox"
                          checked={state.selectedFiles.length === state.files.length && state.files.length > 0}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setState(prev => ({ ...prev, selectedFiles: prev.files.map(f => f.id) }));
                            } else {
                              setState(prev => ({ ...prev, selectedFiles: [] }));
                            }
                          }}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        文件名
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        大小
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        上传时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {state.files.map((file) => (
                      <tr key={file.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="checkbox"
                            checked={state.selectedFiles.includes(file.id)}
                            onChange={() => handleFileSelect(file.id)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <span className="text-lg mr-3">{getFileIcon(file.type)}</span>
                            <div>
                              <div className="text-sm font-medium text-gray-900">{file.name}</div>
                              <div className="text-sm text-gray-500">{file.type}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatFileSize(file.size)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {file.uploadedAt.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleViewFile(file)}
                              className="text-blue-600 hover:text-blue-900"
                              title="查看文件"
                            >
                              <EyeIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDownloadFile(file)}
                              className="text-green-600 hover:text-green-900"
                              title="下载文件"
                            >
                              <ArrowDownTrayIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => {
                                setState(prev => ({
                                  ...prev,
                                  files: prev.files.filter(f => f.id !== file.id)
                                }));
                                toast.success('文件删除成功');
                              }}
                              className="text-red-600 hover:text-red-900"
                              title="删除文件"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="p-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {state.files.map((file) => (
                  <div
                    key={file.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      state.selectedFiles.includes(file.id) 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleFileSelect(file.id)}
                  >
                    <div className="text-center">
                      <div className="text-4xl mb-2">{getFileIcon(file.type)}</div>
                      <h3 className="text-sm font-medium text-gray-900 truncate">{file.name}</h3>
                      <p className="text-xs text-gray-500 mt-1">{formatFileSize(file.size)}</p>
                      <p className="text-xs text-gray-500">{file.uploadedAt.toLocaleDateString()}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Empty State */}
        {state.files.length === 0 && (
          <div className="mt-8 text-center py-12">
            <FolderIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无文件</h3>
            <p className="text-gray-500">上传您的第一个文件开始使用</p>
          </div>
        )}
      </div>
    </div>
  );
};

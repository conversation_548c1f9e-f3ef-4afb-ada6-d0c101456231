import { EventEmitter } from 'events';
import { Task, TaskStatus, Priority, Agent } from '../../../shared/types';
import { Logger } from '../utils/logger';
import { DatabaseService } from './DatabaseService';
import { BaseAgent } from '../agents/BaseAgent';

interface TaskQueue {
  urgent: Task[];
  high: Task[];
  medium: Task[];
  low: Task[];
}

interface SchedulerConfig {
  maxConcurrentTasks: number;
  retryDelayMs: number;
  maxRetryDelayMs: number;
  taskTimeoutMs: number;
  queueCheckIntervalMs: number;
}

export class TaskScheduler extends EventEmitter {
  private logger: Logger;
  private dbService: DatabaseService;
  private config: SchedulerConfig;
  private taskQueue: TaskQueue;
  private runningTasks: Map<string, Task> = new Map();
  private agents: Map<string, BaseAgent> = new Map();
  private isRunning = false;
  private schedulerInterval?: NodeJS.Timeout;

  constructor(config: Partial<SchedulerConfig> = {}) {
    super();
    this.logger = new Logger('TaskScheduler');
    this.dbService = new DatabaseService();
    
    this.config = {
      maxConcurrentTasks: 10,
      retryDelayMs: 5000,
      maxRetryDelayMs: 300000, // 5 minutes
      taskTimeoutMs: 600000, // 10 minutes
      queueCheckIntervalMs: 1000,
      ...config
    };

    this.taskQueue = {
      urgent: [],
      high: [],
      medium: [],
      low: []
    };
  }

  /**
   * Initialize the task scheduler
   */
  async initialize(): Promise<void> {
    this.logger.info('Initializing task scheduler', this.config);
    
    try {
      // Load pending tasks from database
      await this.loadPendingTasks();
      
      // Start the scheduler
      this.start();
      
      this.logger.info('Task scheduler initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize task scheduler', error);
      throw error;
    }
  }

  /**
   * Start the task scheduler
   */
  start(): void {
    if (this.isRunning) {
      this.logger.warn('Task scheduler is already running');
      return;
    }

    this.isRunning = true;
    this.schedulerInterval = setInterval(
      () => this.processTaskQueue(),
      this.config.queueCheckIntervalMs
    );
    
    this.logger.info('Task scheduler started');
  }

  /**
   * Stop the task scheduler
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    if (this.schedulerInterval) {
      clearInterval(this.schedulerInterval);
      this.schedulerInterval = undefined;
    }
    
    this.logger.info('Task scheduler stopped');
  }

  /**
   * Schedule a new task
   */
  async scheduleTask(task: Task): Promise<void> {
    this.logger.info('Scheduling task', { taskId: task.id, type: task.type, priority: task.priority });
    
    // Update task status to pending
    await this.dbService.updateTask(task.id, { status: TaskStatus.PENDING });
    
    // Add to appropriate queue based on priority
    this.addToQueue(task);
    
    this.emit('taskScheduled', { taskId: task.id, priority: task.priority });
  }

  /**
   * Cancel a scheduled task
   */
  async cancelTask(taskId: string): Promise<void> {
    this.logger.info('Cancelling task', { taskId });
    
    // Remove from queue
    this.removeFromQueue(taskId);
    
    // Update task status
    await this.dbService.updateTask(taskId, { 
      status: TaskStatus.CANCELLED,
      completedAt: new Date()
    });
    
    this.emit('taskCancelled', { taskId });
  }

  /**
   * Retry a failed task
   */
  async retryTask(taskId: string): Promise<void> {
    const task = await this.dbService.getTask(taskId);
    if (!task) {
      throw new Error(`Task not found: ${taskId}`);
    }

    if (task.retryCount >= task.maxRetries) {
      throw new Error(`Task has exceeded maximum retries: ${taskId}`);
    }

    this.logger.info('Retrying task', { taskId, retryCount: task.retryCount });
    
    // Calculate retry delay with exponential backoff
    const delay = Math.min(
      this.config.retryDelayMs * Math.pow(2, task.retryCount),
      this.config.maxRetryDelayMs
    );
    
    // Schedule retry
    setTimeout(async () => {
      const updatedTask = await this.dbService.updateTask(taskId, {
        status: TaskStatus.PENDING,
        retryCount: task.retryCount + 1,
        error: undefined
      });
      
      this.addToQueue(updatedTask);
    }, delay);
  }

  /**
   * Register an agent with the scheduler
   */
  registerAgent(agent: BaseAgent): void {
    this.agents.set(agent.id, agent);
    this.logger.info('Agent registered with scheduler', { agentId: agent.id, type: agent.type });
  }

  /**
   * Unregister an agent
   */
  unregisterAgent(agentId: string): void {
    this.agents.delete(agentId);
    this.logger.info('Agent unregistered from scheduler', { agentId });
  }

  /**
   * Get scheduler statistics
   */
  getStats(): any {
    const queueSizes = {
      urgent: this.taskQueue.urgent.length,
      high: this.taskQueue.high.length,
      medium: this.taskQueue.medium.length,
      low: this.taskQueue.low.length
    };
    
    return {
      isRunning: this.isRunning,
      runningTasks: this.runningTasks.size,
      queueSizes,
      totalQueued: Object.values(queueSizes).reduce((sum, size) => sum + size, 0),
      registeredAgents: this.agents.size,
      config: this.config
    };
  }

  /**
   * Process the task queue
   */
  private async processTaskQueue(): Promise<void> {
    if (this.runningTasks.size >= this.config.maxConcurrentTasks) {
      return; // At capacity
    }

    // Get next task from queue (priority order)
    const task = this.getNextTask();
    if (!task) {
      return; // No tasks to process
    }

    // Find available agent for the task
    const agent = this.findAvailableAgent(task);
    if (!agent) {
      // No available agent, put task back in queue
      this.addToQueue(task);
      return;
    }

    // Execute the task
    await this.executeTask(task, agent);
  }

  /**
   * Get the next task from the queue (priority order)
   */
  private getNextTask(): Task | undefined {
    // Check queues in priority order
    if (this.taskQueue.urgent.length > 0) {
      return this.taskQueue.urgent.shift();
    }
    if (this.taskQueue.high.length > 0) {
      return this.taskQueue.high.shift();
    }
    if (this.taskQueue.medium.length > 0) {
      return this.taskQueue.medium.shift();
    }
    if (this.taskQueue.low.length > 0) {
      return this.taskQueue.low.shift();
    }
    
    return undefined;
  }

  /**
   * Find an available agent for a task
   */
  private findAvailableAgent(task: Task): BaseAgent | undefined {
    for (const agent of this.agents.values()) {
      if (agent.getStatus() === 'IDLE' && agent.validateTask(task)) {
        return agent;
      }
    }
    return undefined;
  }

  /**
   * Execute a task with an agent
   */
  private async executeTask(task: Task, agent: BaseAgent): Promise<void> {
    this.logger.info('Executing task', { taskId: task.id, agentId: agent.id });
    
    // Mark task as running
    this.runningTasks.set(task.id, task);
    await this.dbService.updateTask(task.id, {
      status: TaskStatus.RUNNING,
      startedAt: new Date()
    });
    
    this.emit('taskStarted', { taskId: task.id, agentId: agent.id });
    
    // Set timeout for task execution
    const timeoutId = setTimeout(() => {
      this.handleTaskTimeout(task.id);
    }, this.config.taskTimeoutMs);
    
    try {
      // Execute the task
      const result = await agent.execute(task);
      
      // Clear timeout
      clearTimeout(timeoutId);
      
      // Handle task completion
      await this.handleTaskCompletion(task, result);
      
    } catch (error) {
      // Clear timeout
      clearTimeout(timeoutId);
      
      // Handle task failure
      await this.handleTaskFailure(task, error);
    } finally {
      // Remove from running tasks
      this.runningTasks.delete(task.id);
    }
  }

  /**
   * Handle task completion
   */
  private async handleTaskCompletion(task: Task, result: any): Promise<void> {
    this.logger.info('Task completed', { taskId: task.id, success: result.success });
    
    await this.dbService.updateTask(task.id, {
      status: TaskStatus.COMPLETED,
      result: result.data,
      completedAt: new Date(),
      progress: 100
    });
    
    this.emit('taskCompleted', { taskId: task.id, result });
  }

  /**
   * Handle task failure
   */
  private async handleTaskFailure(task: Task, error: any): Promise<void> {
    this.logger.error('Task failed', { taskId: task.id, error });
    
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    // Check if task should be retried
    if (task.retryCount < task.maxRetries) {
      await this.dbService.updateTask(task.id, {
        status: TaskStatus.FAILED,
        error: errorMessage,
        completedAt: new Date()
      });
      
      // Schedule retry
      await this.retryTask(task.id);
      
      this.emit('taskRetrying', { taskId: task.id, retryCount: task.retryCount + 1 });
    } else {
      // Mark as permanently failed
      await this.dbService.updateTask(task.id, {
        status: TaskStatus.FAILED,
        error: errorMessage,
        completedAt: new Date()
      });
      
      this.emit('taskFailed', { taskId: task.id, error: errorMessage });
    }
  }

  /**
   * Handle task timeout
   */
  private async handleTaskTimeout(taskId: string): Promise<void> {
    this.logger.warn('Task timed out', { taskId });
    
    const task = this.runningTasks.get(taskId);
    if (task) {
      await this.handleTaskFailure(task, new Error('Task execution timed out'));
    }
  }

  /**
   * Add task to appropriate queue
   */
  private addToQueue(task: Task): void {
    switch (task.priority) {
      case Priority.URGENT:
        this.taskQueue.urgent.push(task);
        break;
      case Priority.HIGH:
        this.taskQueue.high.push(task);
        break;
      case Priority.MEDIUM:
        this.taskQueue.medium.push(task);
        break;
      case Priority.LOW:
        this.taskQueue.low.push(task);
        break;
      default:
        this.taskQueue.medium.push(task);
    }
  }

  /**
   * Remove task from queue
   */
  private removeFromQueue(taskId: string): void {
    const queues = [
      this.taskQueue.urgent,
      this.taskQueue.high,
      this.taskQueue.medium,
      this.taskQueue.low
    ];
    
    for (const queue of queues) {
      const index = queue.findIndex(task => task.id === taskId);
      if (index !== -1) {
        queue.splice(index, 1);
        break;
      }
    }
  }

  /**
   * Load pending tasks from database
   */
  private async loadPendingTasks(): Promise<void> {
    this.logger.info('Loading pending tasks from database');
    
    const pendingTasks = await this.dbService.getPendingTasks();
    
    for (const task of pendingTasks) {
      this.addToQueue(task);
    }
    
    this.logger.info(`Loaded ${pendingTasks.length} pending tasks`);
  }

  /**
   * Shutdown the task scheduler
   */
  async shutdown(): Promise<void> {
    this.logger.info('Shutting down task scheduler');
    
    // Stop the scheduler
    this.stop();
    
    // Wait for running tasks to complete (with timeout)
    const shutdownTimeout = 30000; // 30 seconds
    const startTime = Date.now();
    
    while (this.runningTasks.size > 0 && (Date.now() - startTime) < shutdownTimeout) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    if (this.runningTasks.size > 0) {
      this.logger.warn(`Forcing shutdown with ${this.runningTasks.size} tasks still running`);
    }
    
    // Clear all queues
    this.taskQueue.urgent = [];
    this.taskQueue.high = [];
    this.taskQueue.medium = [];
    this.taskQueue.low = [];
    
    // Clear agents
    this.agents.clear();
    
    this.removeAllListeners();
    
    this.logger.info('Task scheduler shutdown complete');
  }
}

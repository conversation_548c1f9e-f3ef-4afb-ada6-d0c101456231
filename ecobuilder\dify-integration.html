<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI生态专家 × Dify智能分析平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --warning-color: #f97316;
            --success-color: #22c55e;
            --dify-color: #6366f1;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--gray-800);
            line-height: 1.6;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px 32px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .logo {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .dify-logo {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--dify-color), #8b5cf6);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
        }

        .title-section h1 {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 4px;
        }

        .title-section p {
            color: var(--gray-600);
            font-size: 14px;
        }

        .status-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .status-online {
            background: rgba(34, 197, 94, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        .status-pulse {
            width: 8px;
            height: 8px;
            background: var(--success-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Main Grid Layout */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 24px;
            margin-bottom: 24px;
        }

        /* Workflow Panel */
        .workflow-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--gray-200);
        }

        .panel-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--gray-900);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .panel-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        /* Modern Input Form */
        .input-form {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            margin-bottom: 24px;
        }

        .form-section {
            margin-bottom: 24px;
        }

        .form-section:last-child {
            margin-bottom: 0;
        }

        .user-info-single {
            margin-bottom: 16px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 6px;
            font-size: 13px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 10px 12px;
            border: 1px solid var(--gray-300);
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: var(--gray-50);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
            font-family: inherit;
        }

        /* File Upload */
        .file-upload-area {
            border: 2px dashed var(--gray-300);
            border-radius: 12px;
            padding: 32px;
            text-align: center;
            background: var(--gray-50);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .file-upload-area:hover {
            border-color: var(--primary-color);
            background: rgba(37, 99, 235, 0.05);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15);
        }

        .file-upload-area.dragover {
            border-color: var(--primary-color);
            background: rgba(37, 99, 235, 0.1);
            transform: scale(1.02);
            box-shadow: 0 12px 30px rgba(37, 99, 235, 0.2);
        }

        .file-upload-area.upload-loading {
            border-color: var(--primary-color);
            background: rgba(37, 99, 235, 0.05);
            animation: uploadPulse 2s ease-in-out infinite;
        }

        .file-upload-area.upload-success {
            border-color: #16a34a;
            background: rgba(22, 163, 74, 0.05);
            animation: successBounce 0.6s ease-out;
        }

        .file-upload-area.upload-error {
            border-color: #dc2626;
            background: rgba(220, 38, 38, 0.05);
            animation: errorShake 0.5s ease-in-out;
        }

        .upload-status {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 16px;
            width: 100%;
        }

        .upload-status-icon {
            font-size: 48px;
            transition: all 0.3s ease;
        }

        .upload-loading .upload-status-icon {
            color: var(--primary-color);
            animation: spin 2s linear infinite;
        }

        .upload-success .upload-status-icon {
            color: #16a34a;
            animation: successPulse 0.8s ease-out;
        }

        .upload-error .upload-status-icon {
            color: #dc2626;
            animation: errorPulse 0.5s ease-out;
        }

        .upload-status-text {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 8px;
        }

        .upload-status-subtext {
            font-size: 14px;
            text-align: center;
            opacity: 0.8;
            margin-top: 4px;
        }

        .upload-loading .upload-status-text {
            color: var(--primary-color);
        }

        .upload-success .upload-status-text {
            color: #16a34a;
        }

        .upload-error .upload-status-text {
            color: #dc2626;
        }

        .upload-loading .upload-status-subtext {
            color: var(--primary-color);
        }

        .upload-success .upload-status-subtext {
            color: #16a34a;
        }

        .upload-error .upload-status-subtext {
            color: #dc2626;
        }

        /* Upload Action Buttons */
        .upload-actions {
            display: flex;
            gap: 12px;
            margin-top: 16px;
        }

        .upload-action-btn {
            padding: 8px 16px;
            border: 1px solid;
            border-radius: 6px;
            background: transparent;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .upload-action-btn.retry {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .upload-action-btn.retry:hover {
            background: var(--primary-color);
            color: white;
        }

        .upload-action-btn.cancel {
            border-color: var(--gray-400);
            color: var(--gray-600);
        }

        .upload-action-btn.cancel:hover {
            background: var(--gray-100);
        }

        /* File Size Display */
        .file-size-display {
            font-size: 12px;
            color: var(--gray-500);
            margin-top: 4px;
        }

        /* Upload Animation Enhancements */
        .upload-status-icon.uploading {
            animation: uploadSpin 1.5s linear infinite;
        }

        @keyframes uploadSpin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Toast Notification System */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .toast {
            background: white;
            border-radius: 8px;
            padding: 16px 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-left: 4px solid;
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 300px;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
            position: relative;
        }

        .toast.success {
            border-left-color: var(--success-color);
        }

        .toast.error {
            border-left-color: var(--danger-color);
        }

        .toast.info {
            border-left-color: var(--primary-color);
        }

        .toast-icon {
            font-size: 18px;
            flex-shrink: 0;
        }

        .toast.success .toast-icon {
            color: var(--success-color);
        }

        .toast.error .toast-icon {
            color: var(--danger-color);
        }

        .toast.info .toast-icon {
            color: var(--primary-color);
        }

        .toast-content {
            flex: 1;
        }

        .toast-title {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 2px;
        }

        .toast-message {
            font-size: 13px;
            color: var(--gray-600);
            line-height: 1.4;
        }

        .toast-close {
            background: none;
            border: none;
            color: var(--gray-400);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: color 0.2s;
        }

        .toast-close:hover {
            color: var(--gray-600);
        }

        .toast.removing {
            animation: slideOut 0.3s ease-in forwards;
        }

        .file-upload-icon {
            font-size: 48px;
            color: var(--gray-400);
            margin-bottom: 16px;
        }

        .file-upload-text {
            color: var(--gray-600);
            font-size: 16px;
            margin-bottom: 8px;
        }

        .file-upload-hint {
            color: var(--gray-500);
            font-size: 12px;
            line-height: 1.5;
        }

        .file-format-list {
            display: inline-flex;
            flex-wrap: wrap;
            gap: 4px;
            margin: 4px 0;
        }

        .format-item {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .format-item.pdf {
            background: rgba(220, 38, 38, 0.1);
            color: #dc2626;
        }

        .format-item.doc, .format-item.docx {
            background: rgba(37, 99, 235, 0.1);
            color: #2563eb;
        }

        .format-item.csv {
            background: rgba(34, 197, 94, 0.1);
            color: #22c55e;
        }

        .format-item.xls, .format-item.xlsx {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .file-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .uploaded-file {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
            border-radius: 8px;
            margin-top: 12px;
        }

        .uploaded-file-icon {
            color: var(--success-color);
            font-size: 18px;
        }

        .uploaded-file-info {
            flex: 1;
        }

        .uploaded-file-name {
            font-weight: 500;
            color: var(--gray-800);
            font-size: 14px;
        }

        .uploaded-file-size {
            color: var(--gray-600);
            font-size: 12px;
        }

        .remove-file-btn {
            background: none;
            border: none;
            color: var(--gray-500);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: color 0.2s;
        }

        .remove-file-btn:hover {
            color: var(--danger-color);
        }

        /* File Type Badges */
        .file-type-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            margin-left: 8px;
            text-transform: uppercase;
        }

        .pdf-badge {
            background: rgba(220, 38, 38, 0.1);
            color: #dc2626;
            border: 1px solid rgba(220, 38, 38, 0.2);
        }

        .doc-badge, .docx-badge {
            background: rgba(37, 99, 235, 0.1);
            color: #2563eb;
            border: 1px solid rgba(37, 99, 235, 0.2);
        }

        .csv-badge {
            background: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        .xls-badge, .xlsx-badge {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        /* Submit Button */
        .submit-button {
            width: 100%;
            padding: 16px 24px;
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submit-button.green {
            background: linear-gradient(135deg, #16a34a, #15803d);
        }

        .submit-button.blue {
            background: linear-gradient(135deg, var(--primary-color), var(--dify-color));
        }

        .submit-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }

        .submit-button:disabled {
            background: var(--gray-400);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            opacity: 0.7;
        }

        .submit-button.processing {
            background: linear-gradient(135deg, #6c757d, #495057) !important;
            cursor: not-allowed;
            opacity: 0.8;
        }

        .submit-button.processing:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        .submit-button .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        .submit-button.loading .loading-spinner,
        .submit-button.processing .loading-spinner {
            display: inline-block;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Test Panel Styles */
        .test-panel {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 2px solid #e3f2fd;
        }

        .test-section h3 {
            color: #1976d2;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
        }

        .test-subsection {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }

        .test-subsection h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .test-upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background: white;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .test-upload-area:hover {
            border-color: #2196f3;
            background: #f5f5f5;
        }

        .test-upload-status {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .test-status-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .test-status-icon.uploading {
            background: #e3f2fd;
            color: #1976d2;
        }

        .test-status-icon.success {
            background: #e8f5e8;
            color: #4caf50;
        }

        .test-status-icon.error {
            background: #ffebee;
            color: #f44336;
        }

        .test-status-icon .fa-spinner {
            animation: spin 1s linear infinite;
        }

        .test-ai-button {
            width: 100%;
            padding: 16px 24px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .test-ai-button.default {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            color: white;
        }

        .test-ai-button.blue {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
        }

        .test-ai-button.green {
            background: linear-gradient(135deg, #16a34a, #15803d);
            color: white;
        }

        .test-ai-button.processing {
            background: linear-gradient(135deg, #6c757d, #495057);
            cursor: not-allowed;
            opacity: 0.8;
        }

        .test-loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        .test-ai-button.processing .test-loading-spinner {
            display: inline-block;
        }

        .test-ai-button.processing .test-button-icon {
            display: none;
        }

        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .test-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .test-btn.btn-primary {
            background: #2196f3;
            color: white;
        }

        .test-btn.btn-success {
            background: #4caf50;
            color: white;
        }

        .test-btn.btn-danger {
            background: #f44336;
            color: white;
        }

        .test-btn.btn-warning {
            background: #ff9800;
            color: white;
        }

        .test-btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }

        /* Results Display */
        .results-container {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            min-height: 300px;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--gray-200);
        }

        .results-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
        }

        .results-content {
            color: var(--gray-700);
            line-height: 1.6;
        }

        .empty-results {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 250px;
            color: var(--gray-500);
        }

        .empty-results-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        /* Control Panel */
        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .workflow-selector {
            margin-bottom: 24px;
        }

        .workflow-selector label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 8px;
        }

        .workflow-select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            font-size: 14px;
            background: white;
            outline: none;
        }

        .api-status {
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            font-size: 14px;
        }

        .api-status.connected {
            background: rgba(34, 197, 94, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        .api-status.disconnected {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .quick-actions {
            display: grid;
            gap: 12px;
        }

        .action-button {
            padding: 12px 16px;
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            background: white;
            color: var(--gray-700);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            text-align: left;
        }

        .action-button:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .action-button i {
            margin-right: 8px;
            width: 16px;
        }

        /* Loading Animation */
        .loading {
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .loading-dots {
            display: flex;
            gap: 4px;
        }

        .loading-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--gray-400);
            animation: loading 1.4s infinite ease-in-out;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes loading {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        /* Workflow Results */
        .workflow-results {
            min-height: 200px;
            padding: 16px;
            background: var(--gray-50);
            border-radius: 8px;
            border: 1px solid var(--gray-200);
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
        }

        .result-item {
            background: white;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 12px;
            border: 1px solid var(--gray-200);
            box-shadow: var(--shadow-sm);
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .result-title {
            font-weight: 600;
            color: var(--gray-900);
        }

        .result-timestamp {
            font-size: 12px;
            color: var(--gray-500);
        }

        .result-content {
            color: var(--gray-700);
            line-height: 1.5;
        }

        /* Integration Grid */
        .integration-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
        }

        .integration-item {
            background: white;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.2s;
        }

        .integration-item:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
        }

        .integration-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            flex-shrink: 0;
        }

        .integration-info {
            flex: 1;
        }

        .integration-info h4 {
            font-size: 14px;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 4px;
        }

        .integration-info p {
            font-size: 12px;
            color: var(--gray-600);
            margin-bottom: 8px;
        }

        .integration-status {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .integration-status.active {
            color: var(--success-color);
        }

        .integration-status.inactive {
            color: var(--gray-400);
        }

        /* Progress Bar */
        .progress-section {
            margin-top: 20px;
            padding: 16px;
            background: var(--gray-50);
            border-radius: 8px;
            border: 1px solid var(--gray-200);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
            margin: 8px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            font-size: 12px;
            color: var(--gray-600);
            text-align: center;
        }

        /* Enhanced Form Styles */
        .form-input[type="radio"] {
            width: auto;
            margin-right: 6px;
        }

        .form-section label[style*="flex"] {
            margin-bottom: 0;
            cursor: pointer;
        }

        /* File Upload Progress Bar */
        .upload-progress-container {
            width: 100%;
            margin-top: 16px;
            display: none;
        }

        .upload-progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .upload-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 3px;
            transition: width 0.3s ease;
            width: 0%;
            position: relative;
        }

        .upload-progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: progressShimmer 1.5s infinite;
        }

        .upload-progress-text {
            font-size: 12px;
            text-align: center;
            color: var(--gray-600);
            font-weight: 500;
        }

        /* File Upload Animations */
        @keyframes uploadPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.4);
            }
            50% {
                transform: scale(1.02);
                box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);
            }
        }

        @keyframes successBounce {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes successPulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes errorPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes progressShimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .file-upload-area.uploading {
            animation: uploadPulse 2s ease-in-out infinite;
            border-color: var(--primary-color);
            background: rgba(37, 99, 235, 0.1);
        }

        .file-upload-area.success {
            border-color: var(--success-color);
            background: rgba(34, 197, 94, 0.1);
            animation: successBounce 0.6s ease-out;
        }

        @keyframes successBounce {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Notification Animations */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* Evaluation and Selection Results */
        .evaluation-results,
        .selection-results {
            padding: 20px;
            background: var(--gray-50);
            border-radius: 8px;
            margin-top: 16px;
        }

        .evaluation-section {
            margin-bottom: 20px;
        }

        .evaluation-section h4 {
            color: var(--gray-700);
            margin-bottom: 12px;
            font-size: 16px;
        }

        .evaluation-section ul {
            margin: 12px 0;
            padding-left: 20px;
        }

        .evaluation-section li {
            margin-bottom: 8px;
            color: var(--gray-600);
        }

        .partner-list {
            margin-bottom: 20px;
        }

        .partner-item {
            background: white;
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 12px;
            border-left: 4px solid var(--primary-color);
        }

        .partner-item h5 {
            margin: 0 0 8px 0;
            color: var(--gray-700);
            font-size: 14px;
        }

        .partner-item p {
            margin: 0;
            color: var(--gray-600);
            font-size: 13px;
        }

        .next-step {
            text-align: center;
            padding: 16px;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 6px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .next-step p {
            margin: 0;
            color: var(--primary-color);
            font-weight: 500;
        }

        .results-meta {
            display: flex;
            gap: 16px;
            margin-top: 8px;
            font-size: 13px;
            color: var(--gray-600);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .control-panel {
                order: -1;
            }

            .integration-grid {
                grid-template-columns: 1fr;
            }



            .results-meta {
                flex-direction: column;
                gap: 4px;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 12px;
            }
            
            .header {
                padding: 16px 20px;
            }
            
            .header-content {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .title-section h1 {
                font-size: 24px;
            }
            
            .chat-container {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo">E</div>
                    <div style="font-size: 24px; color: var(--gray-400);">×</div>
                    <div class="dify-logo">D</div>
                    <div class="title-section">
                        <h1>AI生态专家 × Dify智能分析</h1>
                        <p>专业的AI驱动生态合作伙伴开发与分析平台</p>
                    </div>
                </div>
                <div class="status-section">
                    <div class="status-indicator status-online">
                        <div class="status-pulse"></div>
                        <span>Dify API 已连接</span>
                    </div>
                    <div class="status-indicator status-online">
                        <div class="status-pulse"></div>
                        <span>EcoBuilder 集成已激活</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Dashboard -->
        <div class="dashboard-grid">
            <!-- Workflow Input Panel -->
            <div class="workflow-panel">
                <div class="panel-header">
                    <div class="panel-title">
                        <div class="panel-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        AI合同专家工作流
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="font-size: 12px; color: var(--gray-500);">
                            Powered by Dify v1.6.0
                        </div>
                        <button class="action-button" onclick="resetForm()" style="padding: 6px 12px; font-size: 12px;">
                            <i class="fas fa-refresh"></i>
                            重置表单
                        </button>
                    </div>
                </div>

                <div class="input-form">
                    <form id="workflowForm">
                        <!-- 用户信息输入区域 -->
                        <div class="form-section">
                            <label class="form-label">
                                <i class="fas fa-user-circle"></i>
                                用户信息输入
                            </label>
                            <div class="user-info-single">
                                <div class="form-group">
                                    <label for="userRequirement">用户需求描述 *</label>
                                    <textarea id="userRequirement" rows="4" placeholder="请详细描述您的公司信息、业务类型、合作目标和期望寻找的合作伙伴类型..." required></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 文件上传区域 -->
                        <div class="form-section">
                            <label class="form-label">
                                <i class="fas fa-upload"></i>
                                上传合同文件
                            </label>
                            <div class="file-upload-area" id="fileUploadArea">
                                <!-- Default Upload State -->
                                <div id="uploadDefault" class="upload-content">
                                    <div class="file-upload-icon">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                    </div>
                                    <div class="file-upload-text">点击上传或拖拽文件到此处</div>
                                    <div class="file-upload-hint">
                                        支持多种格式：
                                        <span class="file-format-list">
                                            <span class="format-item pdf">PDF</span>
                                            <span class="format-item doc">DOC</span>
                                            <span class="format-item docx">DOCX</span>
                                            <span class="format-item csv">CSV</span>
                                            <span class="format-item xls">XLS</span>
                                            <span class="format-item xlsx">XLSX</span>
                                        </span>
                                        <br>最大文件大小：10MB
                                    </div>
                                </div>

                                <!-- Upload Status Display -->
                                <div id="uploadStatus" class="upload-status" style="display: none;">
                                    <div class="upload-status-icon">
                                        <i class="fas fa-spinner"></i>
                                    </div>
                                    <div class="upload-status-text">正在上传文件...</div>
                                    <div class="upload-status-subtext">请稍候，正在处理您的文件</div>

                                    <!-- Progress Bar -->
                                    <div class="upload-progress-container" id="progressContainer">
                                        <div class="upload-progress-bar">
                                            <div class="upload-progress-fill" id="progressFill"></div>
                                        </div>
                                        <div class="upload-progress-text" id="progressText">0%</div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="upload-actions" id="uploadActions" style="display: none;">
                                        <button class="upload-action-btn retry" onclick="retryUpload()">
                                            <i class="fas fa-redo"></i>
                                            重新上传
                                        </button>
                                        <button class="upload-action-btn cancel" onclick="resetUploadArea()">
                                            <i class="fas fa-times"></i>
                                            取消
                                        </button>
                                    </div>
                                </div>

                                <input type="file" class="file-input" id="fileInput" accept=".pdf,.doc,.docx,.csv,.xls,.xlsx" />
                            </div>
                            <div id="uploadedFiles"></div>
                        </div>

                        <!-- 工作领域 -->
                        <div class="form-section">
                            <label class="form-label" for="workField">
                                <i class="fas fa-briefcase"></i>
                                工作领域
                            </label>
                            <select class="form-input" id="workField" required>
                                <option value="">请选择工作领域</option>
                                <option value="technology">科技/IT</option>
                                <option value="finance">金融服务</option>
                                <option value="healthcare">医疗健康</option>
                                <option value="manufacturing">制造业</option>
                                <option value="retail">零售/电商</option>
                                <option value="education">教育培训</option>
                                <option value="real-estate">房地产</option>
                                <option value="consulting">咨询服务</option>
                                <option value="other">其他</option>
                            </select>
                        </div>

                        <!-- 审查主体 -->
                        <div class="form-section">
                            <label class="form-label" for="reviewSubject">
                                <i class="fas fa-user-tie"></i>
                                审查主体
                            </label>
                            <input type="text" class="form-input" id="reviewSubject"
                                   placeholder="请输入审查主体名称（如：公司名称、部门等）" required />
                        </div>

                        <!-- 合同要点 -->
                        <div class="form-section">
                            <label class="form-label" for="contractPoints">
                                <i class="fas fa-list-ul"></i>
                                合同要点
                            </label>
                            <textarea class="form-input form-textarea" id="contractPoints"
                                      placeholder="请详细描述需要重点关注的合同条款和要点，例如：&#10;• 付款条款和周期&#10;• 违约责任和赔偿&#10;• 知识产权归属&#10;• 保密条款&#10;• 终止条件等"
                                      required></textarea>
                        </div>

                        <!-- 提交按钮 -->
                        <button type="submit" class="submit-button" id="submitButton">
                            <div class="loading-spinner"></div>
                            <i class="fas fa-magic"></i>
                            <span class="button-text">开始AI生态分析</span>
                        </button>
                    </form>
                </div>
            </div>

            <!-- Test Panel (only visible in test mode) -->
            <div class="test-panel" id="testPanel" style="display: none;">
                <div class="test-section">
                    <h3>🧪 状态更新测试页面</h3>

                    <!-- 文件上传状态测试 -->
                    <div class="test-subsection">
                        <h4>📁 文件上传状态测试</h4>
                        <div class="test-upload-area" id="testUploadArea">
                            <input type="file" id="testFileInput" style="display: none;" accept=".pdf,.doc,.docx,.csv,.xls,.xlsx">

                            <div id="testUploadDefault" onclick="triggerTestFileInput()">
                                <i class="fas fa-cloud-upload-alt" style="font-size: 32px; color: #ccc;"></i>
                                <p>点击测试文件上传</p>
                            </div>

                            <div class="test-upload-status" id="testUploadStatus" style="display: none;">
                                <div class="test-status-icon" id="testStatusIcon">
                                    <i class="fas fa-spinner"></i>
                                </div>
                                <div id="testStatusText">正在上传...</div>
                                <div id="testStatusSubtext">请稍候</div>
                            </div>
                        </div>

                        <div class="test-buttons">
                            <button class="test-btn btn-primary" onclick="testUploadStatus()">测试上传状态</button>
                            <button class="test-btn btn-success" onclick="testUploadSuccess()">测试成功状态</button>
                            <button class="test-btn btn-danger" onclick="testUploadError()">测试错误状态</button>
                            <button class="test-btn btn-warning" onclick="resetTestUpload()">重置状态</button>
                        </div>
                    </div>

                    <!-- AI按钮状态测试 -->
                    <div class="test-subsection">
                        <h4>🤖 AI分析按钮测试</h4>
                        <button class="test-ai-button default" id="testAiButton" onclick="handleTestAIButtonClick()">
                            <div class="test-loading-spinner"></div>
                            <i class="fas fa-magic test-button-icon"></i>
                            <span class="test-button-text">开始AI生态分析</span>
                        </button>

                        <div class="test-buttons">
                            <button class="test-btn btn-primary" onclick="testAIProcessing()">测试处理状态</button>
                            <button class="test-btn btn-primary" onclick="testAIEvaluation()">测试评估阶段</button>
                            <button class="test-btn btn-success" onclick="testAISelection()">测试选择阶段</button>
                            <button class="test-btn btn-warning" onclick="resetTestAIButton()">重置按钮</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Control Panel -->
            <div class="control-panel">
                <div class="panel-header">
                    <div class="panel-title">
                        <div class="panel-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        分析设置
                    </div>
                </div>

                <div class="api-status connected" id="apiStatus">
                    <i class="fas fa-check-circle"></i>
                    Dify API 连接正常
                </div>

                <div class="workflow-selector">
                    <label for="analysisType">分析类型</label>
                    <select class="workflow-select" id="analysisType">
                        <option value="comprehensive">全面合同分析</option>
                        <option value="risk-assessment">风险评估分析</option>
                        <option value="legal-compliance">法律合规检查</option>
                        <option value="financial-terms">财务条款分析</option>
                        <option value="liability-review">责任条款审查</option>
                    </select>
                </div>

                <div class="form-section">
                    <label class="form-label">分析深度</label>
                    <div style="display: flex; gap: 8px; margin-top: 8px;">
                        <label style="display: flex; align-items: center; gap: 6px; font-size: 14px; font-weight: normal;">
                            <input type="radio" name="analysisDepth" value="basic" checked />
                            基础分析
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; font-size: 14px; font-weight: normal;">
                            <input type="radio" name="analysisDepth" value="detailed" />
                            详细分析
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; font-size: 14px; font-weight: normal;">
                            <input type="radio" name="analysisDepth" value="expert" />
                            专家级
                        </label>
                    </div>
                </div>

                <div class="quick-actions">
                    <button class="action-button" onclick="fillSampleData()">
                        <i class="fas fa-magic"></i>
                        填充示例数据
                    </button>
                    <button class="action-button" onclick="previewAnalysis()">
                        <i class="fas fa-eye"></i>
                        预览分析框架
                    </button>
                    <button class="action-button" onclick="saveTemplate()">
                        <i class="fas fa-save"></i>
                        保存为模板
                    </button>
                    <button class="action-button" onclick="loadTemplate()">
                        <i class="fas fa-folder-open"></i>
                        加载模板
                    </button>
                    <button class="action-button" onclick="showHelp()">
                        <i class="fas fa-question-circle"></i>
                        使用帮助
                    </button>
                </div>

                <!-- 进度指示器 -->
                <div class="progress-section" id="progressSection" style="display: none;">
                    <label class="form-label">分析进度</label>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备中...</div>
                </div>
            </div>
        </div>

        <!-- Analysis Results Panel -->
        <div class="workflow-panel" style="margin-top: 24px;">
            <div class="panel-header">
                <div class="panel-title">
                    <div class="panel-icon">
                        <i class="fas fa-analytics"></i>
                    </div>
                    AI合同分析结果
                </div>
                <div style="display: flex; gap: 8px;">
                    <button class="action-button" onclick="exportResults()" style="padding: 8px 16px; font-size: 12px;">
                        <i class="fas fa-download"></i>
                        导出报告
                    </button>
                    <button class="action-button" onclick="shareResults()" style="padding: 8px 16px; font-size: 12px;">
                        <i class="fas fa-share"></i>
                        分享结果
                    </button>
                </div>
            </div>

            <div class="results-container" id="resultsContainer">
                <div class="empty-results">
                    <div class="empty-results-icon">
                        <i class="fas fa-file-contract"></i>
                    </div>
                    <h3 style="margin-bottom: 8px; color: var(--gray-600);">等待分析结果</h3>
                    <p>填写用户信息并上传合同文件后，AI生态专家将为您提供专业的合作伙伴分析报告</p>
                </div>
            </div>
        </div>

        <!-- EcoBuilder Integration Status -->
        <div class="workflow-panel" style="margin-top: 24px;">
            <div class="panel-header">
                <div class="panel-title">
                    <div class="panel-icon">
                        <i class="fas fa-network-wired"></i>
                    </div>
                    EcoBuilder集成状态
                </div>
            </div>

            <div class="integration-grid">
                <div class="integration-item">
                    <div class="integration-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="integration-info">
                        <h4>PartnerScout</h4>
                        <p>合作伙伴发现智能体</p>
                        <div class="integration-status active">
                            <i class="fas fa-check-circle"></i>
                            已集成
                        </div>
                    </div>
                </div>

                <div class="integration-item">
                    <div class="integration-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="integration-info">
                        <h4>PartnerRecruiter</h4>
                        <p>合作伙伴招募智能体</p>
                        <div class="integration-status active">
                            <i class="fas fa-check-circle"></i>
                            已集成
                        </div>
                    </div>
                </div>

                <div class="integration-item">
                    <div class="integration-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="integration-info">
                        <h4>PartnerTrainer</h4>
                        <p>合作伙伴培训智能体</p>
                        <div class="integration-status active">
                            <i class="fas fa-check-circle"></i>
                            已集成
                        </div>
                    </div>
                </div>

                <div class="integration-item">
                    <div class="integration-icon">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <div class="integration-info">
                        <h4>OpportunityManager</h4>
                        <p>商机管理智能体</p>
                        <div class="integration-status active">
                            <i class="fas fa-check-circle"></i>
                            已集成
                        </div>
                    </div>
                </div>

                <div class="integration-item">
                    <div class="integration-icon">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </div>
                    <div class="integration-info">
                        <h4>MarketExpander</h4>
                        <p>市场拓展智能体</p>
                        <div class="integration-status active">
                            <i class="fas fa-check-circle"></i>
                            已集成
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notification Container -->
    <div class="toast-container" id="toastContainer"></div>

    <script>
        // Dify API Configuration
        const DIFY_CONFIG = {
            apiKey: 'app-ZRuKpcHUrE5E0zTtNQLddDCc',
            baseUrl: 'https://api.dify.ai/v1',
            workflowId: '8cc0dc2f-37ed-4dbd-b647-0fc278810788'
        };

        // Form functionality
        let isProcessing = false;
        let uploadedFile = null;
        let userInfo = null;
        let currentWorkflowStage = 'input'; // input, evaluation, selection, analysis

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkDifyConnection();
            setupEventListeners();
            updateWorkflowStage('input'); // Set initial stage

            // Add test functionality for development
            if (window.location.search.includes('test=true')) {
                addTestFunctions();

                // Show test panel
                const testPanel = document.getElementById('testPanel');
                if (testPanel) {
                    testPanel.style.display = 'block';
                    console.log('Test panel displayed');
                }
            }
        });

        // Test functions for development
        function addTestFunctions() {
            console.log('Test mode enabled - add ?test=true to URL');

            // Add test button to the page
            const testButton = document.createElement('button');
            testButton.textContent = '测试文件上传';
            testButton.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 9999;
                padding: 8px 16px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            `;
            testButton.onclick = testFileUpload;
            document.body.appendChild(testButton);

            // Add debug button
            const debugButton = document.createElement('button');
            debugButton.textContent = '调试状态';
            debugButton.style.cssText = `
                position: fixed;
                top: 50px;
                right: 10px;
                z-index: 9999;
                padding: 8px 16px;
                background: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            `;
            debugButton.onclick = debugStatus;
            document.body.appendChild(debugButton);

            // Add force status button
            const forceButton = document.createElement('button');
            forceButton.textContent = '强制显示状态';
            forceButton.style.cssText = `
                position: fixed;
                top: 90px;
                right: 10px;
                z-index: 9999;
                padding: 8px 16px;
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            `;
            forceButton.onclick = forceShowStatus;
            document.body.appendChild(forceButton);

            // Add workflow test button
            const workflowButton = document.createElement('button');
            workflowButton.textContent = '测试完整工作流';
            workflowButton.style.cssText = `
                position: fixed;
                top: 130px;
                right: 10px;
                z-index: 9999;
                padding: 8px 16px;
                background: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            `;
            workflowButton.onclick = testCompleteWorkflow;
            document.body.appendChild(workflowButton);
        }

        function debugStatus() {
            console.log('=== Debug Status ===');
            console.log('uploadedFile:', uploadedFile);
            console.log('isProcessing:', isProcessing);
            console.log('currentWorkflowStage:', currentWorkflowStage);

            const uploadArea = document.getElementById('fileUploadArea');
            const uploadDefault = document.getElementById('uploadDefault');
            const uploadStatus = document.getElementById('uploadStatus');
            const submitButton = document.getElementById('submitButton');

            console.log('Elements:', {
                uploadArea: uploadArea ? 'found' : 'missing',
                uploadDefault: uploadDefault ? 'found' : 'missing',
                uploadStatus: uploadStatus ? 'found' : 'missing',
                submitButton: submitButton ? 'found' : 'missing'
            });

            if (uploadArea) {
                console.log('Upload area classes:', uploadArea.className);
            }
            if (uploadDefault) {
                console.log('Upload default display:', uploadDefault.style.display);
            }
            if (uploadStatus) {
                console.log('Upload status display:', uploadStatus.style.display);
            }
            if (submitButton) {
                console.log('Submit button classes:', submitButton.className);
                console.log('Submit button disabled:', submitButton.disabled);
            }
        }

        function forceShowStatus() {
            console.log('Forcing status display...');

            // Force show upload status
            showUploadStatus('uploading', '强制测试上传状态', '这是一个测试状态');

            // Force show button processing state
            updateButtonProcessingState(true);

            setTimeout(() => {
                showUploadStatus('success', '强制测试成功状态', '文件上传成功');
                updateButtonProcessingState(false);
                updateWorkflowStage('evaluation');
            }, 3000);
        }

        function testCompleteWorkflow() {
            console.log('Testing complete workflow...');

            // First, ensure we have a file uploaded
            if (!uploadedFile) {
                testFileUpload();
                setTimeout(() => {
                    startWorkflowTest();
                }, 4000); // Wait for file upload to complete
            } else {
                startWorkflowTest();
            }
        }

        function startWorkflowTest() {
            console.log('Starting workflow test...');

            // Fill form with test data
            fillTestData();

            // Start the workflow
            setTimeout(() => {
                console.log('Triggering form submission...');
                const form = document.getElementById('workflowForm');
                const event = new Event('submit', { bubbles: true, cancelable: true });
                form.dispatchEvent(event);
            }, 1000);
        }

        function fillTestData() {
            console.log('Filling test data...');

            // Fill required fields
            document.getElementById('userRequirement').value = '测试用户需求：需要分析合同风险和合规性';
            document.getElementById('workField').value = 'technology';
            document.getElementById('reviewSubject').value = '测试公司';
            document.getElementById('contractPoints').value = '• 付款条款测试\n• 违约责任测试\n• 知识产权测试';

            showToast('info', '测试数据', '已自动填写测试数据');
        }

        // ===== 测试面板功能函数 =====

        // 文件上传测试函数
        function triggerTestFileInput() {
            document.getElementById('testFileInput').click();
        }

        function handleTestFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                console.log('Test file selected:', file.name);
                testUploadStatus();
                setTimeout(() => {
                    testUploadSuccess();
                }, 2000);
            }
        }

        function testUploadStatus() {
            const defaultArea = document.getElementById('testUploadDefault');
            const statusArea = document.getElementById('testUploadStatus');
            const statusIcon = document.getElementById('testStatusIcon');
            const statusText = document.getElementById('testStatusText');
            const statusSubtext = document.getElementById('testStatusSubtext');

            defaultArea.style.display = 'none';
            statusArea.style.display = 'flex';

            statusIcon.className = 'test-status-icon uploading';
            statusIcon.innerHTML = '<i class="fas fa-spinner"></i>';
            statusText.textContent = '正在上传文件...';
            statusSubtext.textContent = '请稍候，正在处理您的文件';
        }

        function testUploadSuccess() {
            const statusIcon = document.getElementById('testStatusIcon');
            const statusText = document.getElementById('testStatusText');
            const statusSubtext = document.getElementById('testStatusSubtext');

            statusIcon.className = 'test-status-icon success';
            statusIcon.innerHTML = '<i class="fas fa-check"></i>';
            statusText.textContent = '文件上传成功！';
            statusSubtext.textContent = '文件已成功上传并验证';
        }

        function testUploadError() {
            const defaultArea = document.getElementById('testUploadDefault');
            const statusArea = document.getElementById('testUploadStatus');
            const statusIcon = document.getElementById('testStatusIcon');
            const statusText = document.getElementById('testStatusText');
            const statusSubtext = document.getElementById('testStatusSubtext');

            defaultArea.style.display = 'none';
            statusArea.style.display = 'flex';

            statusIcon.className = 'test-status-icon error';
            statusIcon.innerHTML = '<i class="fas fa-times"></i>';
            statusText.textContent = '上传失败';
            statusSubtext.textContent = '请检查文件格式和大小';
        }

        function resetTestUpload() {
            const defaultArea = document.getElementById('testUploadDefault');
            const statusArea = document.getElementById('testUploadStatus');

            defaultArea.style.display = 'block';
            statusArea.style.display = 'none';

            // Reset file input
            document.getElementById('testFileInput').value = '';
        }

        // AI按钮测试函数
        let testAIStage = 'input';

        function handleTestAIButtonClick() {
            console.log('Test AI button clicked, current stage:', testAIStage);

            switch(testAIStage) {
                case 'input':
                    testAIProcessing();
                    setTimeout(() => {
                        testAIEvaluation();
                        testAIStage = 'evaluation';
                    }, 2000);
                    break;
                case 'evaluation':
                    testAIProcessing();
                    setTimeout(() => {
                        testAISelection();
                        testAIStage = 'selection';
                    }, 2000);
                    break;
                case 'selection':
                    testAIProcessing();
                    setTimeout(() => {
                        testAIAnalysis();
                        testAIStage = 'analysis';
                    }, 2000);
                    break;
                case 'analysis':
                    testAIProcessing();
                    setTimeout(() => {
                        resetTestAIButton();
                        testAIStage = 'input';
                    }, 2000);
                    break;
            }
        }

        function testAIProcessing() {
            const button = document.getElementById('testAiButton');
            const buttonText = button.querySelector('.test-button-text');

            button.className = 'test-ai-button processing';
            button.disabled = true;
            buttonText.textContent = '处理中...';
        }

        function testAIEvaluation() {
            const button = document.getElementById('testAiButton');
            const buttonIcon = button.querySelector('.test-button-icon');
            const buttonText = button.querySelector('.test-button-text');

            button.className = 'test-ai-button blue';
            button.disabled = false;
            buttonIcon.className = 'fas fa-search test-button-icon';
            buttonText.textContent = '评估合作伙伴需求';
        }

        function testAISelection() {
            const button = document.getElementById('testAiButton');
            const buttonIcon = button.querySelector('.test-button-icon');
            const buttonText = button.querySelector('.test-button-text');

            button.className = 'test-ai-button green';
            button.disabled = false;
            buttonIcon.className = 'fas fa-check-circle test-button-icon';
            buttonText.textContent = '选择合作伙伴';
        }

        function testAIAnalysis() {
            const button = document.getElementById('testAiButton');
            const buttonIcon = button.querySelector('.test-button-icon');
            const buttonText = button.querySelector('.test-button-text');

            button.className = 'test-ai-button blue';
            button.disabled = false;
            buttonIcon.className = 'fas fa-chart-line test-button-icon';
            buttonText.textContent = '生成分析报告';
        }

        function resetTestAIButton() {
            const button = document.getElementById('testAiButton');
            const buttonIcon = button.querySelector('.test-button-icon');
            const buttonText = button.querySelector('.test-button-text');

            button.className = 'test-ai-button default';
            button.disabled = false;
            buttonIcon.className = 'fas fa-magic test-button-icon';
            buttonText.textContent = '开始AI生态分析';
            testAIStage = 'input';
        }

        function testFileUpload() {
            console.log('Test button clicked');

            // Create a mock file for testing
            const mockFile = new File(['test content for PDF document'], 'test-contract.pdf', {
                type: 'application/pdf'
            });

            console.log('Testing file upload with mock file:', mockFile);
            showToast('info', '测试模式', '正在测试文件上传功能...');

            // Reset upload area first
            resetUploadArea();

            // Process the file
            setTimeout(() => {
                processFile(mockFile);
            }, 500);
        }

        function setupEventListeners() {
            const form = document.getElementById('workflowForm');
            const fileInput = document.getElementById('fileInput');
            const fileUploadArea = document.getElementById('fileUploadArea');
            const submitButton = document.getElementById('submitButton');

            // Form submission
            form.addEventListener('submit', handleFormSubmit);

            // Direct button click handler (backup for form submission)
            submitButton.addEventListener('click', function(e) {
                console.log('Submit button clicked directly');
                // Let the form submission handle it
            });

            // File upload
            fileInput.addEventListener('change', handleFileSelect);

            // Drag and drop
            fileUploadArea.addEventListener('dragover', handleDragOver);
            fileUploadArea.addEventListener('dragleave', handleDragLeave);
            fileUploadArea.addEventListener('drop', handleFileDrop);
        }

        function handleFormSubmit(e) {
            e.preventDefault();
            console.log('Form submitted, current stage:', currentWorkflowStage);

            if (isProcessing) {
                console.log('Already processing, ignoring submit');
                return;
            }

            // Execute different actions based on current workflow stage
            switch(currentWorkflowStage) {
                case 'input':
                    startInitialAnalysis();
                    break;
                case 'evaluation':
                    startEvaluation();
                    break;
                case 'selection':
                    startSelection();
                    break;
                case 'analysis':
                    generateFinalReport();
                    break;
                default:
                    console.error('Unknown workflow stage:', currentWorkflowStage);
            }
        }

        function startInitialAnalysis() {
            console.log('Starting initial analysis');

            // Get user info
            const userRequirement = document.getElementById('userRequirement').value;

            const formData = new FormData();
            const workField = document.getElementById('workField').value;
            const reviewSubject = document.getElementById('reviewSubject').value;
            const contractPoints = document.getElementById('contractPoints').value;
            const analysisType = document.getElementById('analysisType').value;
            const analysisDepthElement = document.querySelector('input[name="analysisDepth"]:checked');
            const analysisDepth = analysisDepthElement ? analysisDepthElement.value : 'basic';

            console.log('Form data:', {
                userRequirement,
                workField,
                reviewSubject,
                contractPoints,
                analysisType,
                analysisDepth,
                uploadedFile: uploadedFile ? uploadedFile.name : 'none'
            });

            // Validate user info
            if (!userRequirement) {
                showToast('error', '验证失败', '请填写用户需求描述');
                return;
            }

            if (!uploadedFile) {
                showToast('error', '验证失败', '请先上传合同文件');
                return;
            }

            if (!workField || !reviewSubject || !contractPoints) {
                showToast('error', '验证失败', '请填写所有必填字段');
                return;
            }

            // Store user info
            userInfo = {
                userRequirement
            };

            // Prepare form data
            formData.append('file', uploadedFile);
            formData.append('work_field', workField);
            formData.append('review_subject', reviewSubject);
            formData.append('contract_points', contractPoints);
            formData.append('analysis_type', analysisType);
            formData.append('analysis_depth', analysisDepth);
            formData.append('user_requirement', userRequirement);

            // Start the analysis process
            executeWorkflowStep('analysis', formData);
        }

        function startEvaluation() {
            console.log('Starting evaluation phase');
            showToast('info', '评估阶段', '正在评估合作伙伴需求...');
            executeWorkflowStep('evaluation');
        }

        function startSelection() {
            console.log('Starting selection phase');
            showToast('info', '选择阶段', '正在选择最佳合作伙伴...');
            executeWorkflowStep('selection');
        }

        function generateFinalReport() {
            console.log('Generating final report');
            showToast('info', '生成报告', '正在生成最终分析报告...');
            executeWorkflowStep('report');
        }

        function executeWorkflowStep(stepType, formData = null) {
            // Show processing state
            updateButtonProcessingState(true);

            // Simulate processing time based on step type
            let processingTime = 3000; // Default 3 seconds
            let nextStage = '';
            let successMessage = '';

            switch(stepType) {
                case 'analysis':
                    processingTime = 3000;
                    nextStage = 'evaluation';
                    successMessage = '初始分析完成，进入需求评估阶段';
                    break;
                case 'evaluation':
                    processingTime = 2500;
                    nextStage = 'selection';
                    successMessage = '需求评估完成，进入合作伙伴选择阶段';
                    break;
                case 'selection':
                    processingTime = 2000;
                    nextStage = 'analysis';
                    successMessage = '合作伙伴选择完成，进入最终分析阶段';
                    break;
                case 'report':
                    processingTime = 3000;
                    nextStage = 'input';
                    successMessage = '分析报告生成完成！';
                    break;
            }

            setTimeout(() => {
                updateButtonProcessingState(false);
                updateWorkflowStage(nextStage);
                showToast('success', '阶段完成', successMessage);

                if (stepType === 'report') {
                    // Show completion message and reset
                    setTimeout(() => {
                        alert('🎉 AI生态分析流程已完成！\n\n您可以查看生成的分析报告，或开始新的分析。');
                    }, 1000);
                }
            }, processingTime);
        }

        function updateWorkflowStage(stage) {
            currentWorkflowStage = stage;
            const submitButton = document.getElementById('submitButton');
            const buttonIcon = submitButton.querySelector('i');
            const buttonText = submitButton.querySelector('.button-text');

            // Remove existing color classes
            submitButton.classList.remove('green', 'blue');

            switch(stage) {
                case 'input':
                    submitButton.className = 'submit-button'; // Red (default)
                    buttonIcon.className = 'fas fa-magic';
                    buttonText.textContent = '开始AI生态分析';
                    break;
                case 'evaluation':
                    submitButton.classList.add('blue');
                    buttonIcon.className = 'fas fa-search';
                    buttonText.textContent = '评估合作伙伴需求';
                    break;
                case 'selection':
                    submitButton.classList.add('green');
                    buttonIcon.className = 'fas fa-check-circle';
                    buttonText.textContent = '选择合作伙伴';
                    break;
                case 'analysis':
                    submitButton.classList.add('blue');
                    buttonIcon.className = 'fas fa-chart-line';
                    buttonText.textContent = '生成分析报告';
                    break;
            }
        }

        function updateButtonProcessingState(isProcessing) {
            const submitButton = document.getElementById('submitButton');
            const buttonIcon = submitButton.querySelector('i');
            const buttonText = submitButton.querySelector('.button-text');
            const spinner = submitButton.querySelector('.loading-spinner');

            if (isProcessing) {
                submitButton.disabled = true;
                submitButton.classList.add('processing');
                buttonIcon.style.display = 'none';
                spinner.style.display = 'inline-block';
                buttonText.textContent = '处理中...';
            } else {
                submitButton.disabled = false;
                submitButton.classList.remove('processing');
                buttonIcon.style.display = 'inline-block';
                spinner.style.display = 'none';
                // Text will be updated by updateWorkflowStage
            }
        }

        async function checkDifyConnection() {
            try {
                const response = await fetch(`${DIFY_CONFIG.baseUrl}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${DIFY_CONFIG.apiKey}`
                    }
                });
                
                if (response.ok) {
                    updateApiStatus(true, 'Dify API 连接正常');
                } else {
                    updateApiStatus(false, 'Dify API 连接失败');
                }
            } catch (error) {
                updateApiStatus(false, 'Dify API 连接错误');
                console.error('Dify connection error:', error);
            }
        }

        function updateApiStatus(connected, message) {
            const statusElement = document.getElementById('apiStatus');
            statusElement.className = `api-status ${connected ? 'connected' : 'disconnected'}`;
            statusElement.innerHTML = `
                <i class="fas fa-${connected ? 'check-circle' : 'exclamation-triangle'}"></i>
                ${message}
            `;
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                processFile(file);
            }
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.currentTarget.classList.remove('dragover');
        }

        function handleFileDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        }

        function processFile(file) {
            console.log('Processing file:', file.name, file.type, file.size);

            // Show upload status
            showUploadStatus('uploading', '正在验证文件...', '检查文件格式和大小');

            // Validate file type
            const allowedTypes = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'text/csv',
                'application/csv',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ];

            const allowedExtensions = ['.pdf', '.doc', '.docx', '.csv', '.xls', '.xlsx'];
            const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

            if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
                const errorMsg = `不支持 ${fileExtension} 格式，请上传 PDF、DOC、DOCX、CSV、XLS 或 XLSX 格式的文件`;
                showUploadStatus('error', '文件格式不支持', errorMsg);
                showToast('error', '文件格式错误', errorMsg);
                setTimeout(() => resetUploadArea(), 3000);
                return;
            }

            // Validate file size (10MB)
            if (file.size > 10 * 1024 * 1024) {
                const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
                const errorMsg = `文件大小 ${fileSizeMB}MB 超过限制，最大支持 10MB`;
                showUploadStatus('error', '文件过大', errorMsg);
                showToast('error', '文件过大', errorMsg);
                setTimeout(() => resetUploadArea(), 3000);
                return;
            }

            // Start upload simulation with progress
            simulateFileUpload(file);
        }

        function simulateFileUpload(file) {
            let progress = 0;
            const progressContainer = document.getElementById('progressContainer');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            // Show progress bar
            progressContainer.style.display = 'block';

            // Update status to uploading
            const fileSize = formatFileSize(file.size);
            showUploadStatus('uploading', '正在上传文件...', `${file.name} (${fileSize})`);

            const uploadInterval = setInterval(() => {
                progress += Math.random() * 15 + 5; // Random progress increment

                if (progress >= 100) {
                    progress = 100;
                    clearInterval(uploadInterval);

                    // Complete upload
                    progressFill.style.width = '100%';
                    progressText.textContent = '100%';

                    setTimeout(() => {
                        uploadedFile = file;
                        const successMsg = `${file.name} 已成功上传并准备分析`;
                        showUploadStatus('success', '文件上传成功', successMsg);
                        showToast('success', '上传完成', `文件 ${file.name} 上传成功`);
                        displayUploadedFile(file);

                        // Update upload area to show success state
                        const uploadArea = document.getElementById('fileUploadArea');
                        uploadArea.classList.remove('uploading');
                        uploadArea.classList.add('success');

                        // Hide progress bar after success
                        setTimeout(() => {
                            progressContainer.style.display = 'none';
                        }, 1500);

                        // Keep status visible longer for success
                        setTimeout(() => {
                            hideUploadStatus();
                        }, 5000);
                    }, 500);
                } else {
                    progressFill.style.width = progress + '%';
                    progressText.textContent = Math.round(progress) + '%';

                    // Update status text based on progress
                    if (progress < 30) {
                        showUploadStatus('uploading', '正在上传文件...', '建立连接中');
                    } else if (progress < 70) {
                        showUploadStatus('uploading', '正在上传文件...', '传输数据中');
                    } else {
                        showUploadStatus('uploading', '正在上传文件...', '处理文件中');
                    }
                }
            }, 200);
        }

        function showUploadStatus(type, message, subtext = '') {
            console.log('Showing upload status:', type, message, subtext);

            const uploadArea = document.getElementById('fileUploadArea');
            const uploadDefault = document.getElementById('uploadDefault');
            const uploadStatus = document.getElementById('uploadStatus');

            if (!uploadArea || !uploadDefault || !uploadStatus) {
                console.error('Upload elements not found:', { uploadArea, uploadDefault, uploadStatus });
                return;
            }

            // Hide default content and show status
            uploadDefault.style.display = 'none';
            uploadStatus.style.display = 'flex';

            // Update upload area class
            const statusClass = type === 'success' ? 'upload-success' :
                               type === 'error' ? 'upload-error' : 'upload-loading';

            // Remove existing status classes
            uploadArea.classList.remove('upload-success', 'upload-error', 'upload-loading');
            uploadArea.classList.add(statusClass);

            // Update status content
            const statusIcon = uploadStatus.querySelector('.upload-status-icon i');
            const statusText = uploadStatus.querySelector('.upload-status-text');
            const statusSubtext = uploadStatus.querySelector('.upload-status-subtext');

            // Update icon based on type
            let iconClass = '';
            switch(type) {
                case 'success':
                    iconClass = 'fas fa-check-circle';
                    break;
                case 'error':
                    iconClass = 'fas fa-exclamation-triangle';
                    break;
                case 'uploading':
                default:
                    iconClass = 'fas fa-spinner';
                    break;
            }

            statusIcon.className = iconClass;
            statusText.textContent = message;
            statusSubtext.textContent = subtext;

            // Add spinning animation for uploading state
            if (type === 'uploading') {
                statusIcon.classList.add('uploading');
            } else {
                statusIcon.classList.remove('uploading');
            }

            // Show/hide action buttons based on status
            const uploadActions = document.getElementById('uploadActions');
            if (type === 'error') {
                uploadActions.style.display = 'flex';
            } else {
                uploadActions.style.display = 'none';
            }
        }

        function hideUploadStatus() {
            const uploadArea = document.getElementById('fileUploadArea');
            const uploadDefault = document.getElementById('uploadDefault');
            const uploadStatus = document.getElementById('uploadStatus');

            // Remove status classes
            uploadArea.classList.remove('upload-success', 'upload-error', 'upload-loading');

            // Show default content and hide status
            uploadDefault.style.display = 'block';
            uploadStatus.style.display = 'none';
        }

        function resetUploadArea() {
            hideUploadStatus();
            uploadedFile = null;

            // Reset upload area classes
            const uploadArea = document.getElementById('fileUploadArea');
            if (uploadArea) {
                uploadArea.classList.remove('drag-over', 'uploading', 'error', 'success');
            }

            // Clear uploaded files display
            const uploadedFilesContainer = document.getElementById('uploadedFiles');
            if (uploadedFilesContainer) {
                uploadedFilesContainer.innerHTML = '';
            }

            // Reset progress bar
            const progressContainer = document.getElementById('progressContainer');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const uploadActions = document.getElementById('uploadActions');

            if (progressContainer) {
                progressContainer.style.display = 'none';
                progressFill.style.width = '0%';
                progressText.textContent = '0%';
            }

            if (uploadActions) {
                uploadActions.style.display = 'none';
            }

            // Reset file input
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.value = '';
            }
        }

        function retryUpload() {
            const fileInput = document.getElementById('fileInput');
            if (fileInput && fileInput.files.length > 0) {
                processFile(fileInput.files[0]);
            } else {
                // Trigger file selection dialog
                fileInput.click();
            }
        }

        // Add file size formatting function
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Toast Notification System
        function showToast(type, title, message, duration = 5000) {
            const toastContainer = document.getElementById('toastContainer');
            const toastId = 'toast_' + Date.now();

            const iconClass = type === 'success' ? 'fa-check-circle' :
                             type === 'error' ? 'fa-exclamation-triangle' :
                             'fa-info-circle';

            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.id = toastId;
            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="fas ${iconClass}"></i>
                </div>
                <div class="toast-content">
                    <div class="toast-title">${title}</div>
                    <div class="toast-message">${message}</div>
                </div>
                <button class="toast-close" onclick="removeToast('${toastId}')">
                    <i class="fas fa-times"></i>
                </button>
            `;

            toastContainer.appendChild(toast);

            // Auto remove after duration
            if (duration > 0) {
                setTimeout(() => {
                    removeToast(toastId);
                }, duration);
            }

            return toastId;
        }

        function removeToast(toastId) {
            const toast = document.getElementById(toastId);
            if (toast) {
                toast.classList.add('removing');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }
        }

        function displayUploadedFile(file) {
            const uploadedFilesDiv = document.getElementById('uploadedFiles');
            const fileSize = (file.size / 1024 / 1024).toFixed(2);
            const fileIcon = getFileIcon(file.type, file.name);

            // Add file type badge
            const fileTypeBadge = getFileTypeBadge(file.name);

            uploadedFilesDiv.innerHTML = `
                <div class="uploaded-file">
                    <div class="uploaded-file-icon">
                        <i class="fas fa-file-${fileIcon}"></i>
                    </div>
                    <div class="uploaded-file-info">
                        <div class="uploaded-file-name">
                            ${file.name}
                            ${fileTypeBadge}
                        </div>
                        <div class="uploaded-file-size">${fileSize} MB</div>
                    </div>
                    <button type="button" class="remove-file-btn" onclick="removeFile()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        }

        function getFileTypeBadge(fileName) {
            const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
            const badges = {
                '.pdf': '<span class="file-type-badge pdf-badge">PDF</span>',
                '.doc': '<span class="file-type-badge doc-badge">DOC</span>',
                '.docx': '<span class="file-type-badge docx-badge">DOCX</span>',
                '.csv': '<span class="file-type-badge csv-badge">CSV</span>',
                '.xls': '<span class="file-type-badge xls-badge">XLS</span>',
                '.xlsx': '<span class="file-type-badge xlsx-badge">XLSX</span>'
            };
            return badges[extension] || '';
        }

        function getFileIcon(fileType, fileName = '') {
            const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));

            // PDF files
            if (fileType.includes('pdf') || extension === '.pdf') {
                return 'pdf';
            }

            // Word documents
            if (fileType.includes('word') || fileType.includes('wordprocessingml') ||
                extension === '.doc' || extension === '.docx') {
                return 'word';
            }

            // Excel files
            if (fileType.includes('excel') || fileType.includes('spreadsheetml') ||
                extension === '.xls' || extension === '.xlsx') {
                return 'excel';
            }

            // CSV files
            if (fileType.includes('csv') || extension === '.csv') {
                return 'csv';
            }

            // Default file icon
            return 'alt';
        }

        function removeFile() {
            uploadedFile = null;
            document.getElementById('uploadedFiles').innerHTML = '';
            document.getElementById('fileInput').value = '';
        }

        async function processWorkflow(formData) {
            setProcessing(true);

            try {
                switch(currentWorkflowStage) {
                    case 'evaluation':
                        await processEvaluationStage(formData);
                        break;
                    case 'selection':
                        await processSelectionStage(formData);
                        break;
                    case 'analysis':
                        await processAnalysisStage(formData);
                        break;
                    default:
                        await processDefaultAnalysis(formData);
                }

                // Show success toast when stage completes
                showToast('success', '阶段完成', '当前分析阶段已成功完成');

            } catch (error) {
                console.error('Error processing workflow:', error);
                showToast('error', '处理失败', '分析过程中出现错误，请稍后重试');
                displayError('分析过程中出现错误，请稍后重试。');
            } finally {
                setProcessing(false);
                updateButtonProcessingState(false);
                hideProgress();
            }
        }

        async function processEvaluationStage(formData) {
            showProgress(0, '分析用户需求...');
            await new Promise(resolve => setTimeout(resolve, 1000));

            showProgress(30, '评估合作伙伴需求...');
            await new Promise(resolve => setTimeout(resolve, 1500));

            showProgress(60, '生成招募建议...');
            await new Promise(resolve => setTimeout(resolve, 1500));

            showProgress(100, '评估完成！');

            const result = await callDifyWorkflow(formData);
            displayEvaluationResults(result);
            updateWorkflowStage('selection');
        }

        async function processSelectionStage(formData) {
            showProgress(0, '搜索潜在合作伙伴...');
            await new Promise(resolve => setTimeout(resolve, 1000));

            showProgress(50, '匹配合作条件...');
            await new Promise(resolve => setTimeout(resolve, 1500));

            showProgress(100, '选择完成！');

            const result = await callDifyWorkflow(formData);
            displaySelectionResults(result);
            updateWorkflowStage('analysis');
        }

        async function processAnalysisStage(formData) {
            showProgress(0, '准备分析...');
            await new Promise(resolve => setTimeout(resolve, 500));

            showProgress(20, '上传文件...');
            await new Promise(resolve => setTimeout(resolve, 1000));

            showProgress(40, '解析合同内容...');
            await new Promise(resolve => setTimeout(resolve, 1500));

            showProgress(60, 'AI分析中...');
            await new Promise(resolve => setTimeout(resolve, 2000));

            showProgress(80, '生成报告...');
            await new Promise(resolve => setTimeout(resolve, 1000));

            showProgress(100, '分析完成！');

            const result = await callDifyWorkflow(formData);
            displayResults(result);
        }

        async function processDefaultAnalysis(formData) {
            showProgress(0, '准备分析...');
            await new Promise(resolve => setTimeout(resolve, 1000));

            showProgress(40, '解析合同内容...');
            await new Promise(resolve => setTimeout(resolve, 1500));

            showProgress(80, 'AI分析中...');
            await new Promise(resolve => setTimeout(resolve, 2000));

            showProgress(100, '分析完成！');

            const result = await callDifyWorkflow(formData);
            displayResults(result);
        }

        function displayEvaluationResults(result) {
            const resultsContainer = document.getElementById('analysisResults');
            resultsContainer.innerHTML = `
                <div class="results-header">
                    <h3><i class="fas fa-search"></i> 合作伙伴需求评估</h3>
                    <div class="results-meta">
                        <span>用户需求: ${userInfo.userRequirement.substring(0, 50)}...</span>
                    </div>
                </div>
                <div class="evaluation-results">
                    <div class="evaluation-section">
                        <h4>招募建议</h4>
                        <p>基于您的需求描述，建议寻找以下类型的合作伙伴：</p>
                        <ul>
                            <li>具有互补技术能力的公司</li>
                            <li>拥有相关市场渠道的企业</li>
                            <li>具备资源整合能力的合作伙伴</li>
                        </ul>
                    </div>
                    <div class="next-step">
                        <p>点击下方按钮继续选择合作伙伴</p>
                    </div>
                </div>
            `;
        }

        function displaySelectionResults(result) {
            const resultsContainer = document.getElementById('analysisResults');
            resultsContainer.innerHTML = `
                <div class="results-header">
                    <h3><i class="fas fa-check-circle"></i> 合作伙伴选择</h3>
                </div>
                <div class="selection-results">
                    <div class="partner-list">
                        <h4>推荐合作伙伴</h4>
                        <div class="partner-item">
                            <h5>科技创新公司A</h5>
                            <p>匹配度: 85% | 技术互补性强</p>
                        </div>
                        <div class="partner-item">
                            <h5>市场渠道公司B</h5>
                            <p>匹配度: 78% | 市场覆盖广泛</p>
                        </div>
                    </div>
                    <div class="next-step">
                        <p>选择完成，点击下方按钮生成详细分析报告</p>
                    </div>
                </div>
            `;
        }

        function setProcessing(processing) {
            isProcessing = processing;
            updateButtonProcessingState(processing);

            if (processing) {
                let loadingText = 'AI分析中...';
                switch(currentWorkflowStage) {
                    case 'evaluation':
                        loadingText = '评估需求中...';
                        break;
                    case 'selection':
                        loadingText = '选择合作伙伴中...';
                        break;
                    case 'analysis':
                        loadingText = '生成报告中...';
                        break;
                }

                const submitButton = document.getElementById('submitButton');
                const buttonText = submitButton.querySelector('.button-text');
                if (buttonText) {
                    buttonText.textContent = loadingText;
                }
            }
        }

        function showProgress(percentage, text) {
            const progressSection = document.getElementById('progressSection');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            progressSection.style.display = 'block';
            progressFill.style.width = percentage + '%';
            progressText.textContent = text;
        }

        function hideProgress() {
            setTimeout(() => {
                document.getElementById('progressSection').style.display = 'none';
            }, 2000);
        }

        function displayResults(result) {
            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.innerHTML = `
                <div class="results-header">
                    <div class="results-title">
                        <i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 8px;"></i>
                        合同分析报告
                    </div>
                    <div style="font-size: 12px; color: var(--gray-500);">
                        ${new Date().toLocaleString('zh-CN')}
                    </div>
                </div>
                <div class="results-content">
                    ${result}
                </div>
            `;
        }

        function displayError(message) {
            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.innerHTML = `
                <div class="results-header">
                    <div class="results-title" style="color: var(--danger-color);">
                        <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
                        分析失败
                    </div>
                </div>
                <div class="results-content" style="color: var(--danger-color);">
                    ${message}
                </div>
            `;
        }

        async function callDifyWorkflow(formData) {
            // For demo purposes, we'll use mock responses when the real API is not available
            const useMockResponse = true; // Set to false when using real Dify API

            if (useMockResponse) {
                return await getMockContractAnalysis(formData);
            }

            // Real Dify API call would go here
            const response = await fetch(`${DIFY_CONFIG.baseUrl}/workflows/run`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${DIFY_CONFIG.apiKey}`,
                },
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data.data?.outputs?.text || data.answer || '合同分析完成。';
        }

        async function getMockContractAnalysis(formData) {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 1000));

            const workField = formData.get('work_field');
            const reviewSubject = formData.get('review_subject');
            const contractPoints = formData.get('contract_points');
            const analysisType = formData.get('analysis_type');

            // Get file information
            const fileName = uploadedFile ? uploadedFile.name : '未知文件';
            const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
            const fileType = getFileTypeDescription(fileExtension);

            // Get file-specific analysis content
            const fileAnalysis = getFileSpecificAnalysis(fileExtension);

            return `
                <div style="background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                    <h3 style="color: var(--primary-color); margin-bottom: 16px;">
                        <i class="fas fa-file-${getFileIcon('', fileName)}"></i>
                        ${fileType}分析概览
                    </h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                        <div style="background: white; padding: 16px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: var(--success-color);">85%</div>
                            <div style="font-size: 12px; color: var(--gray-600);">合规度评分</div>
                        </div>
                        <div style="background: white; padding: 16px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: var(--warning-color);">中等</div>
                            <div style="font-size: 12px; color: var(--gray-600);">风险等级</div>
                        </div>
                        <div style="background: white; padding: 16px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: var(--primary-color);">12</div>
                            <div style="font-size: 12px; color: var(--gray-600);">关键条款</div>
                        </div>
                        <div style="background: white; padding: 16px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: var(--danger-color);">3</div>
                            <div style="font-size: 12px; color: var(--gray-600);">风险点</div>
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 24px;">
                    <h4 style="color: var(--gray-800); margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-info-circle" style="color: var(--primary-color);"></i>
                        基本信息
                    </h4>
                    <div style="background: var(--gray-50); padding: 16px; border-radius: 8px; border-left: 4px solid var(--primary-color);">
                        <p><strong>上传文件：</strong>${fileName} <span style="color: var(--gray-500);">(${fileType})</span></p>
                        <p><strong>文件大小：</strong>${(uploadedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                        <p><strong>工作领域：</strong>${getFieldName(workField)}</p>
                        <p><strong>审查主体：</strong>${reviewSubject}</p>
                        <p><strong>分析类型：</strong>${getAnalysisTypeName(analysisType)}</p>
                        <p><strong>分析时间：</strong>${new Date().toLocaleString('zh-CN')}</p>
                    </div>
                </div>

                <div style="margin-bottom: 24px;">
                    <h4 style="color: var(--gray-800); margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i>
                        主要风险点
                    </h4>
                    <div style="background: rgba(245, 158, 11, 0.1); padding: 16px; border-radius: 8px; border-left: 4px solid var(--warning-color);">
                        <ul style="margin: 0; padding-left: 20px;">
                            ${fileAnalysis.risks.join('')}
                        </ul>
                    </div>
                </div>

                <div style="margin-bottom: 24px;">
                    <h4 style="color: var(--gray-800); margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-lightbulb" style="color: var(--accent-color);"></i>
                        优化建议
                    </h4>
                    <div style="background: rgba(245, 158, 11, 0.1); padding: 16px; border-radius: 8px; border-left: 4px solid var(--accent-color);">
                        <ol style="margin: 0; padding-left: 20px;">
                            ${fileAnalysis.suggestions.join('')}
                        </ol>
                    </div>
                </div>

                <div style="margin-bottom: 24px;">
                    <h4 style="color: var(--gray-800); margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
                        合规检查
                    </h4>
                    <div style="background: rgba(34, 197, 94, 0.1); padding: 16px; border-radius: 8px; border-left: 4px solid var(--success-color);">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 12px;">
                            ${fileAnalysis.compliance.join('')}
                        </div>
                    </div>
                </div>

                <div style="background: var(--gray-50); padding: 16px; border-radius: 8px; border: 1px solid var(--gray-200);">
                    <h4 style="color: var(--gray-800); margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-clipboard-list" style="color: var(--primary-color);"></i>
                        关注要点分析
                    </h4>
                    <div style="background: white; padding: 16px; border-radius: 8px;">
                        <p style="margin-bottom: 12px;"><strong>您提到的关注要点：</strong></p>
                        <div style="background: var(--gray-50); padding: 12px; border-radius: 6px; font-style: italic; color: var(--gray-700);">
                            "${contractPoints}"
                        </div>
                        <p style="margin-top: 12px;"><strong>AI分析结果：</strong></p>
                        <p>根据您提出的关注要点，我们发现合同在这些方面总体表现良好，但仍有改进空间。建议重点关注付款条款的执行细节和违约责任的量化标准。</p>
                    </div>
                </div>
            `;
        }

        function getFieldName(field) {
            const fieldNames = {
                'technology': '科技/IT',
                'finance': '金融服务',
                'healthcare': '医疗健康',
                'manufacturing': '制造业',
                'retail': '零售/电商',
                'education': '教育培训',
                'real-estate': '房地产',
                'consulting': '咨询服务',
                'other': '其他'
            };
            return fieldNames[field] || field;
        }

        function getAnalysisTypeName(type) {
            const typeNames = {
                'comprehensive': '全面合同分析',
                'risk-assessment': '风险评估分析',
                'legal-compliance': '法律合规检查',
                'financial-terms': '财务条款分析',
                'liability-review': '责任条款审查'
            };
            return typeNames[type] || type;
        }

        function getFileTypeDescription(extension) {
            const descriptions = {
                '.pdf': 'PDF文档',
                '.doc': 'Word文档',
                '.docx': 'Word文档',
                '.csv': 'CSV数据表',
                '.xls': 'Excel表格',
                '.xlsx': 'Excel表格'
            };
            return descriptions[extension] || '未知格式';
        }

        function getFileSpecificAnalysis(extension) {
            switch(extension) {
                case '.csv':
                case '.xls':
                case '.xlsx':
                    return {
                        risks: [
                            '<li style="margin-bottom: 8px;"><strong>数据完整性：</strong>表格中存在部分空值，建议补充完整数据</li>',
                            '<li style="margin-bottom: 8px;"><strong>数据格式：</strong>部分数值格式不统一，建议标准化处理</li>',
                            '<li style="margin-bottom: 8px;"><strong>数据安全：</strong>敏感信息未加密，建议增加数据保护措施</li>'
                        ],
                        suggestions: [
                            '<li style="margin-bottom: 8px;">建议对数据进行清洗和验证，确保数据质量</li>',
                            '<li style="margin-bottom: 8px;">建议增加数据字典，明确各字段含义和格式要求</li>',
                            '<li style="margin-bottom: 8px;">建议实施数据备份策略，防止数据丢失</li>',
                            '<li style="margin-bottom: 8px;">建议建立数据访问权限控制机制</li>'
                        ],
                        compliance: [
                            '<div style="display: flex; align-items: center; gap: 8px;"><i class="fas fa-check" style="color: var(--success-color);"></i><span>数据结构规范</span></div>',
                            '<div style="display: flex; align-items: center; gap: 8px;"><i class="fas fa-check" style="color: var(--success-color);"></i><span>文件格式标准</span></div>',
                            '<div style="display: flex; align-items: center; gap: 8px;"><i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i><span>数据完整性待优化</span></div>',
                            '<div style="display: flex; align-items: center; gap: 8px;"><i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i><span>安全措施需加强</span></div>'
                        ]
                    };
                default:
                    return {
                        risks: [
                            '<li style="margin-bottom: 8px;"><strong>付款条款：</strong>付款周期较长，建议增加逾期付款违约金条款</li>',
                            '<li style="margin-bottom: 8px;"><strong>责任限制：</strong>对方责任限制条款过于宽泛，建议细化</li>',
                            '<li style="margin-bottom: 8px;"><strong>知识产权：</strong>知识产权归属条款需要进一步明确</li>'
                        ],
                        suggestions: [
                            '<li style="margin-bottom: 8px;">建议在第3.2条中增加具体的交付时间节点</li>',
                            '<li style="margin-bottom: 8px;">第7.1条违约责任条款建议增加具体的赔偿标准</li>',
                            '<li style="margin-bottom: 8px;">建议在合同中增加不可抗力条款的详细定义</li>',
                            '<li style="margin-bottom: 8px;">第5.3条保密条款的有效期建议延长至合同终止后2年</li>'
                        ],
                        compliance: [
                            '<div style="display: flex; align-items: center; gap: 8px;"><i class="fas fa-check" style="color: var(--success-color);"></i><span>合同格式规范</span></div>',
                            '<div style="display: flex; align-items: center; gap: 8px;"><i class="fas fa-check" style="color: var(--success-color);"></i><span>法律条款完整</span></div>',
                            '<div style="display: flex; align-items: center; gap: 8px;"><i class="fas fa-check" style="color: var(--success-color);"></i><span>签署要素齐全</span></div>',
                            '<div style="display: flex; align-items: center; gap: 8px;"><i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i><span>部分条款需优化</span></div>'
                        ]
                    };
            }
        }

        // Utility functions
        function resetForm() {
            if (confirm('确定要重置表单吗？所有输入的内容将被清空。')) {
                document.getElementById('workflowForm').reset();
                removeFile();
                document.getElementById('resultsContainer').innerHTML = `
                    <div class="empty-results">
                        <div class="empty-results-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <h3 style="margin-bottom: 8px; color: var(--gray-600);">等待分析结果</h3>
                        <p>上传合同文件并填写相关信息后，AI将为您提供专业的合同分析报告</p>
                    </div>
                `;
            }
        }

        function fillSampleData() {
            // Create a sample file for demonstration
            const sampleFiles = [
                { name: '技术服务合同.pdf', type: 'application/pdf' },
                { name: '销售数据分析.xlsx', type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' },
                { name: '客户信息表.csv', type: 'text/csv' },
                { name: '合作协议.docx', type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }
            ];

            const randomFile = sampleFiles[Math.floor(Math.random() * sampleFiles.length)];
            const fileExtension = randomFile.name.toLowerCase().substring(randomFile.name.lastIndexOf('.'));

            // Create a mock file object
            const mockFile = new File(['sample content'], randomFile.name, { type: randomFile.type });
            uploadedFile = mockFile;
            displayUploadedFile(mockFile);

            // Fill form data based on file type
            document.getElementById('workField').value = 'technology';
            document.getElementById('reviewSubject').value = '北京科技创新有限公司';

            if (fileExtension === '.csv' || fileExtension === '.xls' || fileExtension === '.xlsx') {
                document.getElementById('contractPoints').value = `• 数据完整性和准确性
• 数据格式标准化
• 敏感信息保护措施
• 数据访问权限控制
• 数据备份和恢复策略
• 数据质量监控机制`;
                document.getElementById('analysisType').value = 'risk-assessment';
            } else {
                document.getElementById('contractPoints').value = `• 付款条款和周期
• 违约责任和赔偿标准
• 知识产权归属
• 保密条款的有效期
• 合同终止条件
• 不可抗力条款`;
                document.getElementById('analysisType').value = 'comprehensive';
            }

            document.querySelector('input[name="analysisDepth"][value="detailed"]').checked = true;

            // Show notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--success-color);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: var(--shadow-lg);
                z-index: 10000;
                font-size: 14px;
                animation: slideIn 0.3s ease;
            `;
            notification.innerHTML = `
                <i class="fas fa-check-circle" style="margin-right: 8px;"></i>
                已填充示例数据：${randomFile.name}
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        function previewAnalysis() {
            alert('分析框架预览功能正在开发中，敬请期待！');
        }

        function saveTemplate() {
            alert('模板保存功能正在开发中，敬请期待！');
        }

        function loadTemplate() {
            alert('模板加载功能正在开发中，敬请期待！');
        }

        function showHelp() {
            alert(`使用帮助：

1. 填写用户需求描述（包含公司信息、业务类型、合作目标等）
2. 上传合同文件（支持PDF、DOC、DOCX格式）
   - 上传过程中会显示状态：上传中、成功或失败
3. 选择工作领域和分析类型
4. 填写审查主体和关注要点
5. 点击"开始AI生态分析"按钮
6. 系统将根据您的信息执行不同流程：
   - 评估合作伙伴需求（红色按钮）
   - 选择合作伙伴（绿色按钮）
   - 生成分析报告（蓝色按钮）

支持的文件格式：PDF、DOC、DOCX、CSV、XLS、XLSX
最大文件大小：10MB

如需更多帮助，请联系技术支持。`);
        }

        function exportResults() {
            const resultsContent = document.querySelector('.results-content');
            if (!resultsContent) {
                alert('没有可导出的分析结果');
                return;
            }

            const content = resultsContent.textContent;
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `合同分析报告-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function shareResults() {
            if (navigator.share) {
                navigator.share({
                    title: 'AI合同分析报告',
                    text: '查看我的合同分析结果',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                const url = window.location.href;
                navigator.clipboard.writeText(url).then(() => {
                    alert('链接已复制到剪贴板');
                });
            }
        }

        async function getMockDifyResponse(query) {
            // Legacy function for backward compatibility
            await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 2000));
            return "这是一个模拟响应。";
        }
                'partner-discovery': {
                    keywords: ['发现', '寻找', '合作伙伴', '潜在'],
                    response: `🔍 **合作伙伴发现分析完成**

基于您的需求，我已经分析了市场中的潜在合作伙伴：

**推荐合作伙伴 (Top 5):**
1. **科技创新有限公司** - 匹配度: 92%
   - 专业领域: 企业软件开发
   - 团队规模: 150-200人
   - 合作潜力: 高

2. **数字化解决方案公司** - 匹配度: 88%
   - 专业领域: 数字化转型咨询
   - 团队规模: 80-120人
   - 合作潜力: 高

3. **智能系统集成商** - 匹配度: 85%
   - 专业领域: 系统集成与实施
   - 团队规模: 100-150人
   - 合作潜力: 中高

4. **云服务技术公司** - 匹配度: 82%
   - 专业领域: 云计算与基础设施
   - 团队规模: 60-100人
   - 合作潜力: 中高

5. **行业解决方案提供商** - 匹配度: 78%
   - 专业领域: 垂直行业解决方案
   - 团队规模: 50-80人
   - 合作潜力: 中

**下一步建议:**
- 优先联系前3家公司
- 准备详细的合作提案
- 安排初步会议讨论合作可能性`
                },
                'partner-recruitment': {
                    keywords: ['招募', '策略', '协议', '谈判'],
                    response: `🤝 **合作伙伴招募策略制定完成**

**针对中型软件公司的招募策略:**

**第一阶段: 初步接触 (1-2周)**
- 📧 发送个性化邮件介绍合作机会
- 📞 安排电话沟通，了解对方需求
- 📋 准备公司介绍和合作案例

**第二阶段: 深度交流 (2-3周)**
- 🏢 安排CEO/高管会议
- 📊 展示合作价值和商业模式
- 🔍 进行尽职调查和能力评估

**第三阶段: 协议谈判 (2-4周)**
- 📄 起草合作协议框架
- 💰 讨论收益分配模式
- ⚖️ 法务审核和风险评估

**关键成功因素:**
- 明确双方价值定位
- 建立互信关系
- 制定清晰的合作目标
- 设置合理的里程碑

**预期成果:**
- 签约概率: 75-85%
- 合作启动时间: 6-8周
- 预期年收益: 500万-800万元`
                },
                'training-design': {
                    keywords: ['培训', '教育', '课程', '学习'],
                    response: `📚 **销售团队培训计划设计完成**

**培训计划概览:**

**模块一: 产品知识培训 (第1-2周)**
- 🎯 产品功能和特性深度解析
- 💡 技术优势和差异化卖点
- 📈 市场定位和竞争分析
- 🛠️ 实际操作演示和体验

**模块二: 销售技能提升 (第3-4周)**
- 🗣️ 客户沟通和需求挖掘技巧
- 📋 销售流程和方法论
- 🎭 角色扮演和情景模拟
- 📊 销售数据分析和预测

**模块三: 客户关系管理 (第5-6周)**
- 👥 客户分类和管理策略
- 🔄 售后服务和客户维护
- 📞 客户投诉处理和解决
- 🤝 长期合作关系建立

**培训方式:**
- 📺 在线视频课程 (40%)
- 🏫 现场培训工作坊 (35%)
- 💻 实践操作练习 (15%)
- 📝 考试和认证 (10%)

**预期效果:**
- 产品知识掌握率: 95%+
- 销售技能提升: 40%+
- 客户满意度提升: 25%+
- 销售业绩增长: 30%+`
                },
                'opportunity-analysis': {
                    keywords: ['商机', '分析', '市场', '机会'],
                    response: `💼 **市场商机分析报告**

**当前市场趋势分析:**

**🔥 热门领域:**
1. **数字化转型** - 市场规模: 1200亿元
   - 增长率: 35%/年
   - 机会指数: ⭐⭐⭐⭐⭐

2. **人工智能应用** - 市场规模: 800亿元
   - 增长率: 45%/年
   - 机会指数: ⭐⭐⭐⭐⭐

3. **云计算服务** - 市场规模: 2000亿元
   - 增长率: 28%/年
   - 机会指数: ⭐⭐⭐⭐

**重点商机识别:**

**短期机会 (3-6个月):**
- 🏭 制造业数字化改造项目
- 🏥 医疗信息化升级需求
- 🏫 教育行业在线化转型

**中期机会 (6-12个月):**
- 🏢 企业级AI解决方案
- 🌐 跨境电商平台建设
- 🚗 智能交通系统集成

**长期机会 (1-2年):**
- 🏙️ 智慧城市建设项目
- 🔋 新能源管理系统
- 🌱 ESG合规管理平台

**行动建议:**
- 优先布局AI和数字化转型领域
- 建立行业专家团队
- 加强技术研发投入
- 拓展政企客户关系`
                },
                'market-expansion': {
                    keywords: ['拓展', '市场', '活动', '推广'],
                    response: `📈 **市场拓展策略方案**

**联合营销活动规划:**

**Q1 活动计划:**
🎪 **"数字化转型峰会"**
- 时间: 3月15-16日
- 地点: 上海国际会议中心
- 预期参会: 500+企业高管
- 合作伙伴: 5家技术服务商

🚀 **"AI创新应用展示"**
- 时间: 4月20-22日
- 地点: 北京展览馆
- 预期观众: 2000+专业人士
- 展示内容: 最新AI解决方案

**Q2 活动计划:**
💡 **"行业解决方案研讨会"**
- 覆盖行业: 制造、金融、医疗
- 形式: 线上+线下混合
- 预期商机: 50+项目线索

🌐 **"合作伙伴生态大会"**
- 参与伙伴: 20+核心合作商
- 发布内容: 新合作政策
- 签约目标: 10+新合作协议

**营销效果预测:**
- 品牌曝光: 100万+人次
- 商机线索: 200+个
- 新客户获取: 50+家
- 预期收益: 2000万+元

**成功关键因素:**
- 精准的目标客户定位
- 高质量的内容输出
- 有效的合作伙伴协同
- 完善的后续跟进机制`
                }
            };

            // Find matching response based on workflow type and query content
            const workflowConfig = mockResponses[workflowType];
            if (workflowConfig) {
                return workflowConfig.response;
            }

            // Default response
            return `✅ **工作流执行完成**

基于您的请求"${query}"，我已经完成了相关分析和处理。

**执行结果:**
- 工作流类型: ${document.getElementById('workflowSelect').selectedOptions[0].text}
- 处理时间: ${new Date().toLocaleString('zh-CN')}
- 状态: 成功完成

**建议下一步操作:**
1. 查看详细分析结果
2. 制定具体执行计划
3. 分配相关任务给团队成员
4. 设置进度跟踪和监控

如需更详细的信息或有其他问题，请随时告诉我！`;
        }

        function addMessage(content, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const avatar = document.createElement('div');
            avatar.className = `message-avatar ${type}-avatar`;
            avatar.textContent = type === 'user' ? 'U' : 'AI';
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.innerHTML = content.replace(/\n/g, '<br>');
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function setLoading(loading) {
            isLoading = loading;
            sendButton.disabled = loading;
            
            if (loading) {
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'message ai';
                loadingDiv.id = 'loadingMessage';
                
                loadingDiv.innerHTML = `
                    <div class="message-avatar ai-avatar">AI</div>
                    <div class="message-content">
                        <div class="loading">
                            <span>AI正在思考</span>
                            <div class="loading-dots">
                                <div class="loading-dot"></div>
                                <div class="loading-dot"></div>
                                <div class="loading-dot"></div>
                            </div>
                        </div>
                    </div>
                `;
                
                chatMessages.appendChild(loadingDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            } else {
                const loadingMessage = document.getElementById('loadingMessage');
                if (loadingMessage) {
                    loadingMessage.remove();
                }
            }
        }

        function quickAction(action) {
            const actions = {
                discover: '请帮我发现5个潜在的合作伙伴，重点关注技术服务领域',
                recruit: '为一家中型软件公司制定合作伙伴招募策略',
                train: '设计一个针对销售团队的产品培训计划',
                analyze: '分析当前市场中的商机和趋势',
                report: '生成本月合作伙伴开发工作总结报告'
            };

            if (actions[action]) {
                messageInput.value = actions[action];
                sendMessage();
            }
        }

        function clearChat() {
            if (confirm('确定要清空所有对话记录吗？')) {
                chatMessages.innerHTML = `
                    <div class="message ai">
                        <div class="message-avatar ai-avatar">AI</div>
                        <div class="message-content">
                            👋 欢迎使用EcoBuilder AI工作流！我是您的智能助手，可以帮助您：<br><br>
                            🔍 发现潜在合作伙伴<br>
                            🤝 制定招募策略<br>
                            📚 设计培训方案<br>
                            💼 管理商机流程<br>
                            📈 分析市场机会<br><br>
                            请告诉我您需要什么帮助？
                        </div>
                    </div>
                `;
                conversationId = null;
                clearWorkflowResults();
            }
        }

        function addWorkflowResult(title, content, timestamp = new Date()) {
            const resultsContainer = document.getElementById('workflowResults');

            // Remove empty state if it exists
            const emptyState = resultsContainer.querySelector('.empty-state');
            if (emptyState) {
                emptyState.remove();
            }

            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';
            resultItem.innerHTML = `
                <div class="result-header">
                    <div class="result-title">${title}</div>
                    <div class="result-timestamp">${timestamp.toLocaleString('zh-CN')}</div>
                </div>
                <div class="result-content">${content}</div>
            `;

            resultsContainer.insertBefore(resultItem, resultsContainer.firstChild);
        }

        function clearWorkflowResults() {
            const resultsContainer = document.getElementById('workflowResults');
            resultsContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-play-circle" style="font-size: 48px; color: var(--gray-300); margin-bottom: 16px;"></i>
                    <p style="color: var(--gray-500); text-align: center;">运行工作流后，结果将在这里显示</p>
                </div>
            `;
        }

        function exportResults() {
            const results = document.querySelectorAll('.result-item');
            if (results.length === 0) {
                alert('没有可导出的结果');
                return;
            }

            let exportData = 'EcoBuilder × Dify 工作流执行结果\n';
            exportData += '=' + '='.repeat(40) + '\n\n';

            results.forEach((result, index) => {
                const title = result.querySelector('.result-title').textContent;
                const timestamp = result.querySelector('.result-timestamp').textContent;
                const content = result.querySelector('.result-content').textContent;

                exportData += `${index + 1}. ${title}\n`;
                exportData += `时间: ${timestamp}\n`;
                exportData += `内容: ${content}\n\n`;
            });

            // Create and download file
            const blob = new Blob([exportData], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ecobuilder-dify-results-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Simulate real-time updates
        setInterval(() => {
            const pulse = document.querySelector('.status-pulse');
            if (pulse) {
                pulse.style.animation = 'none';
                setTimeout(() => {
                    pulse.style.animation = 'pulse 2s infinite';
                }, 10);
            }
        }, 5000);
    </script>
</body>
</html>

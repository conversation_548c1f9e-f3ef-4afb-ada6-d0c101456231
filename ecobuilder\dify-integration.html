<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI合同专家 × Dify智能分析平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --warning-color: #f97316;
            --success-color: #22c55e;
            --dify-color: #6366f1;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--gray-800);
            line-height: 1.6;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px 32px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .logo {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .dify-logo {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--dify-color), #8b5cf6);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
        }

        .title-section h1 {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 4px;
        }

        .title-section p {
            color: var(--gray-600);
            font-size: 14px;
        }

        .status-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .status-online {
            background: rgba(34, 197, 94, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        .status-pulse {
            width: 8px;
            height: 8px;
            background: var(--success-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Main Grid Layout */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 24px;
            margin-bottom: 24px;
        }

        /* Workflow Panel */
        .workflow-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--gray-200);
        }

        .panel-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--gray-900);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .panel-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        /* Modern Input Form */
        .input-form {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            margin-bottom: 24px;
        }

        .form-section {
            margin-bottom: 24px;
        }

        .form-section:last-child {
            margin-bottom: 0;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: var(--gray-50);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
            font-family: inherit;
        }

        /* File Upload */
        .file-upload-area {
            border: 2px dashed var(--gray-300);
            border-radius: 12px;
            padding: 32px;
            text-align: center;
            background: var(--gray-50);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .file-upload-area:hover {
            border-color: var(--primary-color);
            background: rgba(37, 99, 235, 0.05);
        }

        .file-upload-area.dragover {
            border-color: var(--primary-color);
            background: rgba(37, 99, 235, 0.1);
            transform: scale(1.02);
        }

        .file-upload-icon {
            font-size: 48px;
            color: var(--gray-400);
            margin-bottom: 16px;
        }

        .file-upload-text {
            color: var(--gray-600);
            font-size: 16px;
            margin-bottom: 8px;
        }

        .file-upload-hint {
            color: var(--gray-500);
            font-size: 12px;
        }

        .file-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .uploaded-file {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
            border-radius: 8px;
            margin-top: 12px;
        }

        .uploaded-file-icon {
            color: var(--success-color);
            font-size: 18px;
        }

        .uploaded-file-info {
            flex: 1;
        }

        .uploaded-file-name {
            font-weight: 500;
            color: var(--gray-800);
            font-size: 14px;
        }

        .uploaded-file-size {
            color: var(--gray-600);
            font-size: 12px;
        }

        .remove-file-btn {
            background: none;
            border: none;
            color: var(--gray-500);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: color 0.2s;
        }

        .remove-file-btn:hover {
            color: var(--danger-color);
        }

        /* Submit Button */
        .submit-button {
            width: 100%;
            padding: 16px 24px;
            background: linear-gradient(135deg, var(--primary-color), var(--dify-color));
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submit-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }

        .submit-button:disabled {
            background: var(--gray-400);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .submit-button .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        .submit-button.loading .loading-spinner {
            display: inline-block;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Results Display */
        .results-container {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            min-height: 300px;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--gray-200);
        }

        .results-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
        }

        .results-content {
            color: var(--gray-700);
            line-height: 1.6;
        }

        .empty-results {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 250px;
            color: var(--gray-500);
        }

        .empty-results-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        /* Control Panel */
        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .workflow-selector {
            margin-bottom: 24px;
        }

        .workflow-selector label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 8px;
        }

        .workflow-select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            font-size: 14px;
            background: white;
            outline: none;
        }

        .api-status {
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            font-size: 14px;
        }

        .api-status.connected {
            background: rgba(34, 197, 94, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        .api-status.disconnected {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .quick-actions {
            display: grid;
            gap: 12px;
        }

        .action-button {
            padding: 12px 16px;
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            background: white;
            color: var(--gray-700);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            text-align: left;
        }

        .action-button:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .action-button i {
            margin-right: 8px;
            width: 16px;
        }

        /* Loading Animation */
        .loading {
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .loading-dots {
            display: flex;
            gap: 4px;
        }

        .loading-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--gray-400);
            animation: loading 1.4s infinite ease-in-out;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes loading {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        /* Workflow Results */
        .workflow-results {
            min-height: 200px;
            padding: 16px;
            background: var(--gray-50);
            border-radius: 8px;
            border: 1px solid var(--gray-200);
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
        }

        .result-item {
            background: white;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 12px;
            border: 1px solid var(--gray-200);
            box-shadow: var(--shadow-sm);
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .result-title {
            font-weight: 600;
            color: var(--gray-900);
        }

        .result-timestamp {
            font-size: 12px;
            color: var(--gray-500);
        }

        .result-content {
            color: var(--gray-700);
            line-height: 1.5;
        }

        /* Integration Grid */
        .integration-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
        }

        .integration-item {
            background: white;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.2s;
        }

        .integration-item:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
        }

        .integration-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            flex-shrink: 0;
        }

        .integration-info {
            flex: 1;
        }

        .integration-info h4 {
            font-size: 14px;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 4px;
        }

        .integration-info p {
            font-size: 12px;
            color: var(--gray-600);
            margin-bottom: 8px;
        }

        .integration-status {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .integration-status.active {
            color: var(--success-color);
        }

        .integration-status.inactive {
            color: var(--gray-400);
        }

        /* Progress Bar */
        .progress-section {
            margin-top: 20px;
            padding: 16px;
            background: var(--gray-50);
            border-radius: 8px;
            border: 1px solid var(--gray-200);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
            margin: 8px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            font-size: 12px;
            color: var(--gray-600);
            text-align: center;
        }

        /* Enhanced Form Styles */
        .form-input[type="radio"] {
            width: auto;
            margin-right: 6px;
        }

        .form-section label[style*="flex"] {
            margin-bottom: 0;
            cursor: pointer;
        }

        /* File Upload Animations */
        @keyframes uploadPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .file-upload-area.uploading {
            animation: uploadPulse 1s ease-in-out infinite;
            border-color: var(--primary-color);
            background: rgba(37, 99, 235, 0.1);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .control-panel {
                order: -1;
            }

            .integration-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 12px;
            }
            
            .header {
                padding: 16px 20px;
            }
            
            .header-content {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .title-section h1 {
                font-size: 24px;
            }
            
            .chat-container {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo">E</div>
                    <div style="font-size: 24px; color: var(--gray-400);">×</div>
                    <div class="dify-logo">D</div>
                    <div class="title-section">
                        <h1>AI合同专家 × Dify智能分析</h1>
                        <p>专业的AI驱动合同审查与风险分析平台</p>
                    </div>
                </div>
                <div class="status-section">
                    <div class="status-indicator status-online">
                        <div class="status-pulse"></div>
                        <span>Dify API 已连接</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Dashboard -->
        <div class="dashboard-grid">
            <!-- Workflow Input Panel -->
            <div class="workflow-panel">
                <div class="panel-header">
                    <div class="panel-title">
                        <div class="panel-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        AI合同专家工作流
                    </div>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="font-size: 12px; color: var(--gray-500);">
                            Powered by Dify v1.6.0
                        </div>
                        <button class="action-button" onclick="resetForm()" style="padding: 6px 12px; font-size: 12px;">
                            <i class="fas fa-refresh"></i>
                            重置表单
                        </button>
                    </div>
                </div>

                <div class="input-form">
                    <form id="workflowForm">
                        <!-- 文件上传区域 -->
                        <div class="form-section">
                            <label class="form-label">
                                <i class="fas fa-upload"></i>
                                上传合同文件
                            </label>
                            <div class="file-upload-area" id="fileUploadArea">
                                <div class="file-upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="file-upload-text">点击上传或拖拽文件到此处</div>
                                <div class="file-upload-hint">支持 PDF, DOC, DOCX 格式，最大 10MB</div>
                                <input type="file" class="file-input" id="fileInput" accept=".pdf,.doc,.docx" />
                            </div>
                            <div id="uploadedFiles"></div>
                        </div>

                        <!-- 工作领域 -->
                        <div class="form-section">
                            <label class="form-label" for="workField">
                                <i class="fas fa-briefcase"></i>
                                工作领域
                            </label>
                            <select class="form-input" id="workField" required>
                                <option value="">请选择工作领域</option>
                                <option value="technology">科技/IT</option>
                                <option value="finance">金融服务</option>
                                <option value="healthcare">医疗健康</option>
                                <option value="manufacturing">制造业</option>
                                <option value="retail">零售/电商</option>
                                <option value="education">教育培训</option>
                                <option value="real-estate">房地产</option>
                                <option value="consulting">咨询服务</option>
                                <option value="other">其他</option>
                            </select>
                        </div>

                        <!-- 审查主体 -->
                        <div class="form-section">
                            <label class="form-label" for="reviewSubject">
                                <i class="fas fa-user-tie"></i>
                                审查主体
                            </label>
                            <input type="text" class="form-input" id="reviewSubject"
                                   placeholder="请输入审查主体名称（如：公司名称、部门等）" required />
                        </div>

                        <!-- 合同要点 -->
                        <div class="form-section">
                            <label class="form-label" for="contractPoints">
                                <i class="fas fa-list-ul"></i>
                                合同要点
                            </label>
                            <textarea class="form-input form-textarea" id="contractPoints"
                                      placeholder="请详细描述需要重点关注的合同条款和要点，例如：&#10;• 付款条款和周期&#10;• 违约责任和赔偿&#10;• 知识产权归属&#10;• 保密条款&#10;• 终止条件等"
                                      required></textarea>
                        </div>

                        <!-- 提交按钮 -->
                        <button type="submit" class="submit-button" id="submitButton">
                            <div class="loading-spinner"></div>
                            <i class="fas fa-magic"></i>
                            开始AI合同分析
                        </button>
                    </form>
                </div>
            </div>

            <!-- Control Panel -->
            <div class="control-panel">
                <div class="panel-header">
                    <div class="panel-title">
                        <div class="panel-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        分析设置
                    </div>
                </div>

                <div class="api-status connected" id="apiStatus">
                    <i class="fas fa-check-circle"></i>
                    Dify API 连接正常
                </div>

                <div class="workflow-selector">
                    <label for="analysisType">分析类型</label>
                    <select class="workflow-select" id="analysisType">
                        <option value="comprehensive">全面合同分析</option>
                        <option value="risk-assessment">风险评估分析</option>
                        <option value="legal-compliance">法律合规检查</option>
                        <option value="financial-terms">财务条款分析</option>
                        <option value="liability-review">责任条款审查</option>
                    </select>
                </div>

                <div class="form-section">
                    <label class="form-label">分析深度</label>
                    <div style="display: flex; gap: 8px; margin-top: 8px;">
                        <label style="display: flex; align-items: center; gap: 6px; font-size: 14px; font-weight: normal;">
                            <input type="radio" name="analysisDepth" value="basic" checked />
                            基础分析
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; font-size: 14px; font-weight: normal;">
                            <input type="radio" name="analysisDepth" value="detailed" />
                            详细分析
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; font-size: 14px; font-weight: normal;">
                            <input type="radio" name="analysisDepth" value="expert" />
                            专家级
                        </label>
                    </div>
                </div>

                <div class="quick-actions">
                    <button class="action-button" onclick="fillSampleData()">
                        <i class="fas fa-magic"></i>
                        填充示例数据
                    </button>
                    <button class="action-button" onclick="previewAnalysis()">
                        <i class="fas fa-eye"></i>
                        预览分析框架
                    </button>
                    <button class="action-button" onclick="saveTemplate()">
                        <i class="fas fa-save"></i>
                        保存为模板
                    </button>
                    <button class="action-button" onclick="loadTemplate()">
                        <i class="fas fa-folder-open"></i>
                        加载模板
                    </button>
                    <button class="action-button" onclick="showHelp()">
                        <i class="fas fa-question-circle"></i>
                        使用帮助
                    </button>
                </div>

                <!-- 进度指示器 -->
                <div class="progress-section" id="progressSection" style="display: none;">
                    <label class="form-label">分析进度</label>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备中...</div>
                </div>
            </div>
        </div>

        <!-- Analysis Results Panel -->
        <div class="workflow-panel" style="margin-top: 24px;">
            <div class="panel-header">
                <div class="panel-title">
                    <div class="panel-icon">
                        <i class="fas fa-analytics"></i>
                    </div>
                    AI合同分析结果
                </div>
                <div style="display: flex; gap: 8px;">
                    <button class="action-button" onclick="exportResults()" style="padding: 8px 16px; font-size: 12px;">
                        <i class="fas fa-download"></i>
                        导出报告
                    </button>
                    <button class="action-button" onclick="shareResults()" style="padding: 8px 16px; font-size: 12px;">
                        <i class="fas fa-share"></i>
                        分享结果
                    </button>
                </div>
            </div>

            <div class="results-container" id="resultsContainer">
                <div class="empty-results">
                    <div class="empty-results-icon">
                        <i class="fas fa-file-contract"></i>
                    </div>
                    <h3 style="margin-bottom: 8px; color: var(--gray-600);">等待分析结果</h3>
                    <p>上传合同文件并填写相关信息后，AI将为您提供专业的合同分析报告</p>
                </div>
            </div>
        </div>

        <!-- EcoBuilder Integration Status -->
        <div class="workflow-panel" style="margin-top: 24px;">
            <div class="panel-header">
                <div class="panel-title">
                    <div class="panel-icon">
                        <i class="fas fa-network-wired"></i>
                    </div>
                    EcoBuilder集成状态
                </div>
            </div>

            <div class="integration-grid">
                <div class="integration-item">
                    <div class="integration-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="integration-info">
                        <h4>PartnerScout</h4>
                        <p>合作伙伴发现智能体</p>
                        <div class="integration-status active">
                            <i class="fas fa-check-circle"></i>
                            已集成
                        </div>
                    </div>
                </div>

                <div class="integration-item">
                    <div class="integration-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="integration-info">
                        <h4>PartnerRecruiter</h4>
                        <p>合作伙伴招募智能体</p>
                        <div class="integration-status active">
                            <i class="fas fa-check-circle"></i>
                            已集成
                        </div>
                    </div>
                </div>

                <div class="integration-item">
                    <div class="integration-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="integration-info">
                        <h4>PartnerTrainer</h4>
                        <p>合作伙伴培训智能体</p>
                        <div class="integration-status active">
                            <i class="fas fa-check-circle"></i>
                            已集成
                        </div>
                    </div>
                </div>

                <div class="integration-item">
                    <div class="integration-icon">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <div class="integration-info">
                        <h4>OpportunityManager</h4>
                        <p>商机管理智能体</p>
                        <div class="integration-status active">
                            <i class="fas fa-check-circle"></i>
                            已集成
                        </div>
                    </div>
                </div>

                <div class="integration-item">
                    <div class="integration-icon">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </div>
                    <div class="integration-info">
                        <h4>MarketExpander</h4>
                        <p>市场拓展智能体</p>
                        <div class="integration-status active">
                            <i class="fas fa-check-circle"></i>
                            已集成
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dify API Configuration
        const DIFY_CONFIG = {
            apiKey: 'app-ZRuKpcHUrE5E0zTtNQLddDCc',
            baseUrl: 'https://api.dify.ai/v1',
            workflowId: '8cc0dc2f-37ed-4dbd-b647-0fc278810788'
        };

        // Form functionality
        let isProcessing = false;
        let uploadedFile = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkDifyConnection();
            setupEventListeners();
        });

        function setupEventListeners() {
            const form = document.getElementById('workflowForm');
            const fileInput = document.getElementById('fileInput');
            const fileUploadArea = document.getElementById('fileUploadArea');

            // Form submission
            form.addEventListener('submit', handleFormSubmit);

            // File upload
            fileInput.addEventListener('change', handleFileSelect);

            // Drag and drop
            fileUploadArea.addEventListener('dragover', handleDragOver);
            fileUploadArea.addEventListener('dragleave', handleDragLeave);
            fileUploadArea.addEventListener('drop', handleFileDrop);
        }

        function handleFormSubmit(e) {
            e.preventDefault();
            if (isProcessing) return;

            const formData = new FormData();
            const workField = document.getElementById('workField').value;
            const reviewSubject = document.getElementById('reviewSubject').value;
            const contractPoints = document.getElementById('contractPoints').value;
            const analysisType = document.getElementById('analysisType').value;
            const analysisDepth = document.querySelector('input[name="analysisDepth"]:checked').value;

            if (!uploadedFile) {
                alert('请先上传合同文件');
                return;
            }

            if (!workField || !reviewSubject || !contractPoints) {
                alert('请填写所有必填字段');
                return;
            }

            // Prepare form data
            formData.append('file', uploadedFile);
            formData.append('work_field', workField);
            formData.append('review_subject', reviewSubject);
            formData.append('contract_points', contractPoints);
            formData.append('analysis_type', analysisType);
            formData.append('analysis_depth', analysisDepth);

            processWorkflow(formData);
        }

        async function checkDifyConnection() {
            try {
                const response = await fetch(`${DIFY_CONFIG.baseUrl}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${DIFY_CONFIG.apiKey}`
                    }
                });
                
                if (response.ok) {
                    updateApiStatus(true, 'Dify API 连接正常');
                } else {
                    updateApiStatus(false, 'Dify API 连接失败');
                }
            } catch (error) {
                updateApiStatus(false, 'Dify API 连接错误');
                console.error('Dify connection error:', error);
            }
        }

        function updateApiStatus(connected, message) {
            const statusElement = document.getElementById('apiStatus');
            statusElement.className = `api-status ${connected ? 'connected' : 'disconnected'}`;
            statusElement.innerHTML = `
                <i class="fas fa-${connected ? 'check-circle' : 'exclamation-triangle'}"></i>
                ${message}
            `;
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                processFile(file);
            }
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.currentTarget.classList.remove('dragover');
        }

        function handleFileDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        }

        function processFile(file) {
            // Validate file type
            const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
            if (!allowedTypes.includes(file.type)) {
                alert('请上传 PDF、DOC 或 DOCX 格式的文件');
                return;
            }

            // Validate file size (10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('文件大小不能超过 10MB');
                return;
            }

            uploadedFile = file;
            displayUploadedFile(file);
        }

        function displayUploadedFile(file) {
            const uploadedFilesDiv = document.getElementById('uploadedFiles');
            const fileSize = (file.size / 1024 / 1024).toFixed(2);

            uploadedFilesDiv.innerHTML = `
                <div class="uploaded-file">
                    <div class="uploaded-file-icon">
                        <i class="fas fa-file-${getFileIcon(file.type)}"></i>
                    </div>
                    <div class="uploaded-file-info">
                        <div class="uploaded-file-name">${file.name}</div>
                        <div class="uploaded-file-size">${fileSize} MB</div>
                    </div>
                    <button type="button" class="remove-file-btn" onclick="removeFile()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        }

        function getFileIcon(fileType) {
            if (fileType.includes('pdf')) return 'pdf';
            if (fileType.includes('word')) return 'word';
            return 'alt';
        }

        function removeFile() {
            uploadedFile = null;
            document.getElementById('uploadedFiles').innerHTML = '';
            document.getElementById('fileInput').value = '';
        }

        async function processWorkflow(formData) {
            setProcessing(true);
            showProgress(0, '准备分析...');

            try {
                // Simulate progress updates
                showProgress(20, '上传文件...');
                await new Promise(resolve => setTimeout(resolve, 1000));

                showProgress(40, '解析合同内容...');
                await new Promise(resolve => setTimeout(resolve, 1500));

                showProgress(60, 'AI分析中...');
                await new Promise(resolve => setTimeout(resolve, 2000));

                showProgress(80, '生成报告...');
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Call Dify API (or mock response)
                const result = await callDifyWorkflow(formData);

                showProgress(100, '分析完成！');
                displayResults(result);

            } catch (error) {
                console.error('Error processing workflow:', error);
                displayError('分析过程中出现错误，请稍后重试。');
            } finally {
                setProcessing(false);
                hideProgress();
            }
        }

        function setProcessing(processing) {
            isProcessing = processing;
            const submitButton = document.getElementById('submitButton');

            if (processing) {
                submitButton.classList.add('loading');
                submitButton.disabled = true;
                submitButton.innerHTML = `
                    <div class="loading-spinner"></div>
                    <span>AI分析中...</span>
                `;
            } else {
                submitButton.classList.remove('loading');
                submitButton.disabled = false;
                submitButton.innerHTML = `
                    <i class="fas fa-magic"></i>
                    开始AI合同分析
                `;
            }
        }

        function showProgress(percentage, text) {
            const progressSection = document.getElementById('progressSection');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            progressSection.style.display = 'block';
            progressFill.style.width = percentage + '%';
            progressText.textContent = text;
        }

        function hideProgress() {
            setTimeout(() => {
                document.getElementById('progressSection').style.display = 'none';
            }, 2000);
        }

        function displayResults(result) {
            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.innerHTML = `
                <div class="results-header">
                    <div class="results-title">
                        <i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 8px;"></i>
                        合同分析报告
                    </div>
                    <div style="font-size: 12px; color: var(--gray-500);">
                        ${new Date().toLocaleString('zh-CN')}
                    </div>
                </div>
                <div class="results-content">
                    ${result}
                </div>
            `;
        }

        function displayError(message) {
            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.innerHTML = `
                <div class="results-header">
                    <div class="results-title" style="color: var(--danger-color);">
                        <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
                        分析失败
                    </div>
                </div>
                <div class="results-content" style="color: var(--danger-color);">
                    ${message}
                </div>
            `;
        }

        async function callDifyWorkflow(formData) {
            // For demo purposes, we'll use mock responses when the real API is not available
            const useMockResponse = true; // Set to false when using real Dify API

            if (useMockResponse) {
                return await getMockContractAnalysis(formData);
            }

            // Real Dify API call would go here
            const response = await fetch(`${DIFY_CONFIG.baseUrl}/workflows/run`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${DIFY_CONFIG.apiKey}`,
                },
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data.data?.outputs?.text || data.answer || '合同分析完成。';
        }

        async function getMockContractAnalysis(formData) {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 1000));

            const workField = formData.get('work_field');
            const reviewSubject = formData.get('review_subject');
            const contractPoints = formData.get('contract_points');
            const analysisType = formData.get('analysis_type');

            return `
                <div style="background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                    <h3 style="color: var(--primary-color); margin-bottom: 16px;">
                        <i class="fas fa-file-contract"></i>
                        合同分析概览
                    </h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                        <div style="background: white; padding: 16px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: var(--success-color);">85%</div>
                            <div style="font-size: 12px; color: var(--gray-600);">合规度评分</div>
                        </div>
                        <div style="background: white; padding: 16px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: var(--warning-color);">中等</div>
                            <div style="font-size: 12px; color: var(--gray-600);">风险等级</div>
                        </div>
                        <div style="background: white; padding: 16px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: var(--primary-color);">12</div>
                            <div style="font-size: 12px; color: var(--gray-600);">关键条款</div>
                        </div>
                        <div style="background: white; padding: 16px; border-radius: 8px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: var(--danger-color);">3</div>
                            <div style="font-size: 12px; color: var(--gray-600);">风险点</div>
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 24px;">
                    <h4 style="color: var(--gray-800); margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-info-circle" style="color: var(--primary-color);"></i>
                        基本信息
                    </h4>
                    <div style="background: var(--gray-50); padding: 16px; border-radius: 8px; border-left: 4px solid var(--primary-color);">
                        <p><strong>工作领域：</strong>${getFieldName(workField)}</p>
                        <p><strong>审查主体：</strong>${reviewSubject}</p>
                        <p><strong>分析类型：</strong>${getAnalysisTypeName(analysisType)}</p>
                        <p><strong>分析时间：</strong>${new Date().toLocaleString('zh-CN')}</p>
                    </div>
                </div>

                <div style="margin-bottom: 24px;">
                    <h4 style="color: var(--gray-800); margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i>
                        主要风险点
                    </h4>
                    <div style="background: rgba(245, 158, 11, 0.1); padding: 16px; border-radius: 8px; border-left: 4px solid var(--warning-color);">
                        <ul style="margin: 0; padding-left: 20px;">
                            <li style="margin-bottom: 8px;"><strong>付款条款：</strong>付款周期较长，建议增加逾期付款违约金条款</li>
                            <li style="margin-bottom: 8px;"><strong>责任限制：</strong>对方责任限制条款过于宽泛，建议细化</li>
                            <li style="margin-bottom: 8px;"><strong>知识产权：</strong>知识产权归属条款需要进一步明确</li>
                        </ul>
                    </div>
                </div>

                <div style="margin-bottom: 24px;">
                    <h4 style="color: var(--gray-800); margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-lightbulb" style="color: var(--accent-color);"></i>
                        优化建议
                    </h4>
                    <div style="background: rgba(245, 158, 11, 0.1); padding: 16px; border-radius: 8px; border-left: 4px solid var(--accent-color);">
                        <ol style="margin: 0; padding-left: 20px;">
                            <li style="margin-bottom: 8px;">建议在第3.2条中增加具体的交付时间节点</li>
                            <li style="margin-bottom: 8px;">第7.1条违约责任条款建议增加具体的赔偿标准</li>
                            <li style="margin-bottom: 8px;">建议在合同中增加不可抗力条款的详细定义</li>
                            <li style="margin-bottom: 8px;">第5.3条保密条款的有效期建议延长至合同终止后2年</li>
                        </ol>
                    </div>
                </div>

                <div style="margin-bottom: 24px;">
                    <h4 style="color: var(--gray-800); margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
                        合规检查
                    </h4>
                    <div style="background: rgba(34, 197, 94, 0.1); padding: 16px; border-radius: 8px; border-left: 4px solid var(--success-color);">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 12px;">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-check" style="color: var(--success-color);"></i>
                                <span>合同格式规范</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-check" style="color: var(--success-color);"></i>
                                <span>法律条款完整</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-check" style="color: var(--success-color);"></i>
                                <span>签署要素齐全</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i>
                                <span>部分条款需优化</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="background: var(--gray-50); padding: 16px; border-radius: 8px; border: 1px solid var(--gray-200);">
                    <h4 style="color: var(--gray-800); margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-clipboard-list" style="color: var(--primary-color);"></i>
                        关注要点分析
                    </h4>
                    <div style="background: white; padding: 16px; border-radius: 8px;">
                        <p style="margin-bottom: 12px;"><strong>您提到的关注要点：</strong></p>
                        <div style="background: var(--gray-50); padding: 12px; border-radius: 6px; font-style: italic; color: var(--gray-700);">
                            "${contractPoints}"
                        </div>
                        <p style="margin-top: 12px;"><strong>AI分析结果：</strong></p>
                        <p>根据您提出的关注要点，我们发现合同在这些方面总体表现良好，但仍有改进空间。建议重点关注付款条款的执行细节和违约责任的量化标准。</p>
                    </div>
                </div>
            `;
        }

        function getFieldName(field) {
            const fieldNames = {
                'technology': '科技/IT',
                'finance': '金融服务',
                'healthcare': '医疗健康',
                'manufacturing': '制造业',
                'retail': '零售/电商',
                'education': '教育培训',
                'real-estate': '房地产',
                'consulting': '咨询服务',
                'other': '其他'
            };
            return fieldNames[field] || field;
        }

        function getAnalysisTypeName(type) {
            const typeNames = {
                'comprehensive': '全面合同分析',
                'risk-assessment': '风险评估分析',
                'legal-compliance': '法律合规检查',
                'financial-terms': '财务条款分析',
                'liability-review': '责任条款审查'
            };
            return typeNames[type] || type;
        }

        // Utility functions
        function resetForm() {
            if (confirm('确定要重置表单吗？所有输入的内容将被清空。')) {
                document.getElementById('workflowForm').reset();
                removeFile();
                document.getElementById('resultsContainer').innerHTML = `
                    <div class="empty-results">
                        <div class="empty-results-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <h3 style="margin-bottom: 8px; color: var(--gray-600);">等待分析结果</h3>
                        <p>上传合同文件并填写相关信息后，AI将为您提供专业的合同分析报告</p>
                    </div>
                `;
            }
        }

        function fillSampleData() {
            document.getElementById('workField').value = 'technology';
            document.getElementById('reviewSubject').value = '北京科技创新有限公司';
            document.getElementById('contractPoints').value = `• 付款条款和周期
• 违约责任和赔偿标准
• 知识产权归属
• 保密条款的有效期
• 合同终止条件
• 不可抗力条款`;
            document.getElementById('analysisType').value = 'comprehensive';
            document.querySelector('input[name="analysisDepth"][value="detailed"]').checked = true;
        }

        function previewAnalysis() {
            alert('分析框架预览功能正在开发中，敬请期待！');
        }

        function saveTemplate() {
            alert('模板保存功能正在开发中，敬请期待！');
        }

        function loadTemplate() {
            alert('模板加载功能正在开发中，敬请期待！');
        }

        function showHelp() {
            alert(`使用帮助：

1. 上传合同文件（支持PDF、DOC、DOCX格式）
2. 选择工作领域和分析类型
3. 填写审查主体和关注要点
4. 点击"开始AI合同分析"按钮
5. 等待AI分析完成并查看结果

如需更多帮助，请联系技术支持。`);
        }

        function exportResults() {
            const resultsContent = document.querySelector('.results-content');
            if (!resultsContent) {
                alert('没有可导出的分析结果');
                return;
            }

            const content = resultsContent.textContent;
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `合同分析报告-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function shareResults() {
            if (navigator.share) {
                navigator.share({
                    title: 'AI合同分析报告',
                    text: '查看我的合同分析结果',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                const url = window.location.href;
                navigator.clipboard.writeText(url).then(() => {
                    alert('链接已复制到剪贴板');
                });
            }
        }

        async function getMockDifyResponse(query) {
            // Legacy function for backward compatibility
            await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 2000));
            return "这是一个模拟响应。";
        }
                'partner-discovery': {
                    keywords: ['发现', '寻找', '合作伙伴', '潜在'],
                    response: `🔍 **合作伙伴发现分析完成**

基于您的需求，我已经分析了市场中的潜在合作伙伴：

**推荐合作伙伴 (Top 5):**
1. **科技创新有限公司** - 匹配度: 92%
   - 专业领域: 企业软件开发
   - 团队规模: 150-200人
   - 合作潜力: 高

2. **数字化解决方案公司** - 匹配度: 88%
   - 专业领域: 数字化转型咨询
   - 团队规模: 80-120人
   - 合作潜力: 高

3. **智能系统集成商** - 匹配度: 85%
   - 专业领域: 系统集成与实施
   - 团队规模: 100-150人
   - 合作潜力: 中高

4. **云服务技术公司** - 匹配度: 82%
   - 专业领域: 云计算与基础设施
   - 团队规模: 60-100人
   - 合作潜力: 中高

5. **行业解决方案提供商** - 匹配度: 78%
   - 专业领域: 垂直行业解决方案
   - 团队规模: 50-80人
   - 合作潜力: 中

**下一步建议:**
- 优先联系前3家公司
- 准备详细的合作提案
- 安排初步会议讨论合作可能性`
                },
                'partner-recruitment': {
                    keywords: ['招募', '策略', '协议', '谈判'],
                    response: `🤝 **合作伙伴招募策略制定完成**

**针对中型软件公司的招募策略:**

**第一阶段: 初步接触 (1-2周)**
- 📧 发送个性化邮件介绍合作机会
- 📞 安排电话沟通，了解对方需求
- 📋 准备公司介绍和合作案例

**第二阶段: 深度交流 (2-3周)**
- 🏢 安排CEO/高管会议
- 📊 展示合作价值和商业模式
- 🔍 进行尽职调查和能力评估

**第三阶段: 协议谈判 (2-4周)**
- 📄 起草合作协议框架
- 💰 讨论收益分配模式
- ⚖️ 法务审核和风险评估

**关键成功因素:**
- 明确双方价值定位
- 建立互信关系
- 制定清晰的合作目标
- 设置合理的里程碑

**预期成果:**
- 签约概率: 75-85%
- 合作启动时间: 6-8周
- 预期年收益: 500万-800万元`
                },
                'training-design': {
                    keywords: ['培训', '教育', '课程', '学习'],
                    response: `📚 **销售团队培训计划设计完成**

**培训计划概览:**

**模块一: 产品知识培训 (第1-2周)**
- 🎯 产品功能和特性深度解析
- 💡 技术优势和差异化卖点
- 📈 市场定位和竞争分析
- 🛠️ 实际操作演示和体验

**模块二: 销售技能提升 (第3-4周)**
- 🗣️ 客户沟通和需求挖掘技巧
- 📋 销售流程和方法论
- 🎭 角色扮演和情景模拟
- 📊 销售数据分析和预测

**模块三: 客户关系管理 (第5-6周)**
- 👥 客户分类和管理策略
- 🔄 售后服务和客户维护
- 📞 客户投诉处理和解决
- 🤝 长期合作关系建立

**培训方式:**
- 📺 在线视频课程 (40%)
- 🏫 现场培训工作坊 (35%)
- 💻 实践操作练习 (15%)
- 📝 考试和认证 (10%)

**预期效果:**
- 产品知识掌握率: 95%+
- 销售技能提升: 40%+
- 客户满意度提升: 25%+
- 销售业绩增长: 30%+`
                },
                'opportunity-analysis': {
                    keywords: ['商机', '分析', '市场', '机会'],
                    response: `💼 **市场商机分析报告**

**当前市场趋势分析:**

**🔥 热门领域:**
1. **数字化转型** - 市场规模: 1200亿元
   - 增长率: 35%/年
   - 机会指数: ⭐⭐⭐⭐⭐

2. **人工智能应用** - 市场规模: 800亿元
   - 增长率: 45%/年
   - 机会指数: ⭐⭐⭐⭐⭐

3. **云计算服务** - 市场规模: 2000亿元
   - 增长率: 28%/年
   - 机会指数: ⭐⭐⭐⭐

**重点商机识别:**

**短期机会 (3-6个月):**
- 🏭 制造业数字化改造项目
- 🏥 医疗信息化升级需求
- 🏫 教育行业在线化转型

**中期机会 (6-12个月):**
- 🏢 企业级AI解决方案
- 🌐 跨境电商平台建设
- 🚗 智能交通系统集成

**长期机会 (1-2年):**
- 🏙️ 智慧城市建设项目
- 🔋 新能源管理系统
- 🌱 ESG合规管理平台

**行动建议:**
- 优先布局AI和数字化转型领域
- 建立行业专家团队
- 加强技术研发投入
- 拓展政企客户关系`
                },
                'market-expansion': {
                    keywords: ['拓展', '市场', '活动', '推广'],
                    response: `📈 **市场拓展策略方案**

**联合营销活动规划:**

**Q1 活动计划:**
🎪 **"数字化转型峰会"**
- 时间: 3月15-16日
- 地点: 上海国际会议中心
- 预期参会: 500+企业高管
- 合作伙伴: 5家技术服务商

🚀 **"AI创新应用展示"**
- 时间: 4月20-22日
- 地点: 北京展览馆
- 预期观众: 2000+专业人士
- 展示内容: 最新AI解决方案

**Q2 活动计划:**
💡 **"行业解决方案研讨会"**
- 覆盖行业: 制造、金融、医疗
- 形式: 线上+线下混合
- 预期商机: 50+项目线索

🌐 **"合作伙伴生态大会"**
- 参与伙伴: 20+核心合作商
- 发布内容: 新合作政策
- 签约目标: 10+新合作协议

**营销效果预测:**
- 品牌曝光: 100万+人次
- 商机线索: 200+个
- 新客户获取: 50+家
- 预期收益: 2000万+元

**成功关键因素:**
- 精准的目标客户定位
- 高质量的内容输出
- 有效的合作伙伴协同
- 完善的后续跟进机制`
                }
            };

            // Find matching response based on workflow type and query content
            const workflowConfig = mockResponses[workflowType];
            if (workflowConfig) {
                return workflowConfig.response;
            }

            // Default response
            return `✅ **工作流执行完成**

基于您的请求"${query}"，我已经完成了相关分析和处理。

**执行结果:**
- 工作流类型: ${document.getElementById('workflowSelect').selectedOptions[0].text}
- 处理时间: ${new Date().toLocaleString('zh-CN')}
- 状态: 成功完成

**建议下一步操作:**
1. 查看详细分析结果
2. 制定具体执行计划
3. 分配相关任务给团队成员
4. 设置进度跟踪和监控

如需更详细的信息或有其他问题，请随时告诉我！`;
        }

        function addMessage(content, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const avatar = document.createElement('div');
            avatar.className = `message-avatar ${type}-avatar`;
            avatar.textContent = type === 'user' ? 'U' : 'AI';
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.innerHTML = content.replace(/\n/g, '<br>');
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function setLoading(loading) {
            isLoading = loading;
            sendButton.disabled = loading;
            
            if (loading) {
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'message ai';
                loadingDiv.id = 'loadingMessage';
                
                loadingDiv.innerHTML = `
                    <div class="message-avatar ai-avatar">AI</div>
                    <div class="message-content">
                        <div class="loading">
                            <span>AI正在思考</span>
                            <div class="loading-dots">
                                <div class="loading-dot"></div>
                                <div class="loading-dot"></div>
                                <div class="loading-dot"></div>
                            </div>
                        </div>
                    </div>
                `;
                
                chatMessages.appendChild(loadingDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            } else {
                const loadingMessage = document.getElementById('loadingMessage');
                if (loadingMessage) {
                    loadingMessage.remove();
                }
            }
        }

        function quickAction(action) {
            const actions = {
                discover: '请帮我发现5个潜在的合作伙伴，重点关注技术服务领域',
                recruit: '为一家中型软件公司制定合作伙伴招募策略',
                train: '设计一个针对销售团队的产品培训计划',
                analyze: '分析当前市场中的商机和趋势',
                report: '生成本月合作伙伴开发工作总结报告'
            };

            if (actions[action]) {
                messageInput.value = actions[action];
                sendMessage();
            }
        }

        function clearChat() {
            if (confirm('确定要清空所有对话记录吗？')) {
                chatMessages.innerHTML = `
                    <div class="message ai">
                        <div class="message-avatar ai-avatar">AI</div>
                        <div class="message-content">
                            👋 欢迎使用EcoBuilder AI工作流！我是您的智能助手，可以帮助您：<br><br>
                            🔍 发现潜在合作伙伴<br>
                            🤝 制定招募策略<br>
                            📚 设计培训方案<br>
                            💼 管理商机流程<br>
                            📈 分析市场机会<br><br>
                            请告诉我您需要什么帮助？
                        </div>
                    </div>
                `;
                conversationId = null;
                clearWorkflowResults();
            }
        }

        function addWorkflowResult(title, content, timestamp = new Date()) {
            const resultsContainer = document.getElementById('workflowResults');

            // Remove empty state if it exists
            const emptyState = resultsContainer.querySelector('.empty-state');
            if (emptyState) {
                emptyState.remove();
            }

            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';
            resultItem.innerHTML = `
                <div class="result-header">
                    <div class="result-title">${title}</div>
                    <div class="result-timestamp">${timestamp.toLocaleString('zh-CN')}</div>
                </div>
                <div class="result-content">${content}</div>
            `;

            resultsContainer.insertBefore(resultItem, resultsContainer.firstChild);
        }

        function clearWorkflowResults() {
            const resultsContainer = document.getElementById('workflowResults');
            resultsContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-play-circle" style="font-size: 48px; color: var(--gray-300); margin-bottom: 16px;"></i>
                    <p style="color: var(--gray-500); text-align: center;">运行工作流后，结果将在这里显示</p>
                </div>
            `;
        }

        function exportResults() {
            const results = document.querySelectorAll('.result-item');
            if (results.length === 0) {
                alert('没有可导出的结果');
                return;
            }

            let exportData = 'EcoBuilder × Dify 工作流执行结果\n';
            exportData += '=' + '='.repeat(40) + '\n\n';

            results.forEach((result, index) => {
                const title = result.querySelector('.result-title').textContent;
                const timestamp = result.querySelector('.result-timestamp').textContent;
                const content = result.querySelector('.result-content').textContent;

                exportData += `${index + 1}. ${title}\n`;
                exportData += `时间: ${timestamp}\n`;
                exportData += `内容: ${content}\n\n`;
            });

            // Create and download file
            const blob = new Blob([exportData], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ecobuilder-dify-results-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Simulate real-time updates
        setInterval(() => {
            const pulse = document.querySelector('.status-pulse');
            if (pulse) {
                pulse.style.animation = 'none';
                setTimeout(() => {
                    pulse.style.animation = 'pulse 2s infinite';
                }, 10);
            }
        }, 5000);
    </script>
</body>
</html>

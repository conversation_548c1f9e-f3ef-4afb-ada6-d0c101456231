import { PrismaClient } from '@prisma/client';
import { 
  Partner, 
  Contact, 
  Opportunity, 
  Activity, 
  Task, 
  Agent, 
  Message, 
  Workflow, 
  WorkflowExecution,
  User
} from '../../../shared/types';
import { Logger } from '../utils/logger';

export class DatabaseService {
  private prisma: PrismaClient;
  private logger: Logger;
  private isInitialized = false;

  constructor() {
    this.logger = new Logger('DatabaseService');
    this.prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    });
  }

  /**
   * Initialize the database service
   */
  async initialize(): Promise<void> {
    this.logger.info('Initializing database service');
    
    try {
      await this.prisma.$connect();
      this.isInitialized = true;
      this.logger.info('Database service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize database service', error);
      throw error;
    }
  }

  /**
   * Check if the service is initialized
   */
  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new Error('Database service not initialized');
    }
  }

  // Partner operations
  async createPartner(data: Omit<Partner, 'id' | 'createdAt' | 'updatedAt'>): Promise<Partner> {
    this.ensureInitialized();
    return await this.prisma.partner.create({ data }) as Partner;
  }

  async getPartner(id: string): Promise<Partner | null> {
    this.ensureInitialized();
    return await this.prisma.partner.findUnique({ 
      where: { id },
      include: {
        contacts: true,
        agreements: true,
        opportunities: true,
        activities: true,
        tasks: true
      }
    }) as Partner | null;
  }

  async updatePartner(id: string, data: Partial<Partner>): Promise<Partner> {
    this.ensureInitialized();
    return await this.prisma.partner.update({ 
      where: { id }, 
      data 
    }) as Partner;
  }

  async deletePartner(id: string): Promise<void> {
    this.ensureInitialized();
    await this.prisma.partner.delete({ where: { id } });
  }

  async getPartners(
    limit: number = 50, 
    offset: number = 0, 
    filters?: Record<string, any>
  ): Promise<Partner[]> {
    this.ensureInitialized();
    return await this.prisma.partner.findMany({
      skip: offset,
      take: limit,
      where: filters,
      include: {
        contacts: true,
        opportunities: true
      }
    }) as Partner[];
  }

  // Contact operations
  async createContact(data: Omit<Contact, 'id' | 'createdAt' | 'updatedAt'>): Promise<Contact> {
    this.ensureInitialized();
    return await this.prisma.contact.create({ data }) as Contact;
  }

  async getContact(id: string): Promise<Contact | null> {
    this.ensureInitialized();
    return await this.prisma.contact.findUnique({ where: { id } }) as Contact | null;
  }

  async updateContact(id: string, data: Partial<Contact>): Promise<Contact> {
    this.ensureInitialized();
    return await this.prisma.contact.update({ where: { id }, data }) as Contact;
  }

  // Opportunity operations
  async createOpportunity(data: Omit<Opportunity, 'id' | 'createdAt' | 'updatedAt'>): Promise<Opportunity> {
    this.ensureInitialized();
    return await this.prisma.opportunity.create({ data }) as Opportunity;
  }

  async getOpportunity(id: string): Promise<Opportunity | null> {
    this.ensureInitialized();
    return await this.prisma.opportunity.findUnique({ 
      where: { id },
      include: {
        activities: true,
        tasks: true
      }
    }) as Opportunity | null;
  }

  async updateOpportunity(id: string, data: Partial<Opportunity>): Promise<Opportunity> {
    this.ensureInitialized();
    return await this.prisma.opportunity.update({ where: { id }, data }) as Opportunity;
  }

  // Activity operations
  async createActivity(data: Omit<Activity, 'id' | 'createdAt' | 'updatedAt'>): Promise<Activity> {
    this.ensureInitialized();
    return await this.prisma.activity.create({ data }) as Activity;
  }

  async getActivity(id: string): Promise<Activity | null> {
    this.ensureInitialized();
    return await this.prisma.activity.findUnique({ where: { id } }) as Activity | null;
  }

  async updateActivity(id: string, data: Partial<Activity>): Promise<Activity> {
    this.ensureInitialized();
    return await this.prisma.activity.update({ where: { id }, data }) as Activity;
  }

  // Task operations
  async createTask(data: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> {
    this.ensureInitialized();
    return await this.prisma.task.create({ data }) as Task;
  }

  async getTask(id: string): Promise<Task | null> {
    this.ensureInitialized();
    return await this.prisma.task.findUnique({ where: { id } }) as Task | null;
  }

  async updateTask(id: string, data: Partial<Task>): Promise<Task> {
    this.ensureInitialized();
    return await this.prisma.task.update({ where: { id }, data }) as Task;
  }

  async getPendingTasks(): Promise<Task[]> {
    this.ensureInitialized();
    return await this.prisma.task.findMany({
      where: { status: 'PENDING' },
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'asc' }
      ]
    }) as Task[];
  }

  async getTasksByAgent(agentId: string): Promise<Task[]> {
    this.ensureInitialized();
    return await this.prisma.task.findMany({
      where: { agentId },
      orderBy: { createdAt: 'desc' }
    }) as Task[];
  }

  // Agent operations
  async createAgent(data: Omit<Agent, 'id' | 'createdAt' | 'updatedAt'>): Promise<Agent> {
    this.ensureInitialized();
    return await this.prisma.agent.create({ data }) as Agent;
  }

  async getAgent(id: string): Promise<Agent | null> {
    this.ensureInitialized();
    return await this.prisma.agent.findUnique({ where: { id } }) as Agent | null;
  }

  async updateAgent(id: string, data: Partial<Agent>): Promise<Agent> {
    this.ensureInitialized();
    return await this.prisma.agent.update({ where: { id }, data }) as Agent;
  }

  async getAgents(): Promise<Agent[]> {
    this.ensureInitialized();
    return await this.prisma.agent.findMany() as Agent[];
  }

  // Message operations
  async createMessage(data: Omit<Message, 'id'>): Promise<Message> {
    this.ensureInitialized();
    return await this.prisma.message.create({ data }) as Message;
  }

  async getMessage(id: string): Promise<Message | null> {
    this.ensureInitialized();
    return await this.prisma.message.findUnique({ where: { id } }) as Message | null;
  }

  async getAgentMessages(
    agentId: string, 
    limit: number = 100, 
    offset: number = 0
  ): Promise<Message[]> {
    this.ensureInitialized();
    return await this.prisma.message.findMany({
      where: { agentId },
      skip: offset,
      take: limit,
      orderBy: { createdAt: 'desc' }
    }) as Message[];
  }

  async markMessagesAsRead(messageIds: string[]): Promise<void> {
    this.ensureInitialized();
    await this.prisma.message.updateMany({
      where: { id: { in: messageIds } },
      data: { isRead: true }
    });
  }

  async getUnreadMessageCount(agentId: string): Promise<number> {
    this.ensureInitialized();
    return await this.prisma.message.count({
      where: { agentId, isRead: false }
    });
  }

  async deleteOldMessages(cutoffDate: Date): Promise<number> {
    this.ensureInitialized();
    const result = await this.prisma.message.deleteMany({
      where: { createdAt: { lt: cutoffDate } }
    });
    return result.count;
  }

  // Workflow operations
  async createWorkflow(data: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt'>): Promise<Workflow> {
    this.ensureInitialized();
    return await this.prisma.workflow.create({ data }) as Workflow;
  }

  async getWorkflow(id: string): Promise<Workflow | null> {
    this.ensureInitialized();
    return await this.prisma.workflow.findUnique({ where: { id } }) as Workflow | null;
  }

  async updateWorkflow(id: string, data: Partial<Workflow>): Promise<Workflow> {
    this.ensureInitialized();
    return await this.prisma.workflow.update({ where: { id }, data }) as Workflow;
  }

  async getWorkflows(): Promise<Workflow[]> {
    this.ensureInitialized();
    return await this.prisma.workflow.findMany() as Workflow[];
  }

  // Workflow execution operations
  async createWorkflowExecution(
    data: Omit<WorkflowExecution, 'id'>
  ): Promise<WorkflowExecution> {
    this.ensureInitialized();
    return await this.prisma.workflowExecution.create({ data }) as WorkflowExecution;
  }

  async getWorkflowExecution(id: string): Promise<WorkflowExecution | null> {
    this.ensureInitialized();
    return await this.prisma.workflowExecution.findUnique({ where: { id } }) as WorkflowExecution | null;
  }

  async updateWorkflowExecution(
    id: string, 
    data: Partial<WorkflowExecution>
  ): Promise<WorkflowExecution> {
    this.ensureInitialized();
    return await this.prisma.workflowExecution.update({ where: { id }, data }) as WorkflowExecution;
  }

  async getRunningWorkflowExecutions(): Promise<WorkflowExecution[]> {
    this.ensureInitialized();
    return await this.prisma.workflowExecution.findMany({
      where: { status: 'RUNNING' }
    }) as WorkflowExecution[];
  }

  // User operations
  async createUser(data: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    this.ensureInitialized();
    return await this.prisma.user.create({ data }) as User;
  }

  async getUser(id: string): Promise<User | null> {
    this.ensureInitialized();
    return await this.prisma.user.findUnique({ where: { id } }) as User | null;
  }

  async getUserByEmail(email: string): Promise<User | null> {
    this.ensureInitialized();
    return await this.prisma.user.findUnique({ where: { email } }) as User | null;
  }

  async updateUser(id: string, data: Partial<User>): Promise<User> {
    this.ensureInitialized();
    return await this.prisma.user.update({ where: { id }, data }) as User;
  }

  // Analytics and reporting
  async getPartnerStats(): Promise<any> {
    this.ensureInitialized();
    const [total, byStatus, byPriority] = await Promise.all([
      this.prisma.partner.count(),
      this.prisma.partner.groupBy({
        by: ['status'],
        _count: true
      }),
      this.prisma.partner.groupBy({
        by: ['priority'],
        _count: true
      })
    ]);

    return { total, byStatus, byPriority };
  }

  async getTaskStats(): Promise<any> {
    this.ensureInitialized();
    const [total, byStatus, byType] = await Promise.all([
      this.prisma.task.count(),
      this.prisma.task.groupBy({
        by: ['status'],
        _count: true
      }),
      this.prisma.task.groupBy({
        by: ['type'],
        _count: true
      })
    ]);

    return { total, byStatus, byType };
  }

  async getOpportunityStats(): Promise<any> {
    this.ensureInitialized();
    const [total, byStage, totalValue] = await Promise.all([
      this.prisma.opportunity.count(),
      this.prisma.opportunity.groupBy({
        by: ['stage'],
        _count: true
      }),
      this.prisma.opportunity.aggregate({
        _sum: { value: true }
      })
    ]);

    return { total, byStage, totalValue: totalValue._sum.value || 0 };
  }

  /**
   * Execute raw SQL query
   */
  async executeRaw(query: string, params: any[] = []): Promise<any> {
    this.ensureInitialized();
    return await this.prisma.$queryRawUnsafe(query, ...params);
  }

  /**
   * Start a database transaction
   */
  async transaction<T>(fn: (prisma: PrismaClient) => Promise<T>): Promise<T> {
    this.ensureInitialized();
    return await this.prisma.$transaction(fn);
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      this.logger.error('Database health check failed', error);
      return false;
    }
  }

  /**
   * Cleanup and shutdown
   */
  async cleanup(): Promise<void> {
    // Implement any cleanup logic if needed
  }

  async shutdown(): Promise<void> {
    this.logger.info('Shutting down database service');
    await this.prisma.$disconnect();
    this.isInitialized = false;
  }
}

version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: ecobuilder_postgres
    environment:
      POSTGRES_DB: ecobuilder
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ecobuilder_network

  redis:
    image: redis:7-alpine
    container_name: ecobuilder_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ecobuilder_network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ecobuilder_backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=*****************************************/ecobuilder
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret-key
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - ecobuilder_network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ecobuilder_frontend
    ports:
      - "5173:5173"
    environment:
      - VITE_API_URL=http://localhost:3000
      - VITE_WS_URL=ws://localhost:3000
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - ecobuilder_network

volumes:
  postgres_data:
  redis_data:

networks:
  ecobuilder_network:
    driver: bridge

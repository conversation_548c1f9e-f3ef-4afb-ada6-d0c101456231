import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { Logger } from '../utils/logger';

interface APIConfig {
  baseURL: string;
  apiKey?: string;
  timeout: number;
  retries: number;
  rateLimit?: {
    requests: number;
    windowMs: number;
  };
}

interface CompanySearchCriteria {
  industry?: string;
  size?: string;
  location?: string;
  revenue?: { min?: number; max?: number };
  keywords?: string[];
}

interface CompanyData {
  name: string;
  domain?: string;
  industry?: string;
  size?: string;
  revenue?: number;
  location?: string;
  description?: string;
  website?: string;
  employees?: number;
  founded?: number;
  technologies?: string[];
  socialProfiles?: Record<string, string>;
}

export class ExternalAPIService {
  private logger: Logger;
  private clients: Map<string, AxiosInstance> = new Map();
  private rateLimiters: Map<string, any> = new Map();

  constructor() {
    this.logger = new Logger('ExternalAPIService');
    this.initializeClients();
  }

  /**
   * Initialize API clients for different services
   */
  private initializeClients(): void {
    // Crunchbase API
    if (process.env.CRUNCHBASE_API_KEY) {
      this.clients.set('crunchbase', axios.create({
        baseURL: 'https://api.crunchbase.com/api/v4',
        headers: {
          'X-cb-user-key': process.env.CRUNCHBASE_API_KEY
        },
        timeout: 30000
      }));
    }

    // Clearbit API
    if (process.env.CLEARBIT_API_KEY) {
      this.clients.set('clearbit', axios.create({
        baseURL: 'https://company.clearbit.com/v2',
        auth: {
          username: process.env.CLEARBIT_API_KEY,
          password: ''
        },
        timeout: 30000
      }));
    }

    // LinkedIn API (requires OAuth)
    if (process.env.LINKEDIN_ACCESS_TOKEN) {
      this.clients.set('linkedin', axios.create({
        baseURL: 'https://api.linkedin.com/v2',
        headers: {
          'Authorization': `Bearer ${process.env.LINKEDIN_ACCESS_TOKEN}`
        },
        timeout: 30000
      }));
    }

    // ZoomInfo API
    if (process.env.ZOOMINFO_API_KEY) {
      this.clients.set('zoominfo', axios.create({
        baseURL: 'https://api.zoominfo.com',
        headers: {
          'Authorization': `Bearer ${process.env.ZOOMINFO_API_KEY}`
        },
        timeout: 30000
      }));
    }

    // HubSpot API
    if (process.env.HUBSPOT_API_KEY) {
      this.clients.set('hubspot', axios.create({
        baseURL: 'https://api.hubapi.com',
        headers: {
          'Authorization': `Bearer ${process.env.HUBSPOT_API_KEY}`
        },
        timeout: 30000
      }));
    }
  }

  /**
   * Search for companies using a specific data source
   */
  async searchCompanies(source: string, criteria: CompanySearchCriteria): Promise<CompanyData[]> {
    this.logger.info('Searching companies', { source, criteria });

    try {
      switch (source) {
        case 'crunchbase':
          return await this.searchCrunchbase(criteria);
        case 'clearbit':
          return await this.searchClearbit(criteria);
        case 'linkedin':
          return await this.searchLinkedIn(criteria);
        case 'zoominfo':
          return await this.searchZoomInfo(criteria);
        default:
          throw new Error(`Unsupported data source: ${source}`);
      }
    } catch (error) {
      this.logger.error('Company search failed', { source, error });
      throw error;
    }
  }

  /**
   * Enrich company data with additional information
   */
  async enrichCompanyData(company: Partial<CompanyData>): Promise<CompanyData> {
    this.logger.info('Enriching company data', { companyName: company.name });

    const enrichedData: CompanyData = { ...company } as CompanyData;

    // Try to enrich from multiple sources
    const enrichmentPromises = [];

    if (company.domain && this.clients.has('clearbit')) {
      enrichmentPromises.push(this.enrichFromClearbit(company.domain));
    }

    if (company.name && this.clients.has('crunchbase')) {
      enrichmentPromises.push(this.enrichFromCrunchbase(company.name));
    }

    try {
      const results = await Promise.allSettled(enrichmentPromises);
      
      // Merge results
      results.forEach(result => {
        if (result.status === 'fulfilled' && result.value) {
          Object.assign(enrichedData, result.value);
        }
      });

      return enrichedData;
    } catch (error) {
      this.logger.error('Data enrichment failed', error);
      return enrichedData;
    }
  }

  /**
   * Search Crunchbase for companies
   */
  private async searchCrunchbase(criteria: CompanySearchCriteria): Promise<CompanyData[]> {
    const client = this.clients.get('crunchbase');
    if (!client) {
      throw new Error('Crunchbase client not configured');
    }

    const params: any = {
      entity_def_ids: ['organization'],
      limit: 50
    };

    // Build search filters
    const filters: any[] = [];

    if (criteria.industry) {
      filters.push({
        type: 'predicate',
        field_id: 'categories',
        operator_id: 'includes',
        values: [criteria.industry]
      });
    }

    if (criteria.location) {
      filters.push({
        type: 'predicate',
        field_id: 'location_identifiers',
        operator_id: 'includes',
        values: [criteria.location]
      });
    }

    if (filters.length > 0) {
      params.query = { filters };
    }

    const response = await client.post('/searches/organizations', params);
    
    return response.data.entities?.map((entity: any) => ({
      name: entity.properties.name,
      domain: entity.properties.website?.value,
      industry: entity.properties.categories?.[0]?.value,
      description: entity.properties.short_description,
      website: entity.properties.website?.value,
      employees: entity.properties.num_employees_enum,
      founded: entity.properties.founded_on?.value
    })) || [];
  }

  /**
   * Search Clearbit for companies
   */
  private async searchClearbit(criteria: CompanySearchCriteria): Promise<CompanyData[]> {
    const client = this.clients.get('clearbit');
    if (!client) {
      throw new Error('Clearbit client not configured');
    }

    // Clearbit doesn't have a direct search API, so we'll use domain lookup
    // This is a simplified implementation
    const results: CompanyData[] = [];

    if (criteria.keywords) {
      for (const keyword of criteria.keywords) {
        try {
          const response = await client.get(`/companies/find?name=${encodeURIComponent(keyword)}`);
          if (response.data) {
            results.push(this.mapClearbitData(response.data));
          }
        } catch (error) {
          // Continue with other keywords if one fails
          this.logger.warn('Clearbit search failed for keyword', { keyword, error });
        }
      }
    }

    return results;
  }

  /**
   * Search LinkedIn for companies
   */
  private async searchLinkedIn(criteria: CompanySearchCriteria): Promise<CompanyData[]> {
    const client = this.clients.get('linkedin');
    if (!client) {
      throw new Error('LinkedIn client not configured');
    }

    // LinkedIn API requires specific permissions and setup
    // This is a simplified implementation
    const params: any = {
      q: 'universalName',
      projection: '(elements*(name,description,website,employeeCountRange,industries))'
    };

    const response = await client.get('/companies', { params });
    
    return response.data.elements?.map((company: any) => ({
      name: company.name,
      description: company.description,
      website: company.website,
      industry: company.industries?.[0],
      employees: company.employeeCountRange?.start
    })) || [];
  }

  /**
   * Search ZoomInfo for companies
   */
  private async searchZoomInfo(criteria: CompanySearchCriteria): Promise<CompanyData[]> {
    const client = this.clients.get('zoominfo');
    if (!client) {
      throw new Error('ZoomInfo client not configured');
    }

    const searchParams: any = {
      companySearchRequest: {
        query: {},
        outputFields: [
          'companyName',
          'website',
          'industry',
          'revenue',
          'employees',
          'description'
        ]
      }
    };

    if (criteria.industry) {
      searchParams.companySearchRequest.query.industries = [criteria.industry];
    }

    if (criteria.revenue) {
      searchParams.companySearchRequest.query.revenueRange = criteria.revenue;
    }

    const response = await client.post('/search/company', searchParams);
    
    return response.data.data?.map((company: any) => ({
      name: company.companyName,
      website: company.website,
      industry: company.industry,
      revenue: company.revenue,
      employees: company.employees,
      description: company.description
    })) || [];
  }

  /**
   * Enrich data from Clearbit
   */
  private async enrichFromClearbit(domain: string): Promise<Partial<CompanyData>> {
    const client = this.clients.get('clearbit');
    if (!client) return {};

    try {
      const response = await client.get(`/companies/find?domain=${domain}`);
      return this.mapClearbitData(response.data);
    } catch (error) {
      this.logger.warn('Clearbit enrichment failed', { domain, error });
      return {};
    }
  }

  /**
   * Enrich data from Crunchbase
   */
  private async enrichFromCrunchbase(companyName: string): Promise<Partial<CompanyData>> {
    const client = this.clients.get('crunchbase');
    if (!client) return {};

    try {
      // Search for the company first
      const searchResponse = await client.post('/searches/organizations', {
        entity_def_ids: ['organization'],
        query: {
          filters: [{
            type: 'predicate',
            field_id: 'name',
            operator_id: 'contains',
            values: [companyName]
          }]
        },
        limit: 1
      });

      const entity = searchResponse.data.entities?.[0];
      if (!entity) return {};

      return {
        name: entity.properties.name,
        domain: entity.properties.website?.value,
        industry: entity.properties.categories?.[0]?.value,
        description: entity.properties.short_description,
        founded: entity.properties.founded_on?.value
      };
    } catch (error) {
      this.logger.warn('Crunchbase enrichment failed', { companyName, error });
      return {};
    }
  }

  /**
   * Map Clearbit data to our format
   */
  private mapClearbitData(data: any): CompanyData {
    return {
      name: data.name,
      domain: data.domain,
      industry: data.category?.industry,
      description: data.description,
      website: data.site?.url,
      employees: data.metrics?.employees,
      founded: data.foundedYear,
      location: data.geo?.city,
      technologies: data.tech || []
    };
  }

  /**
   * Send email via external service (SendGrid, SES, etc.)
   */
  async sendEmail(
    to: string,
    subject: string,
    content: string,
    options: { from?: string; cc?: string[]; bcc?: string[] } = {}
  ): Promise<boolean> {
    this.logger.info('Sending email', { to, subject });

    try {
      // Implementation would depend on the email service being used
      // This is a placeholder for the actual implementation
      
      if (process.env.SENDGRID_API_KEY) {
        return await this.sendViaSendGrid(to, subject, content, options);
      } else if (process.env.AWS_SES_REGION) {
        return await this.sendViaSES(to, subject, content, options);
      } else {
        throw new Error('No email service configured');
      }
    } catch (error) {
      this.logger.error('Email sending failed', error);
      throw error;
    }
  }

  /**
   * Send email via SendGrid
   */
  private async sendViaSendGrid(
    to: string,
    subject: string,
    content: string,
    options: any
  ): Promise<boolean> {
    // SendGrid implementation
    const sgMail = require('@sendgrid/mail');
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);

    const msg = {
      to,
      from: options.from || process.env.FROM_EMAIL,
      subject,
      html: content,
      cc: options.cc,
      bcc: options.bcc
    };

    await sgMail.send(msg);
    return true;
  }

  /**
   * Send email via AWS SES
   */
  private async sendViaSES(
    to: string,
    subject: string,
    content: string,
    options: any
  ): Promise<boolean> {
    // AWS SES implementation would go here
    // This is a placeholder
    return true;
  }

  /**
   * Schedule a calendar event
   */
  async scheduleCalendarEvent(
    title: string,
    startTime: Date,
    endTime: Date,
    attendees: string[],
    description?: string
  ): Promise<any> {
    this.logger.info('Scheduling calendar event', { title, startTime, attendees });

    try {
      // Implementation would depend on calendar service (Google Calendar, Outlook, etc.)
      // This is a placeholder for the actual implementation
      
      if (process.env.GOOGLE_CALENDAR_CREDENTIALS) {
        return await this.scheduleGoogleCalendarEvent(title, startTime, endTime, attendees, description);
      } else {
        throw new Error('No calendar service configured');
      }
    } catch (error) {
      this.logger.error('Calendar event scheduling failed', error);
      throw error;
    }
  }

  /**
   * Schedule Google Calendar event
   */
  private async scheduleGoogleCalendarEvent(
    title: string,
    startTime: Date,
    endTime: Date,
    attendees: string[],
    description?: string
  ): Promise<any> {
    // Google Calendar API implementation would go here
    // This is a placeholder
    return {
      id: 'event_' + Date.now(),
      title,
      startTime,
      endTime,
      attendees,
      description
    };
  }

  /**
   * Upload file to cloud storage
   */
  async uploadFile(
    file: Buffer,
    filename: string,
    contentType: string
  ): Promise<{ url: string; key: string }> {
    this.logger.info('Uploading file', { filename, contentType });

    try {
      if (process.env.AWS_S3_BUCKET) {
        return await this.uploadToS3(file, filename, contentType);
      } else {
        throw new Error('No file storage service configured');
      }
    } catch (error) {
      this.logger.error('File upload failed', error);
      throw error;
    }
  }

  /**
   * Upload file to AWS S3
   */
  private async uploadToS3(
    file: Buffer,
    filename: string,
    contentType: string
  ): Promise<{ url: string; key: string }> {
    // AWS S3 implementation would go here
    // This is a placeholder
    const key = `uploads/${Date.now()}_${filename}`;
    const url = `https://${process.env.AWS_S3_BUCKET}.s3.amazonaws.com/${key}`;
    
    return { url, key };
  }

  /**
   * Health check for external services
   */
  async healthCheck(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};

    for (const [service, client] of this.clients.entries()) {
      try {
        // Simple health check - attempt a basic request
        await client.get('/health', { timeout: 5000 });
        results[service] = true;
      } catch (error) {
        results[service] = false;
      }
    }

    return results;
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    // Cleanup any resources if needed
    this.clients.clear();
    this.rateLimiters.clear();
    this.logger.info('External API service cleanup completed');
  }
}

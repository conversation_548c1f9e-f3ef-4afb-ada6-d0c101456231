{"name": "ecobuilder", "version": "1.0.0", "description": "EcoBuilder Multi-Agent Partner Development System", "private": true, "workspaces": ["backend", "frontend", "shared"], "scripts": {"install:all": "npm install && npm run install:backend && npm run install:frontend && npm run install:shared", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "install:shared": "cd shared && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:shared && npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "build:shared": "cd shared && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "test:e2e": "cd backend && npm run test:e2e", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:frontend", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:frontend": "cd frontend && npm run lint:fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "type-check": "npm run type-check:backend && npm run type-check:frontend", "type-check:backend": "cd backend && npm run type-check", "type-check:frontend": "cd frontend && npm run type-check", "clean": "npm run clean:backend && npm run clean:frontend && npm run clean:shared", "clean:backend": "cd backend && npm run clean", "clean:frontend": "cd frontend && npm run clean", "clean:shared": "cd shared && npm run clean", "db:migrate": "cd backend && npx prisma migrate dev", "db:generate": "cd backend && npx prisma generate", "db:seed": "cd backend && npx prisma db seed", "db:reset": "cd backend && npx prisma migrate reset", "db:studio": "cd backend && npx prisma studio", "docker:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:dev:down": "docker-compose -f docker-compose.dev.yml down", "docker:dev:logs": "docker-compose -f docker-compose.dev.yml logs -f", "docker:prod": "docker-compose up -d", "docker:prod:down": "docker-compose down", "setup:dev": "npm run install:all && npm run db:generate && npm run db:migrate && npm run db:seed", "start:dev": "npm run docker:dev && npm run dev", "stop:dev": "npm run docker:dev:down"}, "devDependencies": {"concurrently": "^8.2.2", "prettier": "^3.1.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["ai", "agents", "multi-agent", "partner-development", "ecosystem", "automation", "crm", "business-development"], "author": "EcoBuilder Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/ecobuilder.git"}, "bugs": {"url": "https://github.com/your-org/ecobuilder/issues"}, "homepage": "https://github.com/your-org/ecobuilder#readme"}
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for EcoBuilder */

.App {
  @apply h-screen overflow-hidden;
}

/* Custom scrollbar */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* Loading animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Status indicators */
.status-online {
  @apply text-green-600 bg-green-100;
}

.status-offline {
  @apply text-gray-600 bg-gray-100;
}

.status-error {
  @apply text-red-600 bg-red-100;
}

.status-warning {
  @apply text-yellow-600 bg-yellow-100;
}

/* Agent status colors */
.agent-working {
  @apply bg-green-100 text-green-800 border-green-200;
}

.agent-idle {
  @apply bg-blue-100 text-blue-800 border-blue-200;
}

.agent-error {
  @apply bg-red-100 text-red-800 border-red-200;
}

.agent-offline {
  @apply bg-gray-100 text-gray-800 border-gray-200;
}

.agent-maintenance {
  @apply bg-yellow-100 text-yellow-800 border-yellow-200;
}

/* Task status colors */
.task-pending {
  @apply bg-yellow-100 text-yellow-800 border-yellow-200;
}

.task-running {
  @apply bg-blue-100 text-blue-800 border-blue-200;
}

.task-completed {
  @apply bg-green-100 text-green-800 border-green-200;
}

.task-failed {
  @apply bg-red-100 text-red-800 border-red-200;
}

.task-cancelled {
  @apply bg-gray-100 text-gray-800 border-gray-200;
}

.task-retrying {
  @apply bg-orange-100 text-orange-800 border-orange-200;
}

/* Priority colors */
.priority-urgent {
  @apply border-l-red-500 bg-red-50;
}

.priority-high {
  @apply border-l-orange-500 bg-orange-50;
}

.priority-medium {
  @apply border-l-yellow-500 bg-yellow-50;
}

.priority-low {
  @apply border-l-green-500 bg-green-50;
}

/* Card hover effects */
.card-hover {
  @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
}

/* Button variants */
.btn-primary {
  @apply bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors;
}

.btn-secondary {
  @apply bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors;
}

.btn-success {
  @apply bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors;
}

.btn-danger {
  @apply bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors;
}

.btn-warning {
  @apply bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-colors;
}

/* Form styles */
.form-input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.form-select {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.form-textarea {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

/* Progress bars */
.progress-bar {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.progress-fill {
  @apply h-2 rounded-full transition-all duration-300;
}

.progress-blue {
  @apply bg-blue-500;
}

.progress-green {
  @apply bg-green-500;
}

.progress-yellow {
  @apply bg-yellow-500;
}

.progress-red {
  @apply bg-red-500;
}

/* Workflow visualization */
.workflow-stage {
  @apply flex items-center justify-center w-12 h-12 rounded-full text-white font-medium text-sm transition-all duration-300;
}

.workflow-stage.completed {
  @apply bg-green-500;
}

.workflow-stage.running {
  @apply bg-blue-500 animate-pulse;
}

.workflow-stage.pending {
  @apply bg-gray-300 text-gray-600;
}

.workflow-stage.failed {
  @apply bg-red-500;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .mobile-hidden {
    @apply hidden;
  }
  
  .mobile-full {
    @apply w-full;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    @apply bg-gray-900 text-white;
  }
}

/* Custom animations */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.slide-out {
  animation: slideOut 0.3s ease-in;
}

/* Tooltip styles */
.tooltip {
  @apply absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg opacity-0 pointer-events-none transition-opacity duration-200;
}

.tooltip.show {
  @apply opacity-100;
}

/* Line clamp utility */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

import { useState, useEffect } from 'react';
import { Task, TaskStatus, TaskType, Priority } from '../../../shared/types';
import { apiClient } from '../services/api';

interface UseTasksReturn {
  tasks: Task[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  createTask: (taskData: Partial<Task>) => Promise<Task>;
  updateTask: (taskId: string, updates: Partial<Task>) => Promise<Task>;
  deleteTask: (taskId: string) => Promise<void>;
  cancelTask: (taskId: string) => Promise<void>;
  retryTask: (taskId: string) => Promise<void>;
}

export const useTasks = (): UseTasksReturn => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.get('/tasks');
      setTasks(response.data.data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch tasks');
      console.error('Error fetching tasks:', err);
    } finally {
      setLoading(false);
    }
  };

  const createTask = async (taskData: Partial<Task>): Promise<Task> => {
    try {
      const response = await apiClient.post('/tasks', taskData);
      const newTask = response.data.data;
      setTasks(prev => [...prev, newTask]);
      return newTask;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create task');
      throw err;
    }
  };

  const updateTask = async (taskId: string, updates: Partial<Task>): Promise<Task> => {
    try {
      const response = await apiClient.put(`/tasks/${taskId}`, updates);
      const updatedTask = response.data.data;
      setTasks(prev => prev.map(task => 
        task.id === taskId ? updatedTask : task
      ));
      return updatedTask;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update task');
      throw err;
    }
  };

  const deleteTask = async (taskId: string): Promise<void> => {
    try {
      await apiClient.delete(`/tasks/${taskId}`);
      setTasks(prev => prev.filter(task => task.id !== taskId));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete task');
      throw err;
    }
  };

  const cancelTask = async (taskId: string): Promise<void> => {
    try {
      await apiClient.post(`/tasks/${taskId}/cancel`);
      setTasks(prev => prev.map(task => 
        task.id === taskId 
          ? { ...task, status: TaskStatus.CANCELLED, completedAt: new Date() }
          : task
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to cancel task');
      throw err;
    }
  };

  const retryTask = async (taskId: string): Promise<void> => {
    try {
      await apiClient.post(`/tasks/${taskId}/retry`);
      setTasks(prev => prev.map(task => 
        task.id === taskId 
          ? { ...task, status: TaskStatus.PENDING, error: undefined }
          : task
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to retry task');
      throw err;
    }
  };

  useEffect(() => {
    fetchTasks();
  }, []);

  return {
    tasks,
    loading,
    error,
    refetch: fetchTasks,
    createTask,
    updateTask,
    deleteTask,
    cancelTask,
    retryTask
  };
};

// Mock data for development
const mockTasks: Task[] = [
  {
    id: 'task-1',
    title: 'Find Technology Partners',
    description: 'Search for potential technology partners in the AI/ML space',
    type: TaskType.FIND_PARTNERS,
    status: TaskStatus.RUNNING,
    priority: Priority.HIGH,
    data: { industry: 'technology', keywords: ['AI', 'ML', 'automation'] },
    progress: 75,
    maxRetries: 3,
    retryCount: 0,
    agentId: 'agent-1',
    createdAt: new Date(Date.now() - 3600000), // 1 hour ago
    updatedAt: new Date(Date.now() - 1800000), // 30 minutes ago
    startedAt: new Date(Date.now() - 3000000) // 50 minutes ago
  },
  {
    id: 'task-2',
    title: 'Score Partner: TechCorp Inc',
    description: 'Evaluate TechCorp Inc as a potential partner',
    type: TaskType.SCORE_PARTNER,
    status: TaskStatus.COMPLETED,
    priority: Priority.MEDIUM,
    data: { partnerId: 'partner-123', criteria: { industry: 'match', size: 'large' } },
    result: { score: 0.85, reasoning: 'Strong technical alignment and market presence' },
    progress: 100,
    maxRetries: 3,
    retryCount: 0,
    agentId: 'agent-1',
    createdAt: new Date(Date.now() - 7200000), // 2 hours ago
    updatedAt: new Date(Date.now() - 5400000), // 1.5 hours ago
    startedAt: new Date(Date.now() - 6600000), // 1.8 hours ago
    completedAt: new Date(Date.now() - 5400000) // 1.5 hours ago
  },
  {
    id: 'task-3',
    title: 'Send Introduction Email',
    description: 'Send personalized introduction email to TechCorp Inc',
    type: TaskType.SEND_EMAIL,
    status: TaskStatus.PENDING,
    priority: Priority.HIGH,
    data: { 
      recipientEmail: '<EMAIL>',
      template: 'partner_introduction',
      context: { companyName: 'TechCorp Inc', industry: 'technology' }
    },
    progress: 0,
    maxRetries: 3,
    retryCount: 0,
    agentId: 'agent-2',
    createdAt: new Date(Date.now() - 1800000), // 30 minutes ago
    updatedAt: new Date(Date.now() - 1800000) // 30 minutes ago
  },
  {
    id: 'task-4',
    title: 'Schedule Follow-up Meeting',
    description: 'Schedule a follow-up meeting with interested partners',
    type: TaskType.SCHEDULE_MEETING,
    status: TaskStatus.FAILED,
    priority: Priority.MEDIUM,
    data: { 
      attendees: ['<EMAIL>', '<EMAIL>'],
      duration: 60,
      subject: 'Partnership Discussion'
    },
    error: 'Calendar service unavailable',
    progress: 0,
    maxRetries: 3,
    retryCount: 2,
    agentId: 'agent-2',
    createdAt: new Date(Date.now() - 10800000), // 3 hours ago
    updatedAt: new Date(Date.now() - 900000), // 15 minutes ago
    startedAt: new Date(Date.now() - 9000000), // 2.5 hours ago
    completedAt: new Date(Date.now() - 900000) // 15 minutes ago
  },
  {
    id: 'task-5',
    title: 'Generate Partnership Proposal',
    description: 'Create a detailed partnership proposal document',
    type: TaskType.CREATE_PROPOSAL,
    status: TaskStatus.RETRYING,
    priority: Priority.URGENT,
    data: { 
      partnerId: 'partner-456',
      proposalType: 'technology_partnership',
      terms: { revenue_share: 0.15, exclusivity: false }
    },
    progress: 25,
    maxRetries: 3,
    retryCount: 1,
    agentId: 'agent-2',
    createdAt: new Date(Date.now() - 5400000), // 1.5 hours ago
    updatedAt: new Date(Date.now() - 600000), // 10 minutes ago
    startedAt: new Date(Date.now() - 4800000) // 1.3 hours ago
  },
  {
    id: 'task-6',
    title: 'Analyze Market Opportunity',
    description: 'Analyze the European market for expansion opportunities',
    type: TaskType.MARKET_ANALYSIS,
    status: TaskStatus.RUNNING,
    priority: Priority.LOW,
    data: { 
      region: 'Europe',
      industry: 'fintech',
      timeframe: '12_months'
    },
    progress: 40,
    maxRetries: 3,
    retryCount: 0,
    agentId: 'agent-5',
    createdAt: new Date(Date.now() - 14400000), // 4 hours ago
    updatedAt: new Date(Date.now() - 300000), // 5 minutes ago
    startedAt: new Date(Date.now() - 12600000) // 3.5 hours ago
  },
  {
    id: 'task-7',
    title: 'Generate Weekly Report',
    description: 'Generate weekly partnership development report',
    type: TaskType.GENERATE_REPORT,
    status: TaskStatus.CANCELLED,
    priority: Priority.LOW,
    data: { 
      reportType: 'weekly_summary',
      period: { start: '2024-01-01', end: '2024-01-07' }
    },
    progress: 0,
    maxRetries: 3,
    retryCount: 0,
    agentId: 'agent-4',
    createdAt: new Date(Date.now() - 86400000), // 1 day ago
    updatedAt: new Date(Date.now() - 82800000), // 23 hours ago
    completedAt: new Date(Date.now() - 82800000) // 23 hours ago
  }
];

// Use mock data in development
export const useTasksMock = (): UseTasksReturn => {
  const [tasks, setTasks] = useState<Task[]>(mockTasks);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTasks = async () => {
    setLoading(true);
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    setTasks(mockTasks);
    setLoading(false);
  };

  const createTask = async (taskData: Partial<Task>): Promise<Task> => {
    const newTask: Task = {
      id: `task-${Date.now()}`,
      title: taskData.title || 'New Task',
      type: taskData.type || TaskType.FIND_PARTNERS,
      status: TaskStatus.PENDING,
      priority: taskData.priority || Priority.MEDIUM,
      data: taskData.data || {},
      progress: 0,
      maxRetries: 3,
      retryCount: 0,
      agentId: taskData.agentId || 'agent-1',
      createdAt: new Date(),
      updatedAt: new Date(),
      ...taskData
    };
    setTasks(prev => [...prev, newTask]);
    return newTask;
  };

  const updateTask = async (taskId: string, updates: Partial<Task>): Promise<Task> => {
    const updatedTask = tasks.find(t => t.id === taskId);
    if (!updatedTask) throw new Error('Task not found');
    
    const newTask = { ...updatedTask, ...updates, updatedAt: new Date() };
    setTasks(prev => prev.map(task => 
      task.id === taskId ? newTask : task
    ));
    return newTask;
  };

  const deleteTask = async (taskId: string): Promise<void> => {
    setTasks(prev => prev.filter(task => task.id !== taskId));
  };

  const cancelTask = async (taskId: string): Promise<void> => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, status: TaskStatus.CANCELLED, completedAt: new Date() }
        : task
    ));
  };

  const retryTask = async (taskId: string): Promise<void> => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, status: TaskStatus.PENDING, error: undefined }
        : task
    ));
  };

  return {
    tasks,
    loading,
    error,
    refetch: fetchTasks,
    createTask,
    updateTask,
    deleteTask,
    cancelTask,
    retryTask
  };
};

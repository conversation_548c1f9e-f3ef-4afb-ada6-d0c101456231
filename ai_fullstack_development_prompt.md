# EcoBuilder AI全栈开发提示词

## 项目概述
请帮我开发一个名为EcoBuilder的多智能体群系统，用于自动化生态合作伙伴开发流程。这是一个完整的全栈项目，需要从零开始构建，包括前端、后端、数据库、AI集成、测试和部署。

## 开发要求

### 技术栈
- **前端**: React 18 + TypeScript + Tailwind CSS + Vite
- **后端**: Node.js + Express + TypeScript + Prisma ORM
- **数据库**: PostgreSQL
- **AI集成**: OpenAI GPT-4 API
- **实时通信**: Socket.io
- **容器化**: Docker + Docker Compose
- **测试**: Jest + React Testing Library + Supertest

### 核心功能
1. **5个专业智能体**:
   - PartnerScout: 合作伙伴发现和筛选
   - PartnerRecruiter: 合作伙伴招募和谈判
   - PartnerTrainer: 销售团队培训和赋能
   - OpportunityManager: 商机管理和跟踪
   - MarketExpander: 市场拓展和活动管理

2. **工作流引擎**: 智能体协调和任务调度
3. **多模态交互**: 支持文字、语音、文件等交互方式
4. **第三方集成**: CRM、邮件、日历等系统集成
5. **实时监控**: 智能体状态和任务进度监控

## 开发步骤

### Phase 1: 基础架构搭建
请按以下顺序执行：

1. **项目初始化**
```bash
# 创建项目结构
mkdir ecobuilder && cd ecobuilder
mkdir frontend backend shared

# 初始化前端项目
cd frontend
npm create vite@latest . -- --template react-ts
npm install @tailwindcss/forms @headlessui/react @heroicons/react
npm install axios socket.io-client react-router-dom
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p

# 初始化后端项目
cd ../backend
npm init -y
npm install express cors helmet morgan compression
npm install prisma @prisma/client bcryptjs jsonwebtoken
npm install socket.io openai nodemailer
npm install -D @types/node @types/express @types/cors
npm install -D @types/bcryptjs @types/jsonwebtoken typescript
npm install -D ts-node nodemon jest supertest @types/jest

# 初始化共享类型
cd ../shared
npm init -y
npm install typescript
```

2. **Docker环境配置**
创建 `docker-compose.yml`:
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: ecobuilder
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

3. **数据库模型设计**
创建 `backend/prisma/schema.prisma`，包含：
- Partner (合作伙伴)
- Contact (联系人)
- Agreement (协议)
- Opportunity (商机)
- Activity (活动)
- Agent (智能体)
- Task (任务)
- Message (消息)

### Phase 2: 核心智能体开发

请为每个智能体创建完整的实现：

1. **智能体基础架构**
```typescript
// shared/types/agent.ts
export interface Agent {
  id: string;
  name: string;
  type: AgentType;
  status: AgentStatus;
  capabilities: string[];
  tools: Tool[];
  execute(task: Task): Promise<TaskResult>;
  communicate(message: Message): Promise<Response>;
}
```

2. **PartnerScout Agent**
- 实现合作伙伴发现算法
- 集成企业信息查询API
- 实现智能评分系统
- 创建推荐引擎

3. **PartnerRecruiter Agent**
- 实现邮件自动化
- 集成日历系统
- 创建协议模板引擎
- 实现谈判策略AI

4. **其他智能体**
按照相同模式实现剩余3个智能体

### Phase 3: 工作流引擎开发

1. **工作流引擎**
```typescript
// backend/src/services/WorkflowEngine.ts
export class WorkflowEngine {
  async executeWorkflow(workflowId: string, context: any): Promise<void>;
  private async executeStage(stage: WorkflowStage, context: any): Promise<void>;
  private async handleAgentCommunication(message: Message): Promise<void>;
}
```

2. **任务调度器**
- 实现任务队列
- 优先级调度
- 失败重试机制
- 并发控制

3. **消息总线**
- 智能体间通信
- 事件发布订阅
- 消息持久化
- 实时通知

### Phase 4: 前端界面开发

1. **主要页面组件**
```tsx
// frontend/src/pages/
- Dashboard.tsx          // 主仪表板
- AgentManagement.tsx    // 智能体管理
- PartnerManagement.tsx  // 合作伙伴管理
- WorkflowDesigner.tsx   // 工作流设计器
- Analytics.tsx          // 分析报表
```

2. **智能体交互组件**
```tsx
// frontend/src/components/agents/
- AgentCard.tsx          // 智能体卡片
- AgentChat.tsx          // 智能体对话
- TaskMonitor.tsx        // 任务监控
- WorkflowVisualizer.tsx // 工作流可视化
```

3. **实时通信**
- Socket.io客户端集成
- 实时状态更新
- 消息推送
- 进度监控

### Phase 5: 集成和测试

1. **第三方系统集成**
- CRM系统API集成 (Salesforce/HubSpot)
- 邮件服务集成 (SendGrid)
- 日历集成 (Google Calendar)
- 文件存储 (AWS S3)

2. **AI服务集成**
```typescript
// backend/src/services/AIService.ts
export class AIService {
  async analyzePartnerFit(data: any): Promise<AnalysisResult>;
  async generateEmail(context: any): Promise<string>;
  async scoreOpportunity(opportunity: any): Promise<number>;
  async recommendActions(context: any): Promise<Action[]>;
}
```

3. **测试套件**
- 单元测试 (Jest)
- 集成测试 (Supertest)
- 端到端测试 (Playwright)
- 性能测试 (Artillery)

### Phase 6: 部署和监控

1. **生产环境配置**
```dockerfile
# Dockerfile for backend
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

2. **监控和日志**
- 应用性能监控 (APM)
- 错误追踪 (Sentry)
- 日志聚合 (ELK Stack)
- 健康检查端点

## 开发指导原则

### 代码质量
- 使用TypeScript严格模式
- 遵循ESLint和Prettier规范
- 实现完整的错误处理
- 添加详细的代码注释

### 安全考虑
- 实现JWT身份验证
- 数据加密和脱敏
- API限流和防护
- 输入验证和清理

### 性能优化
- 数据库查询优化
- 缓存策略实现
- 异步处理优化
- 前端代码分割

### 用户体验
- 响应式设计
- 加载状态指示
- 错误友好提示
- 无障碍访问支持

## 具体开发请求

请按照以上规划，从Phase 1开始，逐步完成EcoBuilder多智能体群系统的开发。对于每个阶段：

1. 首先创建必要的文件结构
2. 实现核心功能代码
3. 添加相应的测试用例
4. 提供运行和测试指令
5. 确保代码质量和最佳实践

每完成一个阶段后，请提供：
- 代码实现总结
- 功能演示说明
- 下一阶段的准备工作
- 可能遇到的问题和解决方案

请开始Phase 1的开发工作，创建完整的项目基础架构。

import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { ApiResponse } from '../../../shared/types';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads');
    
    // Create uploads directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1E9)}`;
    const extension = path.extname(file.originalname);
    const filename = `${file.fieldname}-${uniqueSuffix}${extension}`;
    cb(null, filename);
  }
});

// File filter to validate file types
const fileFilter = (req: any, file: Express.Multer.File, cb: any) => {
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/csv',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'image/jpeg',
    'image/png',
    'image/gif'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`不支持的文件类型: ${file.mimetype}`), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 20 * 1024 * 1024, // 20MB limit
    files: 10 // Maximum 10 files
  }
});

// In-memory file storage for demo (in production, use database)
interface FileRecord {
  id: string;
  originalName: string;
  filename: string;
  mimetype: string;
  size: number;
  uploadedAt: Date;
  url: string;
}

const fileRecords: Map<string, FileRecord> = new Map();

// Upload single file
router.post('/upload', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '没有上传文件',
        data: null
      } as ApiResponse);
    }

    const fileId = uuidv4();
    const fileRecord: FileRecord = {
      id: fileId,
      originalName: req.file.originalname,
      filename: req.file.filename,
      mimetype: req.file.mimetype,
      size: req.file.size,
      uploadedAt: new Date(),
      url: `/api/files/${fileId}/download`
    };

    fileRecords.set(fileId, fileRecord);

    res.json({
      success: true,
      message: '文件上传成功',
      data: {
        id: fileId,
        name: fileRecord.originalName,
        size: fileRecord.size,
        type: fileRecord.mimetype,
        url: fileRecord.url,
        uploadedAt: fileRecord.uploadedAt
      }
    } as ApiResponse);

  } catch (error) {
    console.error('File upload error:', error);
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '文件上传失败',
      data: null
    } as ApiResponse);
  }
});

// Upload multiple files
router.post('/upload-multiple', upload.array('files', 10), (req, res) => {
  try {
    const files = req.files as Express.Multer.File[];
    
    if (!files || files.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有上传文件',
        data: null
      } as ApiResponse);
    }

    const uploadedFiles = files.map(file => {
      const fileId = uuidv4();
      const fileRecord: FileRecord = {
        id: fileId,
        originalName: file.originalname,
        filename: file.filename,
        mimetype: file.mimetype,
        size: file.size,
        uploadedAt: new Date(),
        url: `/api/files/${fileId}/download`
      };

      fileRecords.set(fileId, fileRecord);

      return {
        id: fileId,
        name: fileRecord.originalName,
        size: fileRecord.size,
        type: fileRecord.mimetype,
        url: fileRecord.url,
        uploadedAt: fileRecord.uploadedAt
      };
    });

    res.json({
      success: true,
      message: `成功上传 ${uploadedFiles.length} 个文件`,
      data: uploadedFiles
    } as ApiResponse);

  } catch (error) {
    console.error('Multiple file upload error:', error);
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '文件上传失败',
      data: null
    } as ApiResponse);
  }
});

// Get all files
router.get('/', (req, res) => {
  try {
    const files = Array.from(fileRecords.values()).map(record => ({
      id: record.id,
      name: record.originalName,
      size: record.size,
      type: record.mimetype,
      url: record.url,
      uploadedAt: record.uploadedAt
    }));

    res.json({
      success: true,
      message: '获取文件列表成功',
      data: files
    } as ApiResponse);

  } catch (error) {
    console.error('Get files error:', error);
    res.status(500).json({
      success: false,
      message: '获取文件列表失败',
      data: null
    } as ApiResponse);
  }
});

// Get file metadata
router.get('/:id', (req, res) => {
  try {
    const fileId = req.params.id;
    const fileRecord = fileRecords.get(fileId);

    if (!fileRecord) {
      return res.status(404).json({
        success: false,
        message: '文件不存在',
        data: null
      } as ApiResponse);
    }

    res.json({
      success: true,
      message: '获取文件信息成功',
      data: {
        id: fileRecord.id,
        name: fileRecord.originalName,
        size: fileRecord.size,
        type: fileRecord.mimetype,
        url: fileRecord.url,
        uploadedAt: fileRecord.uploadedAt
      }
    } as ApiResponse);

  } catch (error) {
    console.error('Get file error:', error);
    res.status(500).json({
      success: false,
      message: '获取文件信息失败',
      data: null
    } as ApiResponse);
  }
});

// Download file
router.get('/:id/download', (req, res) => {
  try {
    const fileId = req.params.id;
    const fileRecord = fileRecords.get(fileId);

    if (!fileRecord) {
      return res.status(404).json({
        success: false,
        message: '文件不存在',
        data: null
      } as ApiResponse);
    }

    const filePath = path.join(__dirname, '../../uploads', fileRecord.filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: '文件已被删除',
        data: null
      } as ApiResponse);
    }

    res.setHeader('Content-Disposition', `attachment; filename="${fileRecord.originalName}"`);
    res.setHeader('Content-Type', fileRecord.mimetype);
    
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

  } catch (error) {
    console.error('Download file error:', error);
    res.status(500).json({
      success: false,
      message: '文件下载失败',
      data: null
    } as ApiResponse);
  }
});

// Delete file
router.delete('/:id', (req, res) => {
  try {
    const fileId = req.params.id;
    const fileRecord = fileRecords.get(fileId);

    if (!fileRecord) {
      return res.status(404).json({
        success: false,
        message: '文件不存在',
        data: null
      } as ApiResponse);
    }

    // Delete physical file
    const filePath = path.join(__dirname, '../../uploads', fileRecord.filename);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Remove from records
    fileRecords.delete(fileId);

    res.json({
      success: true,
      message: '文件删除成功',
      data: null
    } as ApiResponse);

  } catch (error) {
    console.error('Delete file error:', error);
    res.status(500).json({
      success: false,
      message: '文件删除失败',
      data: null
    } as ApiResponse);
  }
});

// Update file metadata
router.put('/:id/metadata', (req, res) => {
  try {
    const fileId = req.params.id;
    const fileRecord = fileRecords.get(fileId);

    if (!fileRecord) {
      return res.status(404).json({
        success: false,
        message: '文件不存在',
        data: null
      } as ApiResponse);
    }

    // Update allowed metadata fields
    const { name } = req.body;
    if (name) {
      fileRecord.originalName = name;
    }

    fileRecords.set(fileId, fileRecord);

    res.json({
      success: true,
      message: '文件信息更新成功',
      data: {
        id: fileRecord.id,
        name: fileRecord.originalName,
        size: fileRecord.size,
        type: fileRecord.mimetype,
        url: fileRecord.url,
        uploadedAt: fileRecord.uploadedAt
      }
    } as ApiResponse);

  } catch (error) {
    console.error('Update file metadata error:', error);
    res.status(500).json({
      success: false,
      message: '文件信息更新失败',
      data: null
    } as ApiResponse);
  }
});

export default router;

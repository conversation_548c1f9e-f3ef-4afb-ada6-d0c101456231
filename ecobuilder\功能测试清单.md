# 🧪 Dify集成功能测试清单

## 📁 文件位置
- **主文件**: `dify-integration-fixed.html`
- **简化测试**: `simple-test.html`
- **测试模式**: 在URL后添加 `?test=true`

## 🎯 测试步骤

### 1. 基本页面加载测试
- [ ] 页面能正常打开
- [ ] 标题显示："Dify生态合作伙伴分析平台"
- [ ] 页面有渐变背景（蓝紫色）
- [ ] 页面布局正常显示

### 2. 正常模式功能测试

#### 📄 文件上传功能
- [ ] 文件上传区域显示正常
- [ ] 点击上传区域能触发文件选择器
- [ ] 选择文件后显示上传状态
- [ ] 上传成功后显示文件信息
- [ ] 拖拽文件到上传区域有效果

#### 🤖 AI分析按钮
- [ ] AI按钮初始状态为红色"开始AI生态分析"
- [ ] 点击按钮后变为灰色"处理中..."
- [ ] 继续点击变为蓝色"评估合作伙伴需求"
- [ ] 再次点击变为绿色"选择合作伙伴"
- [ ] 最后点击重置为红色初始状态

### 3. 测试模式功能测试（添加 ?test=true）

#### 🧪 测试面板显示
- [ ] 页面顶部显示"状态更新测试页面"
- [ ] 显示文件上传测试区域
- [ ] 显示AI分析按钮测试区域
- [ ] 显示状态日志区域

#### 📁 文件上传测试区域
- [ ] 点击上传区域能选择文件
- [ ] "测试上传状态"按钮显示上传中动画
- [ ] "测试成功状态"按钮显示成功状态
- [ ] "测试错误状态"按钮显示错误状态
- [ ] "重置状态"按钮恢复初始状态

#### 🤖 AI按钮测试区域
- [ ] 测试AI按钮能正常点击循环
- [ ] "测试处理状态"按钮显示处理状态
- [ ] "测试评估阶段"按钮显示蓝色评估状态
- [ ] "测试选择阶段"按钮显示绿色选择状态
- [ ] "重置按钮"恢复红色初始状态

#### 📋 状态日志
- [ ] 日志区域显示操作记录
- [ ] 每个操作都有时间戳
- [ ] "清空日志"按钮能清空内容

### 4. 响应式设计测试
- [ ] 在不同窗口大小下布局正常
- [ ] 移动端视图适配良好
- [ ] 按钮和文字大小合适

### 5. 交互效果测试
- [ ] 按钮有悬停效果
- [ ] 上传区域有悬停效果
- [ ] 动画效果流畅
- [ ] 状态转换自然

## 🚨 常见问题排查

### 问题1：页面无法打开
**可能原因**：
- 文件路径错误
- 浏览器安全设置
- 文件损坏

**解决方案**：
1. 检查文件是否存在
2. 尝试不同浏览器
3. 检查文件权限

### 问题2：测试面板不显示
**可能原因**：
- 未添加 `?test=true` 参数
- JavaScript未加载

**解决方案**：
1. 确保URL包含 `?test=true`
2. 检查浏览器控制台错误
3. 刷新页面

### 问题3：按钮点击无响应
**可能原因**：
- JavaScript错误
- 事件绑定失败

**解决方案**：
1. 打开浏览器开发者工具
2. 查看控制台错误信息
3. 检查网络请求

### 问题4：文件上传不工作
**可能原因**：
- 文件类型不支持
- 浏览器安全限制

**解决方案**：
1. 使用支持的文件格式（PDF、DOC、DOCX、CSV、XLS、XLSX）
2. 检查文件大小
3. 尝试不同文件

## 📞 技术支持

如果遇到问题，请提供以下信息：
1. 使用的浏览器和版本
2. 操作系统版本
3. 具体的错误信息
4. 复现步骤

## ✅ 测试完成确认

完成所有测试项目后，请确认：
- [ ] 所有基本功能正常
- [ ] 测试模式功能完整
- [ ] 界面显示正确
- [ ] 交互效果良好

**测试日期**: ___________
**测试人员**: ___________
**测试结果**: ___________

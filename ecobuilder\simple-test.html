<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-button {
            padding: 15px 30px;
            margin: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .upload-area {
            border: 2px dashed #ccc;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            border-radius: 8px;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        .ai-button {
            width: 100%;
            padding: 20px;
            font-size: 18px;
            font-weight: bold;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 20px 0;
        }
        .ai-button.red { background: #dc3545; color: white; }
        .ai-button.blue { background: #007bff; color: white; }
        .ai-button.green { background: #28a745; color: white; }
        .ai-button.gray { background: #6c757d; color: white; }
        .status-log {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简单功能测试</h1>
        <p>这是一个简化的测试页面，用于验证基本功能。</p>
        
        <h2>📁 文件上传测试</h2>
        <div class="upload-area" onclick="triggerFileInput()">
            <input type="file" id="fileInput" style="display: none;" onchange="handleFileSelect(event)">
            <p>📄 点击此处选择文件</p>
            <p style="font-size: 14px; color: #666;">支持 PDF, DOC, DOCX, CSV, XLS, XLSX</p>
        </div>
        
        <div>
            <button class="test-button" onclick="testUploadStatus()">测试上传状态</button>
            <button class="test-button" onclick="testUploadSuccess()">测试成功状态</button>
            <button class="test-button" onclick="testUploadError()">测试错误状态</button>
            <button class="test-button" onclick="resetUpload()">重置状态</button>
        </div>
        
        <h2>🤖 AI按钮测试</h2>
        <button class="ai-button red" id="aiButton" onclick="handleAIClick()">
            🚀 开始AI生态分析
        </button>
        
        <div>
            <button class="test-button" onclick="setAIProcessing()">设置处理状态</button>
            <button class="test-button" onclick="setAIEvaluation()">设置评估状态</button>
            <button class="test-button" onclick="setAISelection()">设置选择状态</button>
            <button class="test-button" onclick="resetAI()">重置AI按钮</button>
        </div>
        
        <h2>📋 状态日志</h2>
        <div class="status-log" id="statusLog">页面已加载...\n</div>
        <button class="test-button" onclick="clearLog()">清空日志</button>
        
        <div style="margin-top: 30px; padding: 20px; background: #e8f5e8; border-radius: 8px;">
            <h3>✅ 测试说明</h3>
            <ul>
                <li>点击文件上传区域测试文件选择</li>
                <li>使用测试按钮验证各种状态</li>
                <li>点击AI按钮查看状态循环</li>
                <li>查看状态日志了解操作记录</li>
            </ul>
        </div>
    </div>

    <script>
        let aiStage = 0;
        
        function log(message) {
            const logElement = document.getElementById('statusLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function triggerFileInput() {
            log('触发文件选择器');
            document.getElementById('fileInput').click();
        }
        
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                log(`文件已选择: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`);
                testUploadStatus();
                setTimeout(() => {
                    testUploadSuccess();
                }, 2000);
            }
        }
        
        function testUploadStatus() {
            log('测试上传状态 - 显示上传中...');
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.innerHTML = '<p>⏳ 正在上传文件...</p><p style="font-size: 14px; color: #666;">请稍候</p>';
            uploadArea.style.borderColor = '#007bff';
            uploadArea.style.background = '#e3f2fd';
        }
        
        function testUploadSuccess() {
            log('测试上传成功状态');
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.innerHTML = '<p>✅ 文件上传成功！</p><p style="font-size: 14px; color: #666;">文件已成功上传并验证</p>';
            uploadArea.style.borderColor = '#28a745';
            uploadArea.style.background = '#e8f5e8';
        }
        
        function testUploadError() {
            log('测试上传错误状态');
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.innerHTML = '<p>❌ 上传失败</p><p style="font-size: 14px; color: #666;">请检查文件格式和大小</p>';
            uploadArea.style.borderColor = '#dc3545';
            uploadArea.style.background = '#ffebee';
        }
        
        function resetUpload() {
            log('重置上传状态');
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.innerHTML = '<input type="file" id="fileInput" style="display: none;" onchange="handleFileSelect(event)"><p>📄 点击此处选择文件</p><p style="font-size: 14px; color: #666;">支持 PDF, DOC, DOCX, CSV, XLS, XLSX</p>';
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.background = 'white';
            uploadArea.onclick = triggerFileInput;
        }
        
        function handleAIClick() {
            log('AI按钮被点击');
            aiStage = (aiStage + 1) % 4;
            const button = document.getElementById('aiButton');
            
            switch(aiStage) {
                case 1:
                    log('进入处理阶段');
                    button.className = 'ai-button gray';
                    button.textContent = '⏳ 处理中...';
                    break;
                case 2:
                    log('进入评估阶段');
                    button.className = 'ai-button blue';
                    button.textContent = '🔍 评估合作伙伴需求';
                    break;
                case 3:
                    log('进入选择阶段');
                    button.className = 'ai-button green';
                    button.textContent = '✅ 选择合作伙伴';
                    break;
                case 0:
                    log('重置到初始状态');
                    button.className = 'ai-button red';
                    button.textContent = '🚀 开始AI生态分析';
                    break;
            }
        }
        
        function setAIProcessing() {
            log('手动设置处理状态');
            const button = document.getElementById('aiButton');
            button.className = 'ai-button gray';
            button.textContent = '⏳ 处理中...';
            aiStage = 1;
        }
        
        function setAIEvaluation() {
            log('手动设置评估状态');
            const button = document.getElementById('aiButton');
            button.className = 'ai-button blue';
            button.textContent = '🔍 评估合作伙伴需求';
            aiStage = 2;
        }
        
        function setAISelection() {
            log('手动设置选择状态');
            const button = document.getElementById('aiButton');
            button.className = 'ai-button green';
            button.textContent = '✅ 选择合作伙伴';
            aiStage = 3;
        }
        
        function resetAI() {
            log('重置AI按钮');
            const button = document.getElementById('aiButton');
            button.className = 'ai-button red';
            button.textContent = '🚀 开始AI生态分析';
            aiStage = 0;
        }
        
        function clearLog() {
            document.getElementById('statusLog').textContent = '';
            log('日志已清空');
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，所有功能已就绪');
        });
    </script>
</body>
</html>

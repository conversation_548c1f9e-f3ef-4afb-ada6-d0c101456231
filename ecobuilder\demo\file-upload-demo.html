<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcoBuilder 文件上传演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .upload-area {
            transition: all 0.3s ease;
        }
        .upload-area.dragover {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        .progress-bar {
            transition: width 0.3s ease;
        }
        .status-icon {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .success-icon {
            animation: bounce 0.5s ease;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-2">EcoBuilder 文件上传系统</h1>
            <p class="text-lg text-gray-600">支持拖拽上传，实时状态反馈</p>
        </div>

        <!-- Upload Area -->
        <div class="max-w-2xl mx-auto">
            <div id="uploadArea" class="upload-area border-2 border-dashed border-gray-300 rounded-lg p-8 text-center bg-white cursor-pointer hover:border-gray-400">
                <div id="uploadContent">
                    <div id="uploadIcon" class="mx-auto mb-4">
                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                    </div>
                    <h3 id="uploadTitle" class="text-lg font-medium text-gray-900 mb-2">点击或拖拽文件到此处上传</h3>
                    <p id="uploadSubtitle" class="text-sm text-gray-500">支持 PDF, DOC, DOCX, CSV, XLSX, TXT, PNG, JPG 格式</p>
                    <p class="text-xs text-gray-400 mt-2">最大文件大小：20MB，最多10个文件</p>
                </div>

                <!-- Progress Bar -->
                <div id="progressContainer" class="hidden mt-4">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progressBar" class="progress-bar bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                    </div>
                    <p id="progressText" class="text-sm text-gray-600 mt-2">上传中... 0%</p>
                </div>

                <!-- Status Messages -->
                <div id="statusMessage" class="hidden mt-4">
                    <div id="successMessage" class="hidden text-green-600">
                        <div class="success-icon inline-block">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <span class="ml-2 font-medium">上传成功！</span>
                    </div>
                    <div id="errorMessage" class="hidden text-red-600">
                        <div class="inline-block">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                        <span class="ml-2 font-medium">上传失败</span>
                    </div>
                </div>

                <!-- Reset Button -->
                <button id="resetButton" class="hidden mt-4 px-4 py-2 text-sm text-gray-600 hover:text-gray-800 underline">
                    重新上传
                </button>
            </div>

            <input type="file" id="fileInput" class="hidden" multiple accept=".pdf,.doc,.docx,.csv,.xlsx,.txt,.png,.jpg,.jpeg">
        </div>

        <!-- File List -->
        <div id="fileList" class="max-w-4xl mx-auto mt-8 hidden">
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">已上传文件</h2>
                </div>
                <div id="fileListContent" class="divide-y divide-gray-200">
                    <!-- Files will be added here dynamically -->
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div id="statistics" class="max-w-2xl mx-auto mt-8 hidden">
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">上传统计</h3>
                <div class="grid grid-cols-3 gap-4 text-center">
                    <div>
                        <div id="totalFiles" class="text-2xl font-bold text-blue-600">0</div>
                        <div class="text-sm text-gray-500">总文件数</div>
                    </div>
                    <div>
                        <div id="totalSize" class="text-2xl font-bold text-green-600">0 KB</div>
                        <div class="text-sm text-gray-500">总大小</div>
                    </div>
                    <div>
                        <div id="successRate" class="text-2xl font-bold text-purple-600">100%</div>
                        <div class="text-sm text-gray-500">成功率</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class FileUploadDemo {
            constructor() {
                this.uploadArea = document.getElementById('uploadArea');
                this.fileInput = document.getElementById('fileInput');
                this.uploadContent = document.getElementById('uploadContent');
                this.progressContainer = document.getElementById('progressContainer');
                this.progressBar = document.getElementById('progressBar');
                this.progressText = document.getElementById('progressText');
                this.statusMessage = document.getElementById('statusMessage');
                this.successMessage = document.getElementById('successMessage');
                this.errorMessage = document.getElementById('errorMessage');
                this.resetButton = document.getElementById('resetButton');
                this.fileList = document.getElementById('fileList');
                this.fileListContent = document.getElementById('fileListContent');
                this.statistics = document.getElementById('statistics');

                this.uploadedFiles = [];
                this.currentStatus = 'idle'; // idle, uploading, success, error

                this.initializeEventListeners();
            }

            initializeEventListeners() {
                // Click to upload
                this.uploadArea.addEventListener('click', () => {
                    if (this.currentStatus === 'idle') {
                        this.fileInput.click();
                    }
                });

                // File input change
                this.fileInput.addEventListener('change', (e) => {
                    this.handleFiles(e.target.files);
                });

                // Drag and drop
                this.uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    this.uploadArea.classList.add('dragover');
                });

                this.uploadArea.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    this.uploadArea.classList.remove('dragover');
                });

                this.uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    this.uploadArea.classList.remove('dragover');
                    this.handleFiles(e.dataTransfer.files);
                });

                // Reset button
                this.resetButton.addEventListener('click', () => {
                    this.resetUpload();
                });
            }

            async handleFiles(files) {
                if (files.length === 0) return;

                // Validate files
                const validFiles = this.validateFiles(files);
                if (validFiles.length === 0) return;

                // Start upload
                this.setStatus('uploading');
                
                try {
                    const uploadedFiles = [];
                    
                    for (let i = 0; i < validFiles.length; i++) {
                        const file = validFiles[i];
                        const baseProgress = (i / validFiles.length) * 100;
                        
                        this.updateProgress(baseProgress, `上传中... ${file.name}`);
                        
                        // Simulate file upload (replace with actual API call)
                        const result = await this.uploadFile(file, (fileProgress) => {
                            const totalProgress = baseProgress + (fileProgress / validFiles.length);
                            this.updateProgress(totalProgress, `上传中... ${file.name} (${Math.round(fileProgress)}%)`);
                        });
                        
                        uploadedFiles.push(result);
                    }

                    this.uploadedFiles.push(...uploadedFiles);
                    this.setStatus('success');
                    this.updateFileList();
                    this.updateStatistics();
                    
                } catch (error) {
                    this.setStatus('error', error.message);
                }
            }

            validateFiles(files) {
                const validFiles = [];
                const allowedTypes = [
                    'application/pdf',
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'text/csv',
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'text/plain',
                    'image/jpeg',
                    'image/png'
                ];
                const maxSize = 20 * 1024 * 1024; // 20MB

                for (const file of files) {
                    if (!allowedTypes.includes(file.type)) {
                        alert(`不支持的文件类型: ${file.name}`);
                        continue;
                    }
                    if (file.size > maxSize) {
                        alert(`文件过大: ${file.name} (最大20MB)`);
                        continue;
                    }
                    validFiles.push(file);
                }

                if (validFiles.length > 10) {
                    alert('最多只能上传10个文件');
                    return validFiles.slice(0, 10);
                }

                return validFiles;
            }

            async uploadFile(file, onProgress) {
                return new Promise((resolve, reject) => {
                    const formData = new FormData();
                    formData.append('file', file);

                    const xhr = new XMLHttpRequest();

                    xhr.upload.addEventListener('progress', (event) => {
                        if (event.lengthComputable) {
                            const progress = (event.loaded / event.total) * 100;
                            onProgress(progress);
                        }
                    });

                    xhr.addEventListener('load', () => {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                resolve({
                                    id: response.data.id || `file_${Date.now()}`,
                                    name: file.name,
                                    size: file.size,
                                    type: file.type,
                                    url: response.data.url || URL.createObjectURL(file),
                                    uploadedAt: new Date()
                                });
                            } catch (error) {
                                // Fallback for demo
                                resolve({
                                    id: `file_${Date.now()}_${Math.random()}`,
                                    name: file.name,
                                    size: file.size,
                                    type: file.type,
                                    url: URL.createObjectURL(file),
                                    uploadedAt: new Date()
                                });
                            }
                        } else {
                            reject(new Error(`上传失败: ${xhr.status}`));
                        }
                    });

                    xhr.addEventListener('error', () => {
                        reject(new Error('网络错误'));
                    });

                    // For demo, use a mock endpoint or comment out for local testing
                    xhr.open('POST', '/api/files/upload');
                    xhr.send(formData);
                });
            }

            setStatus(status, errorMsg = '') {
                this.currentStatus = status;
                
                // Hide all status elements
                this.uploadContent.style.display = status === 'idle' ? 'block' : 'none';
                this.progressContainer.style.display = status === 'uploading' ? 'block' : 'none';
                this.statusMessage.style.display = (status === 'success' || status === 'error') ? 'block' : 'none';
                this.successMessage.style.display = status === 'success' ? 'flex' : 'none';
                this.errorMessage.style.display = status === 'error' ? 'flex' : 'none';
                this.resetButton.style.display = (status === 'success' || status === 'error') ? 'block' : 'none';

                // Update upload area styling
                this.uploadArea.className = 'upload-area border-2 border-dashed rounded-lg p-8 text-center cursor-pointer';
                
                if (status === 'uploading') {
                    this.uploadArea.className += ' border-blue-300 bg-blue-50';
                } else if (status === 'success') {
                    this.uploadArea.className += ' border-green-300 bg-green-50';
                } else if (status === 'error') {
                    this.uploadArea.className += ' border-red-300 bg-red-50';
                    if (errorMsg) {
                        this.errorMessage.querySelector('span').textContent = errorMsg;
                    }
                } else {
                    this.uploadArea.className += ' border-gray-300 bg-white hover:border-gray-400';
                }
            }

            updateProgress(progress, text) {
                this.progressBar.style.width = `${progress}%`;
                this.progressText.textContent = text;
            }

            resetUpload() {
                this.setStatus('idle');
                this.fileInput.value = '';
                this.updateProgress(0, '');
            }

            updateFileList() {
                if (this.uploadedFiles.length === 0) {
                    this.fileList.style.display = 'none';
                    return;
                }

                this.fileList.style.display = 'block';
                this.fileListContent.innerHTML = '';

                this.uploadedFiles.forEach(file => {
                    const fileElement = this.createFileElement(file);
                    this.fileListContent.appendChild(fileElement);
                });
            }

            createFileElement(file) {
                const div = document.createElement('div');
                div.className = 'px-6 py-4 flex items-center justify-between';
                
                div.innerHTML = `
                    <div class="flex items-center space-x-3">
                        <div class="text-2xl">${this.getFileIcon(file.type)}</div>
                        <div>
                            <div class="text-sm font-medium text-gray-900">${file.name}</div>
                            <div class="text-sm text-gray-500">${this.formatFileSize(file.size)} • ${file.uploadedAt.toLocaleString()}</div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="text-green-500">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <button onclick="window.open('${file.url}', '_blank')" class="text-blue-600 hover:text-blue-800 text-sm">查看</button>
                    </div>
                `;
                
                return div;
            }

            getFileIcon(type) {
                if (type.includes('image')) return '🖼️';
                if (type.includes('pdf')) return '📄';
                if (type.includes('word') || type.includes('document')) return '📝';
                if (type.includes('excel') || type.includes('spreadsheet')) return '📊';
                if (type.includes('text')) return '📃';
                return '📁';
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            updateStatistics() {
                if (this.uploadedFiles.length === 0) {
                    this.statistics.style.display = 'none';
                    return;
                }

                this.statistics.style.display = 'block';
                
                const totalFiles = this.uploadedFiles.length;
                const totalSize = this.uploadedFiles.reduce((sum, file) => sum + file.size, 0);
                
                document.getElementById('totalFiles').textContent = totalFiles;
                document.getElementById('totalSize').textContent = this.formatFileSize(totalSize);
                document.getElementById('successRate').textContent = '100%';
            }
        }

        // Initialize the demo when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new FileUploadDemo();
        });
    </script>
</body>
</html>

# EcoBuilder × Dify AI工作流集成平台

## 项目概述

这是一个专业的UI界面，将EcoBuilder多智能体生态合作伙伴开发系统与Dify AI工作流平台完美集成。通过现代化的Web界面，用户可以轻松与AI工作流进行交互，实现智能化的合作伙伴开发流程。

## 功能特性

### 🤖 AI工作流对话
- 实时与Dify AI工作流进行对话交互
- 支持多种工作流类型选择
- 智能响应和建议生成
- 对话历史记录和管理

### 📊 工作流执行结果
- 实时显示工作流执行结果
- 结构化的结果展示
- 结果导出功能
- 时间戳和追踪记录

### 🔗 EcoBuilder集成状态
- 5个核心智能体集成状态监控
- 实时连接状态显示
- 系统健康检查

### 🎨 现代化UI设计
- 响应式设计，支持多设备
- 毛玻璃效果和现代动画
- 直观的用户交互体验
- 专业的视觉设计

## 技术架构

### 前端技术栈
- **HTML5** - 语义化标记
- **CSS3** - 现代样式和动画
- **JavaScript ES6+** - 交互逻辑
- **Font Awesome** - 图标库
- **Google Fonts** - 字体支持

### API集成
- **Dify API v1.6.0** - AI工作流引擎
- **RESTful API** - 标准HTTP接口
- **JSON数据格式** - 数据交换
- **Bearer Token认证** - 安全认证

## 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd ecobuilder

# 确保有Web服务器环境（可选）
# 可以直接在浏览器中打开HTML文件
```

### 2. 配置Dify API
在 `dify-integration.html` 文件中找到配置部分：

```javascript
const DIFY_CONFIG = {
    apiKey: 'app-ZRuKpcHUrE5E0zTtNQLddDCc',  // 您的Dify API密钥
    baseUrl: 'https://api.dify.ai/v1',        // Dify API基础URL
    workflowId: '8cc0dc2f-37ed-4dbd-b647-0fc278810788'  // 工作流ID
};
```

### 3. 启动应用
```bash
# 方法1: 直接在浏览器中打开
open dify-integration.html

# 方法2: 使用本地服务器
python -m http.server 8000
# 然后访问 http://localhost:8000/dify-integration.html

# 方法3: 使用Node.js服务器
npx serve .
# 然后访问提示的URL
```

## 使用指南

### 基本操作

1. **选择工作流类型**
   - 在右侧控制面板选择合适的工作流
   - 支持5种核心工作流类型

2. **发起对话**
   - 在聊天输入框中输入您的问题或需求
   - 点击发送按钮或按Enter键

3. **查看结果**
   - AI响应会显示在聊天区域
   - 详细结果会保存在工作流结果面板

4. **快速操作**
   - 使用右侧快速操作按钮
   - 一键执行常用工作流

### 高级功能

1. **导出结果**
   - 点击"导出结果"按钮
   - 下载包含所有执行结果的文本文件

2. **清空对话**
   - 点击"清空对话"按钮
   - 重置对话历史和结果

3. **状态监控**
   - 查看Dify API连接状态
   - 监控EcoBuilder智能体集成状态

## 工作流类型

### 1. 合作伙伴发现工作流
- **功能**: 智能发现和筛选潜在合作伙伴
- **输入**: 行业要求、规模偏好、地域限制
- **输出**: 推荐合作伙伴列表和匹配度分析

### 2. 合作伙伴招募工作流
- **功能**: 制定招募策略和执行计划
- **输入**: 目标公司信息、合作模式
- **输出**: 详细招募策略和时间规划

### 3. 培训方案设计工作流
- **功能**: 设计个性化培训计划
- **输入**: 培训对象、技能要求、时间安排
- **输出**: 完整培训方案和课程设计

### 4. 商机分析工作流
- **功能**: 分析市场机会和商业潜力
- **输入**: 市场领域、时间范围、投资预算
- **输出**: 商机分析报告和建议

### 5. 市场拓展工作流
- **功能**: 制定市场拓展策略
- **输入**: 目标市场、资源配置、合作伙伴
- **输出**: 拓展计划和营销活动方案

## API配置说明

### Dify API密钥获取
1. 登录Dify云平台
2. 进入应用管理页面
3. 选择您的工作流应用
4. 在API访问页面获取密钥

### 工作流ID配置
1. 在Dify平台找到您的工作流
2. 从URL中提取工作流ID
3. 更新配置文件中的workflowId

## 故障排除

### 常见问题

**Q: API连接失败怎么办？**
A: 检查API密钥是否正确，网络连接是否正常，Dify服务是否可用。

**Q: 工作流执行超时？**
A: 复杂工作流可能需要更长时间，请耐心等待或检查工作流配置。

**Q: 结果显示异常？**
A: 检查工作流输出格式，确保返回的数据结构正确。

### 调试模式
在浏览器开发者工具中查看控制台日志：
```javascript
// 启用详细日志
localStorage.setItem('debug', 'true');
```

## 部署说明

### 生产环境部署
1. 将文件上传到Web服务器
2. 配置HTTPS证书（推荐）
3. 设置正确的API密钥
4. 配置CDN加速（可选）

### 安全考虑
- 不要在前端代码中暴露敏感API密钥
- 建议通过后端代理API调用
- 启用CORS安全策略
- 定期更新API密钥

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

### 开发环境设置
1. Fork项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues页面
- 邮箱: <EMAIL>
- 官网: https://ecobuilder.ai

---

**EcoBuilder × Dify** - 让AI驱动您的生态合作伙伴开发！

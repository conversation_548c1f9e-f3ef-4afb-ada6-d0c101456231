import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { Dashboard } from './pages/Dashboard';
import { Layout } from './components/layout/Layout';
import './App.css';

// Import mock hooks for development
import { useAgentsMock as useAgents } from './hooks/useAgents';
import { useTasksMock as useTasks } from './hooks/useTasks';
import { useWorkflowsMock as useWorkflows } from './hooks/useWorkflows';
import { useWebSocketMock as useWebSocket } from './hooks/useWebSocket';

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="agents" element={<div>Agents Page (Coming Soon)</div>} />
            <Route path="partners" element={<div>Partners Page (Coming Soon)</div>} />
            <Route path="opportunities" element={<div>Opportunities Page (Coming Soon)</div>} />
            <Route path="workflows" element={<div>Workflows Page (Coming Soon)</div>} />
            <Route path="analytics" element={<div>Analytics Page (Coming Soon)</div>} />
            <Route path="settings" element={<div>Settings Page (Coming Soon)</div>} />
          </Route>
        </Routes>
        
        {/* Global toast notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              duration: 3000,
              iconTheme: {
                primary: '#4ade80',
                secondary: '#fff',
              },
            },
            error: {
              duration: 5000,
              iconTheme: {
                primary: '#ef4444',
                secondary: '#fff',
              },
            },
          }}
        />
      </div>
    </Router>
  );
}

export default App;

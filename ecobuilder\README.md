# EcoBuilder - 多智能体生态合作伙伴开发系统

EcoBuilder 是一个基于AI的多智能体系统，专门用于自动化生态合作伙伴开发的完整流程。通过5个专业智能体的协同工作，实现从合作伙伴发现到市场拓展的全流程自动化。

## 🌟 核心特性

### 5个专业智能体
- **PartnerScout** - 合作伙伴发现和筛选
- **PartnerRecruiter** - 合作伙伴招募和谈判
- **PartnerTrainer** - 销售团队培训和赋能
- **OpportunityManager** - 商机管理和跟踪
- **MarketExpander** - 市场拓展和活动管理

### 核心功能
- 🤖 AI驱动的智能决策
- 🔄 自动化工作流引擎
- 📊 实时数据分析和报告
- 🔗 第三方系统集成 (CRM, 邮件, 日历)
- 💬 多模态交互界面
- 📱 响应式Web应用

## 🏗️ 技术架构

### 前端
- **React 18** + TypeScript
- **Tailwind CSS** - 样式框架
- **Vite** - 构建工具
- **Socket.io** - 实时通信
- **React Query** - 数据管理

### 后端
- **Node.js** + Express + TypeScript
- **Prisma ORM** - 数据库操作
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话
- **Socket.io** - 实时通信

### AI集成
- **OpenAI GPT-4** - 核心AI引擎
- **自然语言处理** - 文本分析
- **机器学习** - 智能推荐

### 部署
- **Docker** + Docker Compose
- **PostgreSQL** 数据库
- **Redis** 缓存
- **Nginx** 反向代理

## 🚀 快速开始

### 环境要求
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd ecobuilder
```

2. **环境配置**
```bash
cp .env.example .env
# 编辑 .env 文件，配置必要的环境变量
```

3. **启动数据库**
```bash
docker-compose up -d postgres redis
```

4. **安装依赖**
```bash
# 后端依赖
cd backend
npm install

# 前端依赖
cd ../frontend
npm install
```

5. **数据库初始化**
```bash
cd backend
npx prisma generate
npx prisma db push
npx prisma db seed
```

6. **启动开发服务器**
```bash
# 启动后端 (终端1)
cd backend
npm run dev

# 启动前端 (终端2)
cd frontend
npm run dev
```

7. **访问应用**
- 前端: http://localhost:5173
- 后端API: http://localhost:3000
- 数据库管理: http://localhost:5555 (Prisma Studio)

## 📁 项目结构

```
ecobuilder/
├── frontend/                 # React前端应用
│   ├── src/
│   │   ├── components/      # 可复用组件
│   │   ├── pages/          # 页面组件
│   │   ├── agents/         # 智能体交互组件
│   │   ├── hooks/          # 自定义Hooks
│   │   ├── services/       # API服务
│   │   ├── types/          # TypeScript类型
│   │   └── utils/          # 工具函数
│   └── package.json
├── backend/                 # Node.js后端应用
│   ├── src/
│   │   ├── agents/         # 智能体实现
│   │   ├── controllers/    # 控制器
│   │   ├── services/       # 业务逻辑
│   │   ├── models/         # 数据模型
│   │   ├── middleware/     # 中间件
│   │   ├── routes/         # 路由定义
│   │   └── utils/          # 工具函数
│   ├── prisma/             # 数据库模式
│   └── package.json
├── shared/                  # 共享类型和工具
│   ├── types/              # 共享类型定义
│   └── utils/              # 共享工具函数
├── docker-compose.yml       # Docker编排
├── .env.example            # 环境变量示例
└── README.md
```

## 🔧 开发命令

### 后端命令
```bash
npm run dev          # 开发模式启动
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run test         # 运行测试
npm run db:generate  # 生成Prisma客户端
npm run db:migrate   # 运行数据库迁移
npm run db:studio    # 启动数据库管理界面
```

### 前端命令
```bash
npm run dev          # 开发模式启动
npm run build        # 构建生产版本
npm run preview      # 预览生产版本
npm run test         # 运行测试
npm run lint         # 代码检查
npm run format       # 代码格式化
```

## 🧪 测试

### 运行测试
```bash
# 后端测试
cd backend
npm run test
npm run test:coverage

# 前端测试
cd frontend
npm run test
npm run test:coverage
```

### 测试覆盖率
- 目标: >90% 代码覆盖率
- 包含单元测试、集成测试、端到端测试

## 📊 智能体工作流

### 1. 合作伙伴发现流程
```
PartnerScout → 分析现有资源 → AI匹配算法 → 评分排序 → 推荐列表
```

### 2. 合作伙伴招募流程
```
PartnerRecruiter → CEO联系 → 会议安排 → 协议谈判 → 合作达成
```

### 3. 培训赋能流程
```
PartnerTrainer → 需求评估 → 培训计划 → 能力认证 → 持续支持
```

### 4. 商机管理流程
```
OpportunityManager → 商机识别 → 销售计划 → 进度跟踪 → 成功案例
```

### 5. 市场拓展流程
```
MarketExpander → 活动策划 → 联合营销 → 商机复制 → 效果分析
```

## 🔌 第三方集成

### CRM系统
- Salesforce
- HubSpot
- Pipedrive

### 通信工具
- SendGrid (邮件)
- Twilio (短信)
- Slack (团队协作)

### 日历系统
- Google Calendar
- Outlook Calendar

### 文件存储
- AWS S3
- Google Drive

## 🛡️ 安全特性

- JWT身份验证
- 数据加密传输
- API限流保护
- 输入验证和清理
- 审计日志记录

## 📈 监控和分析

- 应用性能监控 (APM)
- 错误追踪 (Sentry)
- 业务指标分析
- 实时状态监控

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持

如有问题或建议，请：
- 创建 Issue
- 发送邮件至 <EMAIL>
- 查看文档 [docs.ecobuilder.com](https://docs.ecobuilder.com)

---

**EcoBuilder Team** - 让AI驱动生态合作伙伴发展 🚀

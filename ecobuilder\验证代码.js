// 🧪 HTML文件功能验证脚本
// 这个脚本可以在浏览器控制台中运行来验证功能

console.log('🧪 开始验证Dify集成功能...');

// 验证基本元素存在
function verifyBasicElements() {
    console.log('\n📋 验证基本元素...');
    
    const elements = {
        'testPanel': '测试面板',
        'testUploadArea': '测试上传区域',
        'testAiButton': '测试AI按钮',
        'testStatusLog': '状态日志',
        'uploadArea': '主上传区域',
        'aiButton': '主AI按钮'
    };
    
    let allFound = true;
    
    for (const [id, name] of Object.entries(elements)) {
        const element = document.getElementById(id);
        if (element) {
            console.log(`✅ ${name} 存在`);
        } else {
            console.log(`❌ ${name} 不存在`);
            allFound = false;
        }
    }
    
    return allFound;
}

// 验证CSS样式
function verifyStyles() {
    console.log('\n🎨 验证CSS样式...');
    
    const testButton = document.querySelector('.test-button');
    const aiButton = document.querySelector('.ai-button');
    const uploadArea = document.querySelector('.upload-area');
    
    if (testButton) {
        console.log('✅ 测试按钮样式存在');
    } else {
        console.log('❌ 测试按钮样式缺失');
    }
    
    if (aiButton) {
        console.log('✅ AI按钮样式存在');
    } else {
        console.log('❌ AI按钮样式缺失');
    }
    
    if (uploadArea) {
        console.log('✅ 上传区域样式存在');
    } else {
        console.log('❌ 上传区域样式缺失');
    }
}

// 验证JavaScript函数
function verifyFunctions() {
    console.log('\n⚙️ 验证JavaScript函数...');
    
    const functions = [
        'triggerTestFileInput',
        'handleTestFileSelect',
        'testUploadStatus',
        'testUploadSuccess',
        'testUploadError',
        'resetTestUpload',
        'handleTestAIButtonClick',
        'testAIProcessing',
        'testAIEvaluation',
        'testAISelection',
        'resetTestAIButton',
        'clearTestLog',
        'testLog'
    ];
    
    let allFound = true;
    
    functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ 函数 ${funcName} 存在`);
        } else {
            console.log(`❌ 函数 ${funcName} 不存在`);
            allFound = false;
        }
    });
    
    return allFound;
}

// 测试文件上传功能
function testFileUpload() {
    console.log('\n📁 测试文件上传功能...');
    
    try {
        // 测试上传状态
        if (typeof testUploadStatus === 'function') {
            testUploadStatus();
            console.log('✅ 上传状态测试成功');
        }
        
        // 测试成功状态
        setTimeout(() => {
            if (typeof testUploadSuccess === 'function') {
                testUploadSuccess();
                console.log('✅ 成功状态测试成功');
            }
        }, 1000);
        
        // 测试错误状态
        setTimeout(() => {
            if (typeof testUploadError === 'function') {
                testUploadError();
                console.log('✅ 错误状态测试成功');
            }
        }, 2000);
        
        // 重置状态
        setTimeout(() => {
            if (typeof resetTestUpload === 'function') {
                resetTestUpload();
                console.log('✅ 重置状态测试成功');
            }
        }, 3000);
        
    } catch (error) {
        console.log('❌ 文件上传测试失败:', error.message);
    }
}

// 测试AI按钮功能
function testAIButton() {
    console.log('\n🤖 测试AI按钮功能...');
    
    try {
        // 测试处理状态
        if (typeof testAIProcessing === 'function') {
            testAIProcessing();
            console.log('✅ 处理状态测试成功');
        }
        
        // 测试评估状态
        setTimeout(() => {
            if (typeof testAIEvaluation === 'function') {
                testAIEvaluation();
                console.log('✅ 评估状态测试成功');
            }
        }, 1000);
        
        // 测试选择状态
        setTimeout(() => {
            if (typeof testAISelection === 'function') {
                testAISelection();
                console.log('✅ 选择状态测试成功');
            }
        }, 2000);
        
        // 重置状态
        setTimeout(() => {
            if (typeof resetTestAIButton === 'function') {
                resetTestAIButton();
                console.log('✅ 重置状态测试成功');
            }
        }, 3000);
        
    } catch (error) {
        console.log('❌ AI按钮测试失败:', error.message);
    }
}

// 测试日志功能
function testLogging() {
    console.log('\n📋 测试日志功能...');
    
    try {
        if (typeof testLog === 'function') {
            testLog('这是一条测试日志');
            console.log('✅ 日志功能正常');
        } else {
            console.log('❌ 日志功能不存在');
        }
    } catch (error) {
        console.log('❌ 日志测试失败:', error.message);
    }
}

// 检查测试模式
function checkTestMode() {
    console.log('\n🧪 检查测试模式...');
    
    const testPanel = document.getElementById('testPanel');
    const isTestMode = window.location.search.includes('test=true');
    
    if (isTestMode) {
        console.log('✅ 当前为测试模式');
        if (testPanel && testPanel.style.display !== 'none') {
            console.log('✅ 测试面板已显示');
        } else {
            console.log('❌ 测试面板未显示');
        }
    } else {
        console.log('ℹ️ 当前为正常模式，添加 ?test=true 启用测试模式');
    }
}

// 运行完整验证
function runFullVerification() {
    console.log('🚀 开始完整功能验证...');
    
    const results = {
        elements: verifyBasicElements(),
        functions: verifyFunctions()
    };
    
    verifyStyles();
    checkTestMode();
    
    // 延迟运行功能测试
    setTimeout(() => {
        testLogging();
        testFileUpload();
        testAIButton();
        
        console.log('\n📊 验证结果汇总:');
        console.log(`元素检查: ${results.elements ? '✅ 通过' : '❌ 失败'}`);
        console.log(`函数检查: ${results.functions ? '✅ 通过' : '❌ 失败'}`);
        
        if (results.elements && results.functions) {
            console.log('\n🎉 所有基础验证通过！页面功能正常。');
        } else {
            console.log('\n⚠️ 部分验证失败，请检查页面加载。');
        }
    }, 1000);
}

// 自动运行验证（如果页面已加载）
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runFullVerification);
} else {
    runFullVerification();
}

// 导出验证函数供手动调用
window.verifyDifyIntegration = {
    runFullVerification,
    verifyBasicElements,
    verifyStyles,
    verifyFunctions,
    testFileUpload,
    testAIButton,
    testLogging,
    checkTestMode
};

console.log('\n💡 提示: 可以手动调用 verifyDifyIntegration.runFullVerification() 重新运行验证');

# EcoBuilder 实现指南

## 项目结构

```
ecobuilder/
├── frontend/                 # React前端应用
│   ├── src/
│   │   ├── components/      # 可复用组件
│   │   ├── pages/          # 页面组件
│   │   ├── agents/         # 智能体交互组件
│   │   ├── hooks/          # 自定义Hooks
│   │   ├── services/       # API服务
│   │   ├── types/          # TypeScript类型定义
│   │   └── utils/          # 工具函数
│   ├── package.json
│   └── vite.config.ts
├── backend/                 # Node.js后端应用
│   ├── src/
│   │   ├── agents/         # 智能体实现
│   │   ├── controllers/    # 控制器
│   │   ├── services/       # 业务逻辑
│   │   ├── models/         # 数据模型
│   │   ├── middleware/     # 中间件
│   │   ├── routes/         # 路由定义
│   │   ├── utils/          # 工具函数
│   │   └── config/         # 配置文件
│   ├── prisma/             # 数据库模式
│   └── package.json
├── shared/                  # 共享类型和工具
│   ├── types/              # 共享类型定义
│   └── utils/              # 共享工具函数
├── docker-compose.yml       # Docker编排文件
├── .env.example            # 环境变量示例
└── README.md               # 项目文档
```

## 核心代码实现

### 1. 智能体基础架构

```typescript
// shared/types/agent.ts
export interface Agent {
  id: string;
  name: string;
  type: AgentType;
  status: AgentStatus;
  capabilities: Capability[];
  tools: Tool[];
  context: AgentContext;
}

export enum AgentType {
  PARTNER_SCOUT = 'partner_scout',
  PARTNER_RECRUITER = 'partner_recruiter',
  PARTNER_TRAINER = 'partner_trainer',
  OPPORTUNITY_MANAGER = 'opportunity_manager',
  MARKET_EXPANDER = 'market_expander'
}

export enum AgentStatus {
  IDLE = 'idle',
  WORKING = 'working',
  ERROR = 'error',
  OFFLINE = 'offline'
}

export interface Task {
  id: string;
  type: string;
  priority: number;
  data: any;
  assignedAgent: string;
  status: TaskStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface Message {
  id: string;
  from: string;
  to: string;
  type: MessageType;
  content: any;
  timestamp: Date;
}
```

### 2. 数据库模型设计

```prisma
// backend/prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Partner {
  id          String        @id @default(cuid())
  companyName String
  industry    String
  size        CompanySize
  status      PartnerStatus @default(PROSPECT)
  contacts    Contact[]
  agreements  Agreement[]
  opportunities Opportunity[]
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  
  @@map("partners")
}

model Contact {
  id        String   @id @default(cuid())
  name      String
  email     String
  phone     String?
  position  String
  partnerId String
  partner   Partner  @relation(fields: [partnerId], references: [id])
  createdAt DateTime @default(now())
  
  @@map("contacts")
}

model Agreement {
  id          String        @id @default(cuid())
  title       String
  type        AgreementType
  status      AgreementStatus
  content     String
  signedAt    DateTime?
  partnerId   String
  partner     Partner       @relation(fields: [partnerId], references: [id])
  createdAt   DateTime      @default(now())
  
  @@map("agreements")
}

model Opportunity {
  id          String            @id @default(cuid())
  title       String
  description String
  value       Float
  stage       OpportunityStage
  probability Int
  partnerId   String
  partner     Partner           @relation(fields: [partnerId], references: [id])
  activities  Activity[]
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  
  @@map("opportunities")
}

model Activity {
  id            String      @id @default(cuid())
  type          ActivityType
  description   String
  status        ActivityStatus
  scheduledAt   DateTime?
  completedAt   DateTime?
  opportunityId String
  opportunity   Opportunity @relation(fields: [opportunityId], references: [id])
  agentId       String
  createdAt     DateTime    @default(now())
  
  @@map("activities")
}

enum CompanySize {
  STARTUP
  SMALL
  MEDIUM
  LARGE
  ENTERPRISE
}

enum PartnerStatus {
  PROSPECT
  CONTACTED
  NEGOTIATING
  ACTIVE
  INACTIVE
}

enum AgreementType {
  NDA
  PARTNERSHIP
  RESELLER
  DISTRIBUTOR
}

enum AgreementStatus {
  DRAFT
  REVIEW
  SIGNED
  EXPIRED
}

enum OpportunityStage {
  QUALIFICATION
  PROPOSAL
  NEGOTIATION
  CLOSED_WON
  CLOSED_LOST
}

enum ActivityType {
  CALL
  EMAIL
  MEETING
  TRAINING
  PROPOSAL
}

enum ActivityStatus {
  PLANNED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}
```

### 3. 智能体实现示例

```typescript
// backend/src/agents/PartnerScoutAgent.ts
import { Agent, Task, TaskResult } from '../types/agent';
import { CRMService } from '../services/CRMService';
import { AIService } from '../services/AIService';

export class PartnerScoutAgent implements Agent {
  id = 'partner-scout';
  name = 'Partner Scout';
  type = AgentType.PARTNER_SCOUT;
  status = AgentStatus.IDLE;
  
  constructor(
    private crmService: CRMService,
    private aiService: AIService
  ) {}

  async execute(task: Task): Promise<TaskResult> {
    this.status = AgentStatus.WORKING;
    
    try {
      switch (task.type) {
        case 'FIND_PARTNERS':
          return await this.findPartners(task.data);
        case 'SCORE_PARTNER':
          return await this.scorePartner(task.data);
        case 'ANALYZE_ECOSYSTEM':
          return await this.analyzeEcosystem(task.data);
        default:
          throw new Error(`Unknown task type: ${task.type}`);
      }
    } catch (error) {
      this.status = AgentStatus.ERROR;
      throw error;
    } finally {
      this.status = AgentStatus.IDLE;
    }
  }

  private async findPartners(criteria: any): Promise<TaskResult> {
    // 1. 分析现有客户和供应商
    const existingRelations = await this.crmService.getRelatedCompanies();
    
    // 2. 基于AI分析匹配潜在伙伴
    const aiAnalysis = await this.aiService.analyzePartnerFit({
      criteria,
      existingRelations,
      industryData: await this.getIndustryData()
    });
    
    // 3. 评分和排序
    const scoredPartners = await this.scorePartners(aiAnalysis.candidates);
    
    return {
      success: true,
      data: {
        partners: scoredPartners,
        analysis: aiAnalysis,
        recommendations: this.generateRecommendations(scoredPartners)
      }
    };
  }

  private async scorePartner(partnerData: any): Promise<TaskResult> {
    const score = await this.aiService.calculatePartnerScore({
      companySize: partnerData.size,
      industry: partnerData.industry,
      revenue: partnerData.revenue,
      marketPresence: partnerData.marketPresence,
      technicalCapability: partnerData.technicalCapability,
      strategicAlignment: partnerData.strategicAlignment
    });

    return {
      success: true,
      data: { score, factors: score.factors }
    };
  }
}
```

### 4. 工作流引擎

```typescript
// backend/src/services/WorkflowEngine.ts
export class WorkflowEngine {
  private agents: Map<string, Agent> = new Map();
  private workflows: Map<string, Workflow> = new Map();
  
  async executeWorkflow(workflowId: string, context: any): Promise<void> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) throw new Error(`Workflow ${workflowId} not found`);
    
    for (const stage of workflow.stages) {
      await this.executeStage(stage, context);
    }
  }
  
  private async executeStage(stage: WorkflowStage, context: any): Promise<void> {
    const agent = this.agents.get(stage.agentId);
    if (!agent) throw new Error(`Agent ${stage.agentId} not found`);
    
    const task: Task = {
      id: generateId(),
      type: stage.taskType,
      priority: stage.priority,
      data: { ...context, ...stage.parameters },
      assignedAgent: agent.id,
      status: TaskStatus.PENDING,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const result = await agent.execute(task);
    
    // 更新上下文用于下一阶段
    Object.assign(context, result.data);
  }
}
```

### 5. 前端智能体交互组件

```tsx
// frontend/src/components/AgentDashboard.tsx
import React, { useState, useEffect } from 'react';
import { Agent, AgentStatus } from '../types/agent';
import { useAgents } from '../hooks/useAgents';

export const AgentDashboard: React.FC = () => {
  const { agents, sendTask, getAgentStatus } = useAgents();
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {agents.map(agent => (
        <AgentCard
          key={agent.id}
          agent={agent}
          onSelect={setSelectedAgent}
          onSendTask={sendTask}
        />
      ))}
      
      {selectedAgent && (
        <AgentDetailModal
          agent={selectedAgent}
          onClose={() => setSelectedAgent(null)}
        />
      )}
    </div>
  );
};

const AgentCard: React.FC<{
  agent: Agent;
  onSelect: (agent: Agent) => void;
  onSendTask: (agentId: string, task: any) => void;
}> = ({ agent, onSelect, onSendTask }) => {
  const statusColor = {
    [AgentStatus.IDLE]: 'bg-green-500',
    [AgentStatus.WORKING]: 'bg-yellow-500',
    [AgentStatus.ERROR]: 'bg-red-500',
    [AgentStatus.OFFLINE]: 'bg-gray-500'
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">{agent.name}</h3>
        <div className={`w-3 h-3 rounded-full ${statusColor[agent.status]}`} />
      </div>
      
      <p className="text-gray-600 mb-4">
        {agent.capabilities.join(', ')}
      </p>
      
      <div className="flex space-x-2">
        <button
          onClick={() => onSelect(agent)}
          className="flex-1 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          详情
        </button>
        <button
          onClick={() => onSendTask(agent.id, { type: 'HEALTH_CHECK' })}
          className="flex-1 bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
        >
          测试
        </button>
      </div>
    </div>
  );
};
```

## 开发启动命令

### 1. 项目初始化
```bash
# 创建项目目录
mkdir ecobuilder && cd ecobuilder

# 初始化前端项目
npm create vite@latest frontend -- --template react-ts
cd frontend && npm install && cd ..

# 初始化后端项目
mkdir backend && cd backend
npm init -y
npm install express typescript prisma @prisma/client
npm install -D @types/node @types/express ts-node nodemon
cd ..

# 初始化共享类型
mkdir shared && cd shared
npm init -y
npm install typescript
cd ..
```

### 2. 开发环境启动
```bash
# 启动数据库
docker-compose up -d postgres

# 启动后端
cd backend
npm run dev

# 启动前端
cd frontend
npm run dev
```

### 3. 测试命令
```bash
# 运行单元测试
npm run test

# 运行集成测试
npm run test:integration

# 运行端到端测试
npm run test:e2e
```

这个实现指南提供了完整的技术架构和代码结构，您可以基于此开始 EcoBuilder 多智能体群系统的开发。每个智能体都有明确的职责分工，通过工作流引擎协调工作，实现生态合作伙伴开发的全流程自动化。

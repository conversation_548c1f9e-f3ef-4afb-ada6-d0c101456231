@echo off
setlocal enabledelayedexpansion

REM EcoBuilder Development Startup Script for Windows
REM This script starts all necessary services for development

echo 🌱 Starting EcoBuilder Multi-Agent System...

REM Check if Node.js is installed
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

REM Check if npm is installed
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] npm is not installed. Please install npm first.
    pause
    exit /b 1
)

echo [INFO] Dependencies check completed.

REM Change to project root
cd /d "%~dp0\.."

REM Ask user what to do
echo.
echo What would you like to do?
echo 1) Full setup (install deps, setup env, start dev servers)
echo 2) Install dependencies only
echo 3) Setup environment files only
echo 4) Start development servers only
echo 5) Quick start (assumes everything is already set up)
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto full_setup
if "%choice%"=="2" goto install_deps
if "%choice%"=="3" goto setup_env
if "%choice%"=="4" goto start_servers
if "%choice%"=="5" goto start_servers
echo [ERROR] Invalid choice. Exiting.
pause
exit /b 1

:full_setup
call :install_dependencies
call :setup_env
call :start_dev_servers
goto end

:install_deps
call :install_dependencies
goto end

:setup_env
call :setup_env
goto end

:start_servers
call :start_dev_servers
goto end

:install_dependencies
echo [STEP] Installing dependencies...

REM Install shared dependencies
echo [INFO] Installing shared dependencies...
cd shared
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install shared dependencies
    pause
    exit /b 1
)
cd ..

REM Install backend dependencies
echo [INFO] Installing backend dependencies...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install backend dependencies
    pause
    exit /b 1
)
cd ..

REM Install frontend dependencies
echo [INFO] Installing frontend dependencies...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install frontend dependencies
    pause
    exit /b 1
)
cd ..

echo [INFO] All dependencies installed successfully.
goto :eof

:setup_env
echo [STEP] Setting up environment files...

REM Backend .env
if not exist "backend\.env" (
    echo [INFO] Creating backend .env file...
    (
        echo # Database
        echo DATABASE_URL="postgresql://ecobuilder:password@localhost:5432/ecobuilder"
        echo.
        echo # Redis
        echo REDIS_URL="redis://localhost:6379"
        echo.
        echo # API Keys ^(Replace with your actual keys^)
        echo OPENAI_API_KEY="your-openai-api-key-here"
        echo CRUNCHBASE_API_KEY="your-crunchbase-api-key-here"
        echo LINKEDIN_ACCESS_TOKEN="your-linkedin-token-here"
        echo SENDGRID_API_KEY="your-sendgrid-api-key-here"
        echo.
        echo # Application
        echo NODE_ENV="development"
        echo PORT=3000
        echo JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
        echo FRONTEND_URL="http://localhost:5173"
        echo.
        echo # Optional External APIs
        echo CLEARBIT_API_KEY="your-clearbit-api-key"
        echo ZOOMINFO_API_KEY="your-zoominfo-api-key"
        echo HUBSPOT_API_KEY="your-hubspot-api-key"
        echo AWS_S3_BUCKET="your-s3-bucket"
        echo AWS_SES_REGION="us-east-1"
    ) > backend\.env
    echo [WARN] Please update backend\.env with your actual API keys!
) else (
    echo [INFO] Backend .env file already exists.
)

REM Frontend .env
if not exist "frontend\.env" (
    echo [INFO] Creating frontend .env file...
    (
        echo REACT_APP_API_URL="http://localhost:3000/api"
        echo REACT_APP_WS_URL="http://localhost:3000"
    ) > frontend\.env
) else (
    echo [INFO] Frontend .env file already exists.
)
goto :eof

:start_dev_servers
echo [STEP] Starting development servers...

REM Create a batch file to start backend
echo [INFO] Creating backend startup script...
(
    echo @echo off
    echo cd backend
    echo echo [INFO] Starting EcoBuilder Backend...
    echo npm run dev
    echo pause
) > start-backend.bat

REM Create a batch file to start frontend
echo [INFO] Creating frontend startup script...
(
    echo @echo off
    echo cd frontend
    echo echo [INFO] Starting EcoBuilder Frontend...
    echo npm run dev
    echo pause
) > start-frontend.bat

REM Start backend in new window
echo [INFO] Starting backend server...
start "EcoBuilder Backend" start-backend.bat

REM Wait a moment
timeout /t 3 /nobreak >nul

REM Start frontend in new window
echo [INFO] Starting frontend server...
start "EcoBuilder Frontend" start-frontend.bat

echo [INFO] Development servers started in separate windows.
goto :eof

:end
echo.
echo [INFO] 🎉 EcoBuilder setup completed!
echo.
echo 📱 Frontend: http://localhost:5173
echo 🔧 Backend API: http://localhost:3000/api
echo.
echo [WARN] Don't forget to update your API keys in backend\.env!
echo [WARN] Make sure PostgreSQL and Redis are running on your system.
echo.
echo Database setup commands (run in backend directory):
echo   npx prisma generate
echo   npx prisma migrate dev --name init
echo   npx prisma db seed
echo.
pause

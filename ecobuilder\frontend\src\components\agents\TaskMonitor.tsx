import React, { useState } from 'react';
import { 
  ClockI<PERSON>, 
  CheckC<PERSON>cleIcon, 
  ExclamationTriangleIcon,
  XCircleIcon,
  ArrowPathIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { Task, TaskStatus, TaskType, Priority } from '../../../../shared/types';

interface TaskMonitorProps {
  tasks: Task[];
  onTaskClick?: (task: Task) => void;
}

const getTaskTypeIcon = (type: TaskType) => {
  switch (type) {
    case TaskType.FIND_PARTNERS:
      return '🔍';
    case TaskType.SCORE_PARTNER:
      return '⭐';
    case TaskType.SEND_EMAIL:
      return '📧';
    case TaskType.SCHEDULE_MEETING:
      return '📅';
    case TaskType.CREATE_PROPOSAL:
      return '📄';
    case TaskType.ANALYZE_OPPORTUNITY:
      return '📊';
    case TaskType.GENERATE_REPORT:
      return '📈';
    case TaskType.TRAIN_PARTNER:
      return '🎓';
    case TaskType.MARKET_ANALYSIS:
      return '🌍';
    default:
      return '📋';
  }
};

const getStatusColor = (status: TaskStatus) => {
  switch (status) {
    case TaskStatus.PENDING:
      return 'text-yellow-600 bg-yellow-100';
    case TaskStatus.RUNNING:
      return 'text-blue-600 bg-blue-100';
    case TaskStatus.COMPLETED:
      return 'text-green-600 bg-green-100';
    case TaskStatus.FAILED:
      return 'text-red-600 bg-red-100';
    case TaskStatus.CANCELLED:
      return 'text-gray-600 bg-gray-100';
    case TaskStatus.RETRYING:
      return 'text-orange-600 bg-orange-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

const getStatusIcon = (status: TaskStatus) => {
  switch (status) {
    case TaskStatus.PENDING:
      return ClockIcon;
    case TaskStatus.RUNNING:
      return ArrowPathIcon;
    case TaskStatus.COMPLETED:
      return CheckCircleIcon;
    case TaskStatus.FAILED:
      return ExclamationTriangleIcon;
    case TaskStatus.CANCELLED:
      return XCircleIcon;
    case TaskStatus.RETRYING:
      return ArrowPathIcon;
    default:
      return ClockIcon;
  }
};

const getPriorityColor = (priority: Priority) => {
  switch (priority) {
    case Priority.URGENT:
      return 'border-l-red-500';
    case Priority.HIGH:
      return 'border-l-orange-500';
    case Priority.MEDIUM:
      return 'border-l-yellow-500';
    case Priority.LOW:
      return 'border-l-green-500';
    default:
      return 'border-l-gray-500';
  }
};

export const TaskMonitor: React.FC<TaskMonitorProps> = ({ tasks, onTaskClick }) => {
  const [filter, setFilter] = useState<TaskStatus | 'ALL'>('ALL');
  const [sortBy, setSortBy] = useState<'createdAt' | 'priority' | 'status'>('createdAt');

  const filteredTasks = tasks.filter(task => 
    filter === 'ALL' || task.status === filter
  );

  const sortedTasks = [...filteredTasks].sort((a, b) => {
    switch (sortBy) {
      case 'priority':
        const priorityOrder = { URGENT: 4, HIGH: 3, MEDIUM: 2, LOW: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      case 'status':
        return a.status.localeCompare(b.status);
      case 'createdAt':
      default:
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    }
  });

  const taskCounts = {
    total: tasks.length,
    pending: tasks.filter(t => t.status === TaskStatus.PENDING).length,
    running: tasks.filter(t => t.status === TaskStatus.RUNNING).length,
    completed: tasks.filter(t => t.status === TaskStatus.COMPLETED).length,
    failed: tasks.filter(t => t.status === TaskStatus.FAILED).length,
  };

  return (
    <div className="space-y-4">
      {/* Summary Stats */}
      <div className="grid grid-cols-2 gap-3 text-sm">
        <div className="bg-blue-50 p-3 rounded-lg">
          <div className="text-blue-600 font-medium">Running</div>
          <div className="text-2xl font-bold text-blue-900">{taskCounts.running}</div>
        </div>
        <div className="bg-green-50 p-3 rounded-lg">
          <div className="text-green-600 font-medium">Completed</div>
          <div className="text-2xl font-bold text-green-900">{taskCounts.completed}</div>
        </div>
        <div className="bg-yellow-50 p-3 rounded-lg">
          <div className="text-yellow-600 font-medium">Pending</div>
          <div className="text-2xl font-bold text-yellow-900">{taskCounts.pending}</div>
        </div>
        <div className="bg-red-50 p-3 rounded-lg">
          <div className="text-red-600 font-medium">Failed</div>
          <div className="text-2xl font-bold text-red-900">{taskCounts.failed}</div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col space-y-2">
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Filter by Status</label>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as TaskStatus | 'ALL')}
            className="w-full text-xs border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="ALL">All Tasks</option>
            <option value={TaskStatus.PENDING}>Pending</option>
            <option value={TaskStatus.RUNNING}>Running</option>
            <option value={TaskStatus.COMPLETED}>Completed</option>
            <option value={TaskStatus.FAILED}>Failed</option>
            <option value={TaskStatus.CANCELLED}>Cancelled</option>
          </select>
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">Sort by</label>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'createdAt' | 'priority' | 'status')}
            className="w-full text-xs border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="createdAt">Created Date</option>
            <option value="priority">Priority</option>
            <option value="status">Status</option>
          </select>
        </div>
      </div>

      {/* Task List */}
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {sortedTasks.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <ClockIcon className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm">No tasks found</p>
          </div>
        ) : (
          sortedTasks.map((task) => {
            const StatusIcon = getStatusIcon(task.status);
            const statusColor = getStatusColor(task.status);
            const priorityColor = getPriorityColor(task.priority);
            const taskIcon = getTaskTypeIcon(task.type);

            return (
              <div
                key={task.id}
                className={`border-l-4 ${priorityColor} bg-white p-3 rounded-r-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer`}
                onClick={() => onTaskClick?.(task)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-2 flex-1">
                    <div className="text-lg">{taskIcon}</div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {task.title}
                        </h4>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${statusColor}`}>
                          <StatusIcon className="w-3 h-3 mr-1" />
                          {task.status}
                        </span>
                      </div>
                      {task.description && (
                        <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                          {task.description}
                        </p>
                      )}
                      <div className="flex items-center space-x-3 mt-2 text-xs text-gray-500">
                        <span className={`font-medium ${
                          task.priority === Priority.URGENT ? 'text-red-600' :
                          task.priority === Priority.HIGH ? 'text-orange-600' :
                          task.priority === Priority.MEDIUM ? 'text-yellow-600' :
                          'text-green-600'
                        }`}>
                          {task.priority}
                        </span>
                        <span>•</span>
                        <span>{new Date(task.createdAt).toLocaleDateString()}</span>
                        {task.retryCount > 0 && (
                          <>
                            <span>•</span>
                            <span className="text-orange-600">
                              Retry {task.retryCount}/{task.maxRetries}
                            </span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  <button className="ml-2 p-1 text-gray-400 hover:text-gray-600">
                    <EyeIcon className="h-4 w-4" />
                  </button>
                </div>

                {/* Progress Bar for Running Tasks */}
                {task.status === TaskStatus.RUNNING && (
                  <div className="mt-3">
                    <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                      <span>Progress</span>
                      <span>{task.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div 
                        className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${task.progress}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Error Message for Failed Tasks */}
                {task.status === TaskStatus.FAILED && task.error && (
                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                    <div className="flex items-center">
                      <ExclamationTriangleIcon className="w-3 h-3 mr-1" />
                      <span className="font-medium">Error:</span>
                    </div>
                    <p className="mt-1 truncate">{task.error}</p>
                  </div>
                )}
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

import { EventEmitter } from 'events';
import { Message, MessageType } from '../../../shared/types';
import { Logger } from '../utils/logger';
import { DatabaseService } from './DatabaseService';
import Redis from 'ioredis';

interface MessageHandler {
  id: string;
  pattern: string;
  handler: (message: Message) => Promise<void>;
  agentId?: string;
}

interface MessageBusConfig {
  redisUrl: string;
  persistMessages: boolean;
  messageRetentionDays: number;
  maxRetries: number;
  retryDelayMs: number;
}

export class MessageBus extends EventEmitter {
  private logger: Logger;
  private dbService: DatabaseService;
  private redis: Redis;
  private config: MessageBusConfig;
  private handlers: Map<string, MessageHandler> = new Map();
  private isInitialized = false;

  constructor(config: Partial<MessageBusConfig> = {}) {
    super();
    this.logger = new Logger('MessageBus');
    this.dbService = new DatabaseService();
    
    this.config = {
      redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',
      persistMessages: true,
      messageRetentionDays: 30,
      maxRetries: 3,
      retryDelayMs: 1000,
      ...config
    };
  }

  /**
   * Initialize the message bus
   */
  async initialize(): Promise<void> {
    this.logger.info('Initializing message bus', { redisUrl: this.config.redisUrl });
    
    try {
      // Initialize Redis connection
      this.redis = new Redis(this.config.redisUrl);
      
      // Setup Redis event handlers
      this.redis.on('connect', () => {
        this.logger.info('Connected to Redis');
      });
      
      this.redis.on('error', (error) => {
        this.logger.error('Redis connection error', error);
      });
      
      // Subscribe to message channels
      await this.setupSubscriptions();
      
      // Setup message cleanup job
      this.setupMessageCleanup();
      
      this.isInitialized = true;
      this.logger.info('Message bus initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize message bus', error);
      throw error;
    }
  }

  /**
   * Publish a message to the bus
   */
  async publish(message: Message): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Message bus not initialized');
    }

    this.logger.info('Publishing message', { 
      messageId: message.id, 
      type: message.type,
      agentId: message.agentId 
    });

    try {
      // Persist message if configured
      if (this.config.persistMessages) {
        await this.dbService.createMessage(message);
      }

      // Publish to Redis
      const channel = this.getChannelName(message);
      await this.redis.publish(channel, JSON.stringify(message));

      // Emit local event
      this.emit('messagePublished', message);

    } catch (error) {
      this.logger.error('Failed to publish message', { messageId: message.id, error });
      throw error;
    }
  }

  /**
   * Subscribe to messages matching a pattern
   */
  async subscribe(
    pattern: string, 
    handler: (message: Message) => Promise<void>,
    agentId?: string
  ): Promise<string> {
    const handlerId = `handler_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.logger.info('Subscribing to messages', { pattern, handlerId, agentId });

    const messageHandler: MessageHandler = {
      id: handlerId,
      pattern,
      handler,
      agentId
    };

    this.handlers.set(handlerId, messageHandler);

    // Subscribe to Redis pattern
    await this.redis.psubscribe(pattern);

    return handlerId;
  }

  /**
   * Unsubscribe from messages
   */
  async unsubscribe(handlerId: string): Promise<void> {
    const handler = this.handlers.get(handlerId);
    if (!handler) {
      this.logger.warn('Handler not found for unsubscribe', { handlerId });
      return;
    }

    this.logger.info('Unsubscribing from messages', { handlerId, pattern: handler.pattern });

    this.handlers.delete(handlerId);

    // Check if any other handlers use the same pattern
    const patternInUse = Array.from(this.handlers.values())
      .some(h => h.pattern === handler.pattern);

    if (!patternInUse) {
      await this.redis.punsubscribe(handler.pattern);
    }
  }

  /**
   * Send a direct message to a specific agent
   */
  async sendToAgent(agentId: string, message: Omit<Message, 'id' | 'createdAt'>): Promise<void> {
    const fullMessage: Message = {
      ...message,
      id: this.generateMessageId(),
      createdAt: new Date()
    };

    const channel = `agent:${agentId}`;
    await this.publish({ ...fullMessage, metadata: { ...fullMessage.metadata, channel } });
  }

  /**
   * Broadcast a message to all agents of a specific type
   */
  async broadcastToAgentType(agentType: string, message: Omit<Message, 'id' | 'createdAt'>): Promise<void> {
    const fullMessage: Message = {
      ...message,
      id: this.generateMessageId(),
      createdAt: new Date()
    };

    const channel = `agent_type:${agentType}`;
    await this.publish({ ...fullMessage, metadata: { ...fullMessage.metadata, channel } });
  }

  /**
   * Send a system notification
   */
  async sendNotification(
    content: string, 
    userId?: string, 
    metadata?: Record<string, any>
  ): Promise<void> {
    const message: Message = {
      id: this.generateMessageId(),
      type: MessageType.NOTIFICATION,
      content,
      metadata: { ...metadata, userId },
      fromId: 'system',
      isRead: false,
      createdAt: new Date()
    };

    const channel = userId ? `user:${userId}` : 'notifications:all';
    await this.publish({ ...message, metadata: { ...message.metadata, channel } });
  }

  /**
   * Get message history for an agent
   */
  async getMessageHistory(
    agentId: string, 
    limit: number = 100, 
    offset: number = 0
  ): Promise<Message[]> {
    if (!this.config.persistMessages) {
      throw new Error('Message persistence is disabled');
    }

    return await this.dbService.getAgentMessages(agentId, limit, offset);
  }

  /**
   * Mark messages as read
   */
  async markAsRead(messageIds: string[]): Promise<void> {
    if (!this.config.persistMessages) {
      return;
    }

    await this.dbService.markMessagesAsRead(messageIds);
    this.emit('messagesRead', { messageIds });
  }

  /**
   * Get unread message count for an agent
   */
  async getUnreadCount(agentId: string): Promise<number> {
    if (!this.config.persistMessages) {
      return 0;
    }

    return await this.dbService.getUnreadMessageCount(agentId);
  }

  /**
   * Setup Redis subscriptions
   */
  private async setupSubscriptions(): Promise<void> {
    // Subscribe to all message patterns
    await this.redis.psubscribe('*');

    // Handle incoming messages
    this.redis.on('pmessage', async (pattern: string, channel: string, message: string) => {
      try {
        const parsedMessage: Message = JSON.parse(message);
        await this.handleIncomingMessage(pattern, channel, parsedMessage);
      } catch (error) {
        this.logger.error('Failed to handle incoming message', { pattern, channel, error });
      }
    });
  }

  /**
   * Handle incoming messages from Redis
   */
  private async handleIncomingMessage(
    pattern: string, 
    channel: string, 
    message: Message
  ): Promise<void> {
    this.logger.debug('Handling incoming message', { 
      pattern, 
      channel, 
      messageId: message.id 
    });

    // Find matching handlers
    const matchingHandlers = Array.from(this.handlers.values())
      .filter(handler => this.matchesPattern(channel, handler.pattern));

    // Execute handlers
    for (const handler of matchingHandlers) {
      try {
        await this.executeHandler(handler, message);
      } catch (error) {
        this.logger.error('Handler execution failed', { 
          handlerId: handler.id, 
          messageId: message.id, 
          error 
        });
        
        // Retry logic could be implemented here
        await this.handleHandlerError(handler, message, error);
      }
    }

    // Emit local event
    this.emit('messageReceived', { pattern, channel, message });
  }

  /**
   * Execute a message handler with retry logic
   */
  private async executeHandler(handler: MessageHandler, message: Message): Promise<void> {
    let retries = 0;
    
    while (retries <= this.config.maxRetries) {
      try {
        await handler.handler(message);
        return; // Success
      } catch (error) {
        retries++;
        
        if (retries > this.config.maxRetries) {
          throw error; // Max retries exceeded
        }
        
        // Wait before retry
        await new Promise(resolve => 
          setTimeout(resolve, this.config.retryDelayMs * retries)
        );
      }
    }
  }

  /**
   * Handle handler execution errors
   */
  private async handleHandlerError(
    handler: MessageHandler, 
    message: Message, 
    error: any
  ): Promise<void> {
    this.logger.error('Handler error after retries', {
      handlerId: handler.id,
      messageId: message.id,
      error: error instanceof Error ? error.message : String(error)
    });

    // Send error notification
    await this.sendNotification(
      `Message handler failed: ${handler.id}`,
      undefined,
      { handlerId: handler.id, messageId: message.id, error }
    );
  }

  /**
   * Check if a channel matches a pattern
   */
  private matchesPattern(channel: string, pattern: string): boolean {
    // Simple pattern matching (could be enhanced with regex)
    if (pattern === '*') return true;
    if (pattern.endsWith('*')) {
      return channel.startsWith(pattern.slice(0, -1));
    }
    return channel === pattern;
  }

  /**
   * Get channel name for a message
   */
  private getChannelName(message: Message): string {
    // Use channel from metadata if provided
    if (message.metadata?.channel) {
      return message.metadata.channel;
    }

    // Default channel based on message type and agent
    if (message.agentId) {
      return `agent:${message.agentId}`;
    }

    return `messages:${message.type.toLowerCase()}`;
  }

  /**
   * Generate a unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Setup message cleanup job
   */
  private setupMessageCleanup(): void {
    if (!this.config.persistMessages) {
      return;
    }

    // Run cleanup daily
    setInterval(async () => {
      try {
        await this.cleanupOldMessages();
      } catch (error) {
        this.logger.error('Message cleanup failed', error);
      }
    }, 24 * 60 * 60 * 1000); // 24 hours
  }

  /**
   * Cleanup old messages
   */
  private async cleanupOldMessages(): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.messageRetentionDays);

    const deletedCount = await this.dbService.deleteOldMessages(cutoffDate);
    
    if (deletedCount > 0) {
      this.logger.info('Cleaned up old messages', { deletedCount, cutoffDate });
    }
  }

  /**
   * Get message bus statistics
   */
  getStats(): any {
    return {
      isInitialized: this.isInitialized,
      activeHandlers: this.handlers.size,
      config: this.config,
      redisConnected: this.redis?.status === 'ready'
    };
  }

  /**
   * Shutdown the message bus
   */
  async shutdown(): Promise<void> {
    this.logger.info('Shutting down message bus');

    // Clear all handlers
    this.handlers.clear();

    // Disconnect from Redis
    if (this.redis) {
      await this.redis.disconnect();
    }

    this.removeAllListeners();
    this.isInitialized = false;

    this.logger.info('Message bus shutdown complete');
  }
}

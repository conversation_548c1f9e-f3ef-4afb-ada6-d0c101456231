import React, { useState, useEffect } from 'react';
import { 
  ChartBarIcon, 
  UserGroupIcon, 
  CogIcon, 
  BoltIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { AgentCard } from '../components/agents/AgentCard';
import { TaskMonitor } from '../components/agents/TaskMonitor';
import { WorkflowVisualizer } from '../components/agents/WorkflowVisualizer';
import { useAgents } from '../hooks/useAgents';
import { useTasks } from '../hooks/useTasks';
import { useWorkflows } from '../hooks/useWorkflows';
import { useWebSocket } from '../hooks/useWebSocket';

interface DashboardStats {
  totalPartners: number;
  activeAgents: number;
  runningTasks: number;
  completedWorkflows: number;
}

export const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalPartners: 0,
    activeAgents: 0,
    runningTasks: 0,
    completedWorkflows: 0
  });

  const { agents, loading: agentsLoading } = useAgents();
  const { tasks, loading: tasksLoading } = useTasks();
  const { workflows, loading: workflowsLoading } = useWorkflows();
  const { socket, connected } = useWebSocket();

  useEffect(() => {
    // Update stats when data changes
    setStats({
      totalPartners: 0, // TODO: Get from API
      activeAgents: agents.filter(agent => agent.status === 'WORKING').length,
      runningTasks: tasks.filter(task => task.status === 'RUNNING').length,
      completedWorkflows: workflows.filter(workflow => workflow.status === 'COMPLETED').length
    });
  }, [agents, tasks, workflows]);

  useEffect(() => {
    if (socket) {
      // Listen for real-time updates
      socket.on('agentStatusUpdate', (data) => {
        console.log('Agent status update:', data);
      });

      socket.on('taskUpdate', (data) => {
        console.log('Task update:', data);
      });

      socket.on('workflowUpdate', (data) => {
        console.log('Workflow update:', data);
      });

      return () => {
        socket.off('agentStatusUpdate');
        socket.off('taskUpdate');
        socket.off('workflowUpdate');
      };
    }
  }, [socket]);

  const statCards = [
    {
      title: 'Total Partners',
      value: stats.totalPartners,
      icon: UserGroupIcon,
      color: 'bg-blue-500',
      change: '+12%',
      changeType: 'positive' as const
    },
    {
      title: 'Active Agents',
      value: stats.activeAgents,
      icon: BoltIcon,
      color: 'bg-green-500',
      change: `${stats.activeAgents}/${agents.length}`,
      changeType: 'neutral' as const
    },
    {
      title: 'Running Tasks',
      value: stats.runningTasks,
      icon: ClockIcon,
      color: 'bg-yellow-500',
      change: '+5',
      changeType: 'positive' as const
    },
    {
      title: 'Completed Workflows',
      value: stats.completedWorkflows,
      icon: CheckCircleIcon,
      color: 'bg-purple-500',
      change: '+8%',
      changeType: 'positive' as const
    }
  ];

  if (agentsLoading || tasksLoading || workflowsLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                EcoBuilder Dashboard
              </h1>
              <p className="mt-1 text-sm text-gray-500">
                Multi-Agent Partner Development System
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
                connected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  connected ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span>{connected ? 'Connected' : 'Disconnected'}</span>
              </div>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                New Workflow
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statCards.map((stat, index) => (
            <div key={index} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className={`${stat.color} rounded-md p-3`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">{stat.title}</p>
                  <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                </div>
              </div>
              <div className="mt-4">
                <span className={`text-sm ${
                  stat.changeType === 'positive' ? 'text-green-600' : 
                  stat.changeType === 'negative' ? 'text-red-600' : 'text-gray-600'
                }`}>
                  {stat.change}
                </span>
                <span className="text-sm text-gray-500 ml-1">from last week</span>
              </div>
            </div>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Agents Section */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900 flex items-center">
                  <CogIcon className="h-5 w-5 mr-2" />
                  Active Agents
                </h2>
              </div>
              <div className="p-6">
                {agents.length === 0 ? (
                  <div className="text-center py-8">
                    <ExclamationTriangleIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No agents configured</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {agents.map((agent) => (
                      <AgentCard key={agent.id} agent={agent} />
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Task Monitor */}
          <div>
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900 flex items-center">
                  <ChartBarIcon className="h-5 w-5 mr-2" />
                  Task Monitor
                </h2>
              </div>
              <div className="p-6">
                <TaskMonitor tasks={tasks} />
              </div>
            </div>
          </div>
        </div>

        {/* Workflow Visualizer */}
        <div className="mt-8">
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                Active Workflows
              </h2>
            </div>
            <div className="p-6">
              <WorkflowVisualizer workflows={workflows} />
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="mt-8">
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                Recent Activity
              </h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {/* Activity items would go here */}
                <div className="text-center py-8 text-gray-500">
                  No recent activity
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

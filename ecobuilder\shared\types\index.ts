// User Types
export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
  USER = 'USER'
}

// Partner Types
export interface Partner {
  id: string;
  companyName: string;
  industry: string;
  size: CompanySize;
  revenue?: number;
  website?: string;
  description?: string;
  status: PartnerStatus;
  score?: number;
  priority: Priority;
  createdById: string;
  createdAt: Date;
  updatedAt: Date;
  contacts?: Contact[];
  agreements?: Agreement[];
  opportunities?: Opportunity[];
  activities?: Activity[];
}

export enum CompanySize {
  STARTUP = 'STARTUP',
  SMALL = 'SMALL',
  MEDIUM = 'MEDIUM',
  LARGE = 'LARGE',
  ENTERPRISE = 'ENTERPRISE'
}

export enum PartnerStatus {
  PROSPECT = 'PROSPECT',
  CONTACTED = 'CONTACTED',
  QUALIFIED = 'QUALIFIED',
  NEGOTIATING = 'NEGOTIATING',
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  REJECTED = 'REJECTED'
}

export enum Priority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// Contact Types
export interface Contact {
  id: string;
  name: string;
  email: string;
  phone?: string;
  position: string;
  isPrimary: boolean;
  partnerId: string;
  createdAt: Date;
  updatedAt: Date;
}

// Agreement Types
export interface Agreement {
  id: string;
  title: string;
  type: AgreementType;
  status: AgreementStatus;
  content?: string;
  terms?: any;
  value?: number;
  startDate?: Date;
  endDate?: Date;
  signedAt?: Date;
  partnerId: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum AgreementType {
  NDA = 'NDA',
  PARTNERSHIP = 'PARTNERSHIP',
  RESELLER = 'RESELLER',
  DISTRIBUTOR = 'DISTRIBUTOR',
  JOINT_VENTURE = 'JOINT_VENTURE'
}

export enum AgreementStatus {
  DRAFT = 'DRAFT',
  REVIEW = 'REVIEW',
  NEGOTIATION = 'NEGOTIATION',
  SIGNED = 'SIGNED',
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  TERMINATED = 'TERMINATED'
}

// Opportunity Types
export interface Opportunity {
  id: string;
  title: string;
  description?: string;
  value: number;
  currency: string;
  stage: OpportunityStage;
  probability: number;
  expectedCloseDate?: Date;
  actualCloseDate?: Date;
  partnerId: string;
  createdAt: Date;
  updatedAt: Date;
  activities?: Activity[];
  tasks?: Task[];
}

export enum OpportunityStage {
  QUALIFICATION = 'QUALIFICATION',
  NEEDS_ANALYSIS = 'NEEDS_ANALYSIS',
  PROPOSAL = 'PROPOSAL',
  NEGOTIATION = 'NEGOTIATION',
  CLOSED_WON = 'CLOSED_WON',
  CLOSED_LOST = 'CLOSED_LOST'
}

// Activity Types
export interface Activity {
  id: string;
  type: ActivityType;
  subject: string;
  description?: string;
  status: ActivityStatus;
  priority: Priority;
  scheduledAt?: Date;
  completedAt?: Date;
  duration?: number;
  outcome?: string;
  partnerId?: string;
  contactId?: string;
  opportunityId?: string;
  agentId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum ActivityType {
  CALL = 'CALL',
  EMAIL = 'EMAIL',
  MEETING = 'MEETING',
  TRAINING = 'TRAINING',
  PROPOSAL = 'PROPOSAL',
  DEMO = 'DEMO',
  FOLLOW_UP = 'FOLLOW_UP',
  RESEARCH = 'RESEARCH'
}

export enum ActivityStatus {
  PLANNED = 'PLANNED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  RESCHEDULED = 'RESCHEDULED'
}

// Agent Types
export interface Agent {
  id: string;
  name: string;
  type: AgentType;
  status: AgentStatus;
  capabilities: any;
  config?: any;
  isActive: boolean;
  lastActiveAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export enum AgentType {
  PARTNER_SCOUT = 'PARTNER_SCOUT',
  PARTNER_RECRUITER = 'PARTNER_RECRUITER',
  PARTNER_TRAINER = 'PARTNER_TRAINER',
  OPPORTUNITY_MANAGER = 'OPPORTUNITY_MANAGER',
  MARKET_EXPANDER = 'MARKET_EXPANDER'
}

export enum AgentStatus {
  IDLE = 'IDLE',
  WORKING = 'WORKING',
  ERROR = 'ERROR',
  OFFLINE = 'OFFLINE',
  MAINTENANCE = 'MAINTENANCE'
}

// Task Types
export interface Task {
  id: string;
  title: string;
  description?: string;
  type: TaskType;
  status: TaskStatus;
  priority: Priority;
  data?: any;
  result?: any;
  error?: string;
  progress: number;
  maxRetries: number;
  retryCount: number;
  scheduledAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
  agentId: string;
  assignedToId?: string;
  partnerId?: string;
  opportunityId?: string;
  parentTaskId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum TaskType {
  FIND_PARTNERS = 'FIND_PARTNERS',
  SCORE_PARTNER = 'SCORE_PARTNER',
  SEND_EMAIL = 'SEND_EMAIL',
  SCHEDULE_MEETING = 'SCHEDULE_MEETING',
  CREATE_PROPOSAL = 'CREATE_PROPOSAL',
  ANALYZE_OPPORTUNITY = 'ANALYZE_OPPORTUNITY',
  GENERATE_REPORT = 'GENERATE_REPORT',
  TRAIN_PARTNER = 'TRAIN_PARTNER',
  MARKET_ANALYSIS = 'MARKET_ANALYSIS'
}

export enum TaskStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  RETRYING = 'RETRYING'
}

// Message Types
export interface Message {
  id: string;
  type: MessageType;
  content: string;
  metadata?: any;
  fromId?: string;
  agentId?: string;
  isRead: boolean;
  createdAt: Date;
}

export enum MessageType {
  SYSTEM = 'SYSTEM',
  USER = 'USER',
  AGENT = 'AGENT',
  NOTIFICATION = 'NOTIFICATION',
  ERROR = 'ERROR'
}

// Workflow Types
export interface Workflow {
  id: string;
  name: string;
  description?: string;
  definition: any;
  isActive: boolean;
  version: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkflowExecution {
  id: string;
  status: WorkflowExecutionStatus;
  context?: any;
  result?: any;
  error?: string;
  startedAt: Date;
  completedAt?: Date;
  workflowId: string;
}

export enum WorkflowExecutionStatus {
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Tool Types
export interface Tool {
  id: string;
  name: string;
  description: string;
  type: ToolType;
  config: any;
}

export enum ToolType {
  CRM = 'CRM',
  EMAIL = 'EMAIL',
  CALENDAR = 'CALENDAR',
  DOCUMENT = 'DOCUMENT',
  ANALYSIS = 'ANALYSIS',
  COMMUNICATION = 'COMMUNICATION'
}

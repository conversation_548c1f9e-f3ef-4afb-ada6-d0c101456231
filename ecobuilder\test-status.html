<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态测试页面</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e5e5e5;
            border-radius: 8px;
        }

        .test-button {
            padding: 12px 24px;
            margin: 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }

        /* 文件上传区域 */
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            margin: 20px 0;
        }

        .upload-area.uploading {
            border-color: #007bff;
            background: rgba(0,123,255,0.1);
        }

        .upload-area.success {
            border-color: #28a745;
            background: rgba(40,167,69,0.1);
        }

        .upload-area.error {
            border-color: #dc3545;
            background: rgba(220,53,69,0.1);
        }

        .upload-status {
            display: none;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .upload-status.show {
            display: flex;
        }

        .status-icon {
            font-size: 48px;
        }

        .status-icon.uploading {
            color: #007bff;
            animation: spin 1s linear infinite;
        }

        .status-icon.success {
            color: #28a745;
        }

        .status-icon.error {
            color: #dc3545;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* AI分析按钮 */
        .ai-button {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
        }

        .ai-button.default {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .ai-button.processing {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            cursor: not-allowed;
        }

        .ai-button.evaluation {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .ai-button.selection {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s linear infinite;
        }

        .ai-button.processing .loading-spinner {
            display: inline-block;
        }

        .ai-button.processing .button-icon {
            display: none;
        }

        .status-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 状态更新测试页面</h1>
        
        <!-- 文件上传测试 -->
        <div class="test-section">
            <h2>📁 文件上传状态测试</h2>
            <div class="upload-area" id="uploadArea" onclick="triggerFileInput()">
                <input type="file" id="fileInput" style="display: none;" accept=".pdf,.doc,.docx,.csv,.xls,.xlsx" onchange="handleFileSelect(event)">

                <div id="uploadDefault">
                    <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #ccc;"></i>
                    <p>点击上传或拖拽文件到此处</p>
                    <p style="font-size: 12px; color: #666;">支持格式：PDF、DOC、DOCX、CSV、XLS、XLSX</p>
                </div>

                <div class="upload-status" id="uploadStatus">
                    <div class="status-icon" id="statusIcon">
                        <i class="fas fa-spinner"></i>
                    </div>
                    <div id="statusText">正在上传...</div>
                    <div id="statusSubtext">请稍候</div>
                </div>
            </div>

            <div style="margin-top: 15px;">
                <button class="test-button btn-primary" onclick="testUploadStatus()">测试上传状态</button>
                <button class="test-button btn-success" onclick="testUploadSuccess()">测试成功状态</button>
                <button class="test-button btn-danger" onclick="testUploadError()">测试错误状态</button>
                <button class="test-button btn-warning" onclick="resetUpload()">重置状态</button>
            </div>

            <div id="uploadedFileInfo" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 6px; display: none;">
                <strong>已上传文件：</strong>
                <div id="fileName"></div>
                <div id="fileSize" style="font-size: 12px; color: #666;"></div>
            </div>
        </div>

        <!-- AI按钮测试 -->
        <div class="test-section">
            <h2>🤖 AI分析按钮测试</h2>
            <button class="ai-button default" id="aiButton" onclick="handleAIButtonClick()">
                <div class="loading-spinner"></div>
                <i class="fas fa-magic button-icon"></i>
                <span class="button-text">开始AI生态分析</span>
            </button>

            <div style="margin-top: 15px;">
                <button class="test-button btn-primary" onclick="testProcessing()">测试处理状态</button>
                <button class="test-button btn-primary" onclick="testEvaluation()">测试评估阶段</button>
                <button class="test-button btn-success" onclick="testSelection()">测试选择阶段</button>
                <button class="test-button btn-warning" onclick="resetButton()">重置按钮</button>
            </div>
        </div>

        <!-- 状态日志 -->
        <div class="test-section">
            <h2>📋 状态日志</h2>
            <div class="status-log" id="statusLog"></div>
            <button class="test-button btn-warning" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        function log(message) {
            const logElement = document.getElementById('statusLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        // 文件上传功能
        function triggerFileInput() {
            document.getElementById('fileInput').click();
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                log(`选择文件: ${file.name} (${formatFileSize(file.size)})`);
                simulateFileUpload(file);
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function simulateFileUpload(file) {
            log('开始模拟文件上传');
            testUploadStatus();

            // 模拟上传进度
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 20 + 10;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);

                    setTimeout(() => {
                        testUploadSuccess();
                        showFileInfo(file);
                        log(`文件上传完成: ${file.name}`);
                    }, 500);
                } else {
                    log(`上传进度: ${Math.round(progress)}%`);
                }
            }, 300);
        }

        function showFileInfo(file) {
            const fileInfo = document.getElementById('uploadedFileInfo');
            const fileName = document.getElementById('fileName');
            const fileSize = document.getElementById('fileSize');

            fileName.textContent = file.name;
            fileSize.textContent = `大小: ${formatFileSize(file.size)} | 类型: ${file.type || '未知'}`;
            fileInfo.style.display = 'block';
        }

        // 文件上传状态测试
        function testUploadStatus() {
            log('开始测试上传状态');
            const uploadArea = document.getElementById('uploadArea');
            const uploadDefault = document.getElementById('uploadDefault');
            const uploadStatus = document.getElementById('uploadStatus');
            const statusIcon = document.getElementById('statusIcon');

            uploadArea.className = 'upload-area uploading';
            uploadDefault.style.display = 'none';
            uploadStatus.classList.add('show');
            statusIcon.className = 'status-icon uploading';
            statusIcon.innerHTML = '<i class="fas fa-spinner"></i>';
            document.getElementById('statusText').textContent = '正在上传文件...';
            document.getElementById('statusSubtext').textContent = '请稍候，正在处理您的文件';
        }

        function testUploadSuccess() {
            log('测试上传成功状态');
            const uploadArea = document.getElementById('uploadArea');
            const statusIcon = document.getElementById('statusIcon');
            
            uploadArea.className = 'upload-area success';
            statusIcon.className = 'status-icon success';
            statusIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
            document.getElementById('statusText').textContent = '文件上传成功';
            document.getElementById('statusSubtext').textContent = '文件已准备好进行分析';
        }

        function testUploadError() {
            log('测试上传错误状态');
            const uploadArea = document.getElementById('uploadArea');
            const statusIcon = document.getElementById('statusIcon');
            
            uploadArea.className = 'upload-area error';
            statusIcon.className = 'status-icon error';
            statusIcon.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
            document.getElementById('statusText').textContent = '上传失败';
            document.getElementById('statusSubtext').textContent = '请检查文件格式和大小';
        }

        function resetUpload() {
            log('重置上传状态');
            const uploadArea = document.getElementById('uploadArea');
            const uploadDefault = document.getElementById('uploadDefault');
            const uploadStatus = document.getElementById('uploadStatus');
            const fileInfo = document.getElementById('uploadedFileInfo');
            const fileInput = document.getElementById('fileInput');

            uploadArea.className = 'upload-area';
            uploadDefault.style.display = 'block';
            uploadStatus.classList.remove('show');
            fileInfo.style.display = 'none';
            fileInput.value = '';
        }

        // AI按钮点击处理
        let currentStage = 'default';
        let isProcessing = false;

        function handleAIButtonClick() {
            if (isProcessing) {
                log('按钮正在处理中，忽略点击');
                return;
            }

            log('AI按钮被点击，当前阶段: ' + currentStage);

            switch(currentStage) {
                case 'default':
                    startAnalysis();
                    break;
                case 'evaluation':
                    startEvaluation();
                    break;
                case 'selection':
                    startSelection();
                    break;
                case 'analysis':
                    generateReport();
                    break;
                default:
                    log('未知阶段: ' + currentStage);
            }
        }

        function startAnalysis() {
            log('开始AI生态分析');
            isProcessing = true;
            testProcessing();

            // 模拟处理时间
            setTimeout(() => {
                isProcessing = false;
                currentStage = 'evaluation';
                testEvaluation();
                log('分析完成，进入评估阶段');
            }, 3000);
        }

        function startEvaluation() {
            log('开始评估合作伙伴需求');
            isProcessing = true;
            testProcessing();

            setTimeout(() => {
                isProcessing = false;
                currentStage = 'selection';
                testSelection();
                log('评估完成，进入选择阶段');
            }, 2500);
        }

        function startSelection() {
            log('开始选择合作伙伴');
            isProcessing = true;
            testProcessing();

            setTimeout(() => {
                isProcessing = false;
                currentStage = 'analysis';
                testAnalysis();
                log('选择完成，进入分析阶段');
            }, 2000);
        }

        function generateReport() {
            log('生成分析报告');
            isProcessing = true;
            testProcessing();

            setTimeout(() => {
                isProcessing = false;
                currentStage = 'default';
                resetButton();
                log('报告生成完成，流程结束');
                alert('🎉 AI生态分析流程已完成！');
            }, 3000);
        }

        // AI按钮状态测试
        function testProcessing() {
            log('测试AI按钮处理状态');
            const aiButton = document.getElementById('aiButton');
            const buttonText = aiButton.querySelector('.button-text');

            aiButton.className = 'ai-button processing';
            aiButton.disabled = true;
            buttonText.textContent = '处理中...';
        }

        function testEvaluation() {
            log('测试评估阶段');
            const aiButton = document.getElementById('aiButton');
            const buttonText = aiButton.querySelector('.button-text');
            const buttonIcon = aiButton.querySelector('.button-icon');
            
            aiButton.className = 'ai-button evaluation';
            aiButton.disabled = false;
            buttonIcon.className = 'fas fa-search button-icon';
            buttonText.textContent = '评估合作伙伴需求';
        }

        function testSelection() {
            log('测试选择阶段');
            const aiButton = document.getElementById('aiButton');
            const buttonText = aiButton.querySelector('.button-text');
            const buttonIcon = aiButton.querySelector('.button-icon');

            aiButton.className = 'ai-button selection';
            aiButton.disabled = false;
            buttonIcon.className = 'fas fa-check-circle button-icon';
            buttonText.textContent = '选择合作伙伴';
        }

        function testAnalysis() {
            log('测试分析阶段');
            const aiButton = document.getElementById('aiButton');
            const buttonText = aiButton.querySelector('.button-text');
            const buttonIcon = aiButton.querySelector('.button-icon');

            aiButton.className = 'ai-button evaluation'; // 使用蓝色
            aiButton.disabled = false;
            buttonIcon.className = 'fas fa-chart-line button-icon';
            buttonText.textContent = '生成分析报告';
        }

        function resetButton() {
            log('重置AI按钮');
            const aiButton = document.getElementById('aiButton');
            const buttonText = aiButton.querySelector('.button-text');
            const buttonIcon = aiButton.querySelector('.button-icon');

            aiButton.className = 'ai-button default';
            aiButton.disabled = false;
            buttonIcon.className = 'fas fa-magic button-icon';
            buttonText.textContent = '开始AI生态分析';

            // 重置状态变量
            currentStage = 'default';
            isProcessing = false;
        }

        function clearLog() {
            document.getElementById('statusLog').innerHTML = '';
        }

        // 拖拽上传功能
        function setupDragAndDrop() {
            const uploadArea = document.getElementById('uploadArea');

            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#007bff';
                uploadArea.style.backgroundColor = 'rgba(0,123,255,0.1)';
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#ccc';
                uploadArea.style.backgroundColor = 'transparent';
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#ccc';
                uploadArea.style.backgroundColor = 'transparent';

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    log(`拖拽文件: ${file.name}`);
                    simulateFileUpload(file);
                }
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面已加载完成');
            log('点击各个测试按钮来验证状态更新功能');
            log('可以点击上传区域选择文件，或直接拖拽文件到上传区域');
            setupDragAndDrop();
        });
    </script>
</body>
</html>

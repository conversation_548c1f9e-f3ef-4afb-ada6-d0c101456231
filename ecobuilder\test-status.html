<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态测试页面</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e5e5e5;
            border-radius: 8px;
        }

        .test-button {
            padding: 12px 24px;
            margin: 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }

        /* 文件上传区域 */
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            margin: 20px 0;
        }

        .upload-area.uploading {
            border-color: #007bff;
            background: rgba(0,123,255,0.1);
        }

        .upload-area.success {
            border-color: #28a745;
            background: rgba(40,167,69,0.1);
        }

        .upload-area.error {
            border-color: #dc3545;
            background: rgba(220,53,69,0.1);
        }

        .upload-status {
            display: none;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .upload-status.show {
            display: flex;
        }

        .status-icon {
            font-size: 48px;
        }

        .status-icon.uploading {
            color: #007bff;
            animation: spin 1s linear infinite;
        }

        .status-icon.success {
            color: #28a745;
        }

        .status-icon.error {
            color: #dc3545;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* AI分析按钮 */
        .ai-button {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
        }

        .ai-button.default {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .ai-button.processing {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            cursor: not-allowed;
        }

        .ai-button.evaluation {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .ai-button.selection {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s linear infinite;
        }

        .ai-button.processing .loading-spinner {
            display: inline-block;
        }

        .ai-button.processing .button-icon {
            display: none;
        }

        .status-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 状态更新测试页面</h1>
        
        <!-- 文件上传测试 -->
        <div class="test-section">
            <h2>📁 文件上传状态测试</h2>
            <div class="upload-area" id="uploadArea">
                <div id="uploadDefault">
                    <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #ccc;"></i>
                    <p>点击上传或拖拽文件到此处</p>
                </div>
                
                <div class="upload-status" id="uploadStatus">
                    <div class="status-icon" id="statusIcon">
                        <i class="fas fa-spinner"></i>
                    </div>
                    <div id="statusText">正在上传...</div>
                    <div id="statusSubtext">请稍候</div>
                </div>
            </div>
            
            <button class="test-button btn-primary" onclick="testUploadStatus()">测试上传状态</button>
            <button class="test-button btn-success" onclick="testUploadSuccess()">测试成功状态</button>
            <button class="test-button btn-danger" onclick="testUploadError()">测试错误状态</button>
            <button class="test-button btn-warning" onclick="resetUpload()">重置状态</button>
        </div>

        <!-- AI按钮测试 -->
        <div class="test-section">
            <h2>🤖 AI分析按钮测试</h2>
            <button class="ai-button default" id="aiButton">
                <div class="loading-spinner"></div>
                <i class="fas fa-magic button-icon"></i>
                <span class="button-text">开始AI生态分析</span>
            </button>
            
            <button class="test-button btn-primary" onclick="testProcessing()">测试处理状态</button>
            <button class="test-button btn-primary" onclick="testEvaluation()">测试评估阶段</button>
            <button class="test-button btn-success" onclick="testSelection()">测试选择阶段</button>
            <button class="test-button btn-warning" onclick="resetButton()">重置按钮</button>
        </div>

        <!-- 状态日志 -->
        <div class="test-section">
            <h2>📋 状态日志</h2>
            <div class="status-log" id="statusLog"></div>
            <button class="test-button btn-warning" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        function log(message) {
            const logElement = document.getElementById('statusLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        // 文件上传状态测试
        function testUploadStatus() {
            log('开始测试上传状态');
            const uploadArea = document.getElementById('uploadArea');
            const uploadDefault = document.getElementById('uploadDefault');
            const uploadStatus = document.getElementById('uploadStatus');
            const statusIcon = document.getElementById('statusIcon');
            
            uploadArea.className = 'upload-area uploading';
            uploadDefault.style.display = 'none';
            uploadStatus.classList.add('show');
            statusIcon.className = 'status-icon uploading';
            statusIcon.innerHTML = '<i class="fas fa-spinner"></i>';
            document.getElementById('statusText').textContent = '正在上传文件...';
            document.getElementById('statusSubtext').textContent = '请稍候，正在处理您的文件';
        }

        function testUploadSuccess() {
            log('测试上传成功状态');
            const uploadArea = document.getElementById('uploadArea');
            const statusIcon = document.getElementById('statusIcon');
            
            uploadArea.className = 'upload-area success';
            statusIcon.className = 'status-icon success';
            statusIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
            document.getElementById('statusText').textContent = '文件上传成功';
            document.getElementById('statusSubtext').textContent = '文件已准备好进行分析';
        }

        function testUploadError() {
            log('测试上传错误状态');
            const uploadArea = document.getElementById('uploadArea');
            const statusIcon = document.getElementById('statusIcon');
            
            uploadArea.className = 'upload-area error';
            statusIcon.className = 'status-icon error';
            statusIcon.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
            document.getElementById('statusText').textContent = '上传失败';
            document.getElementById('statusSubtext').textContent = '请检查文件格式和大小';
        }

        function resetUpload() {
            log('重置上传状态');
            const uploadArea = document.getElementById('uploadArea');
            const uploadDefault = document.getElementById('uploadDefault');
            const uploadStatus = document.getElementById('uploadStatus');
            
            uploadArea.className = 'upload-area';
            uploadDefault.style.display = 'block';
            uploadStatus.classList.remove('show');
        }

        // AI按钮状态测试
        function testProcessing() {
            log('测试AI按钮处理状态');
            const aiButton = document.getElementById('aiButton');
            const buttonText = aiButton.querySelector('.button-text');
            
            aiButton.className = 'ai-button processing';
            aiButton.disabled = true;
            buttonText.textContent = '处理中...';
        }

        function testEvaluation() {
            log('测试评估阶段');
            const aiButton = document.getElementById('aiButton');
            const buttonText = aiButton.querySelector('.button-text');
            const buttonIcon = aiButton.querySelector('.button-icon');
            
            aiButton.className = 'ai-button evaluation';
            aiButton.disabled = false;
            buttonIcon.className = 'fas fa-search button-icon';
            buttonText.textContent = '评估合作伙伴需求';
        }

        function testSelection() {
            log('测试选择阶段');
            const aiButton = document.getElementById('aiButton');
            const buttonText = aiButton.querySelector('.button-text');
            const buttonIcon = aiButton.querySelector('.button-icon');
            
            aiButton.className = 'ai-button selection';
            aiButton.disabled = false;
            buttonIcon.className = 'fas fa-check-circle button-icon';
            buttonText.textContent = '选择合作伙伴';
        }

        function resetButton() {
            log('重置AI按钮');
            const aiButton = document.getElementById('aiButton');
            const buttonText = aiButton.querySelector('.button-text');
            const buttonIcon = aiButton.querySelector('.button-icon');
            
            aiButton.className = 'ai-button default';
            aiButton.disabled = false;
            buttonIcon.className = 'fas fa-magic button-icon';
            buttonText.textContent = '开始AI生态分析';
        }

        function clearLog() {
            document.getElementById('statusLog').innerHTML = '';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面已加载完成');
            log('点击各个测试按钮来验证状态更新功能');
        });
    </script>
</body>
</html>

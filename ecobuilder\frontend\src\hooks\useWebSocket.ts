import { useState, useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';

interface UseWebSocketReturn {
  socket: Socket | null;
  connected: boolean;
  error: string | null;
  connect: () => void;
  disconnect: () => void;
  emit: (event: string, data?: any) => void;
  on: (event: string, callback: (data: any) => void) => void;
  off: (event: string, callback?: (data: any) => void) => void;
}

interface WebSocketConfig {
  url?: string;
  autoConnect?: boolean;
  reconnection?: boolean;
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
}

export const useWebSocket = (config: WebSocketConfig = {}): UseWebSocketReturn => {
  const {
    url = process.env.REACT_APP_WS_URL || 'http://localhost:3000',
    autoConnect = true,
    reconnection = true,
    reconnectionAttempts = 5,
    reconnectionDelay = 1000
  } = config;

  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const socketRef = useRef<Socket | null>(null);

  const connect = () => {
    if (socketRef.current?.connected) {
      return;
    }

    try {
      const newSocket = io(url, {
        reconnection,
        reconnectionAttempts,
        reconnectionDelay,
        timeout: 10000,
        transports: ['websocket', 'polling']
      });

      // Connection event handlers
      newSocket.on('connect', () => {
        console.log('WebSocket connected');
        setConnected(true);
        setError(null);
      });

      newSocket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason);
        setConnected(false);
        
        if (reason === 'io server disconnect') {
          // Server initiated disconnect, try to reconnect
          newSocket.connect();
        }
      });

      newSocket.on('connect_error', (err) => {
        console.error('WebSocket connection error:', err);
        setError(err.message);
        setConnected(false);
      });

      newSocket.on('reconnect', (attemptNumber) => {
        console.log('WebSocket reconnected after', attemptNumber, 'attempts');
        setConnected(true);
        setError(null);
      });

      newSocket.on('reconnect_error', (err) => {
        console.error('WebSocket reconnection error:', err);
        setError(err.message);
      });

      newSocket.on('reconnect_failed', () => {
        console.error('WebSocket reconnection failed');
        setError('Failed to reconnect to server');
        setConnected(false);
      });

      // Application-specific event handlers
      newSocket.on('agentStatusUpdate', (data) => {
        console.log('Agent status update:', data);
      });

      newSocket.on('taskUpdate', (data) => {
        console.log('Task update:', data);
      });

      newSocket.on('workflowUpdate', (data) => {
        console.log('Workflow update:', data);
      });

      newSocket.on('notification', (data) => {
        console.log('Notification:', data);
      });

      newSocket.on('systemAlert', (data) => {
        console.log('System alert:', data);
      });

      socketRef.current = newSocket;
      setSocket(newSocket);
    } catch (err) {
      console.error('Failed to create WebSocket connection:', err);
      setError(err instanceof Error ? err.message : 'Failed to connect');
    }
  };

  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
      setSocket(null);
      setConnected(false);
    }
  };

  const emit = (event: string, data?: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data);
    } else {
      console.warn('Cannot emit event: WebSocket not connected');
    }
  };

  const on = (event: string, callback: (data: any) => void) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback);
    }
  };

  const off = (event: string, callback?: (data: any) => void) => {
    if (socketRef.current) {
      if (callback) {
        socketRef.current.off(event, callback);
      } else {
        socketRef.current.off(event);
      }
    }
  };

  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [url, autoConnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, []);

  return {
    socket,
    connected,
    error,
    connect,
    disconnect,
    emit,
    on,
    off
  };
};

// Mock WebSocket for development
export const useWebSocketMock = (): UseWebSocketReturn => {
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const eventHandlers = useRef<Map<string, ((data: any) => void)[]>>(new Map());

  const connect = () => {
    setTimeout(() => {
      setConnected(true);
      setError(null);
      console.log('Mock WebSocket connected');
      
      // Simulate periodic updates
      const interval = setInterval(() => {
        if (eventHandlers.current.has('agentStatusUpdate')) {
          const handlers = eventHandlers.current.get('agentStatusUpdate') || [];
          handlers.forEach(handler => {
            handler({
              agentId: 'agent-1',
              status: Math.random() > 0.5 ? 'WORKING' : 'IDLE',
              timestamp: new Date().toISOString()
            });
          });
        }

        if (eventHandlers.current.has('taskUpdate')) {
          const handlers = eventHandlers.current.get('taskUpdate') || [];
          handlers.forEach(handler => {
            handler({
              taskId: 'task-1',
              progress: Math.floor(Math.random() * 100),
              status: 'RUNNING',
              timestamp: new Date().toISOString()
            });
          });
        }
      }, 5000);

      return () => clearInterval(interval);
    }, 1000);
  };

  const disconnect = () => {
    setConnected(false);
    eventHandlers.current.clear();
    console.log('Mock WebSocket disconnected');
  };

  const emit = (event: string, data?: any) => {
    console.log('Mock WebSocket emit:', event, data);
  };

  const on = (event: string, callback: (data: any) => void) => {
    if (!eventHandlers.current.has(event)) {
      eventHandlers.current.set(event, []);
    }
    eventHandlers.current.get(event)?.push(callback);
  };

  const off = (event: string, callback?: (data: any) => void) => {
    if (callback) {
      const handlers = eventHandlers.current.get(event) || [];
      const index = handlers.indexOf(callback);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    } else {
      eventHandlers.current.delete(event);
    }
  };

  useEffect(() => {
    connect();
    return () => disconnect();
  }, []);

  return {
    socket: null,
    connected,
    error,
    connect,
    disconnect,
    emit,
    on,
    off
  };
};

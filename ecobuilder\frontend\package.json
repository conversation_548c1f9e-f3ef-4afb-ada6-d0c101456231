{"name": "ecobuilder-frontend", "version": "1.0.0", "description": "EcoBuilder Multi-Agent System Frontend", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write src/**/*.{ts,tsx}", "type-check": "tsc --noEmit"}, "keywords": ["react", "typescript", "ai", "agents", "ecosystem"], "author": "EcoBuilder Team", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@tailwindcss/forms": "^0.5.7", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "recharts": "^2.8.0", "zustand": "^4.4.7", "lucide-react": "^0.294.0", "react-dropzone": "^14.2.3", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-syntax-highlighter": "^15.5.10", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "prettier": "^3.1.0", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1", "@vitest/coverage-v8": "^0.34.6", "@vitest/ui": "^0.34.6", "jsdom": "^23.0.1"}}
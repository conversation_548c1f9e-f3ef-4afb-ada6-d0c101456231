// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  role      UserRole @default(USER)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdPartners Partner[] @relation("CreatedBy")
  assignedTasks   Task[]    @relation("AssignedTo")
  sentMessages    Message[] @relation("SentBy")

  @@map("users")
}

model Partner {
  id          String        @id @default(cuid())
  companyName String
  industry    String
  size        CompanySize
  revenue     Float?
  website     String?
  description String?
  status      PartnerStatus @default(PROSPECT)
  score       Float?
  priority    Priority      @default(MEDIUM)
  createdById String
  createdBy   User          @relation("CreatedBy", fields: [createdById], references: [id])
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  contacts      Contact[]
  agreements    Agreement[]
  opportunities Opportunity[]
  activities    Activity[]
  tasks         Task[]

  @@map("partners")
}

model Contact {
  id        String   @id @default(cuid())
  name      String
  email     String
  phone     String?
  position  String
  isPrimary Boolean  @default(false)
  partnerId String
  partner   Partner  @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  activities Activity[]

  @@map("contacts")
}

model Agreement {
  id          String          @id @default(cuid())
  title       String
  type        AgreementType
  status      AgreementStatus @default(DRAFT)
  content     String?
  terms       Json?
  value       Float?
  startDate   DateTime?
  endDate     DateTime?
  signedAt    DateTime?
  partnerId   String
  partner     Partner         @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  @@map("agreements")
}

model Opportunity {
  id          String            @id @default(cuid())
  title       String
  description String?
  value       Float
  currency    String            @default("USD")
  stage       OpportunityStage  @default(QUALIFICATION)
  probability Int               @default(0)
  expectedCloseDate DateTime?
  actualCloseDate   DateTime?
  partnerId   String
  partner     Partner           @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  // Relations
  activities Activity[]
  tasks      Task[]

  @@map("opportunities")
}

model Activity {
  id            String         @id @default(cuid())
  type          ActivityType
  subject       String
  description   String?
  status        ActivityStatus @default(PLANNED)
  priority      Priority       @default(MEDIUM)
  scheduledAt   DateTime?
  completedAt   DateTime?
  duration      Int? // in minutes
  outcome       String?
  partnerId     String?
  partner       Partner?       @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  contactId     String?
  contact       Contact?       @relation(fields: [contactId], references: [id], onDelete: SetNull)
  opportunityId String?
  opportunity   Opportunity?   @relation(fields: [opportunityId], references: [id], onDelete: Cascade)
  agentId       String?
  agent         Agent?         @relation(fields: [agentId], references: [id], onDelete: SetNull)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  @@map("activities")
}

model Agent {
  id           String      @id @default(cuid())
  name         String
  type         AgentType
  status       AgentStatus @default(IDLE)
  capabilities Json
  config       Json?
  isActive     Boolean     @default(true)
  lastActiveAt DateTime?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  // Relations
  tasks      Task[]
  activities Activity[]
  messages   Message[]

  @@map("agents")
}

model Task {
  id            String     @id @default(cuid())
  title         String
  description   String?
  type          TaskType
  status        TaskStatus @default(PENDING)
  priority      Priority   @default(MEDIUM)
  data          Json?
  result        Json?
  error         String?
  progress      Int        @default(0)
  maxRetries    Int        @default(3)
  retryCount    Int        @default(0)
  scheduledAt   DateTime?
  startedAt     DateTime?
  completedAt   DateTime?
  agentId       String
  agent         Agent      @relation(fields: [agentId], references: [id], onDelete: Cascade)
  assignedToId  String?
  assignedTo    User?      @relation("AssignedTo", fields: [assignedToId], references: [id], onDelete: SetNull)
  partnerId     String?
  partner       Partner?   @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  opportunityId String?
  opportunity   Opportunity? @relation(fields: [opportunityId], references: [id], onDelete: Cascade)
  parentTaskId  String?
  parentTask    Task?      @relation("SubTasks", fields: [parentTaskId], references: [id], onDelete: Cascade)
  subTasks      Task[]     @relation("SubTasks")
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  @@map("tasks")
}

model Message {
  id        String      @id @default(cuid())
  type      MessageType
  content   String
  metadata  Json?
  fromId    String?
  from      User?       @relation("SentBy", fields: [fromId], references: [id], onDelete: SetNull)
  agentId   String?
  agent     Agent?      @relation(fields: [agentId], references: [id], onDelete: SetNull)
  isRead    Boolean     @default(false)
  createdAt DateTime    @default(now())

  @@map("messages")
}

model Workflow {
  id          String           @id @default(cuid())
  name        String
  description String?
  definition  Json
  isActive    Boolean          @default(true)
  version     Int              @default(1)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  // Relations
  executions WorkflowExecution[]

  @@map("workflows")
}

model WorkflowExecution {
  id         String                @id @default(cuid())
  status     WorkflowExecutionStatus @default(RUNNING)
  context    Json?
  result     Json?
  error      String?
  startedAt  DateTime              @default(now())
  completedAt DateTime?
  workflowId String
  workflow   Workflow              @relation(fields: [workflowId], references: [id], onDelete: Cascade)

  @@map("workflow_executions")
}

// Enums
enum UserRole {
  ADMIN
  MANAGER
  USER
}

enum CompanySize {
  STARTUP
  SMALL
  MEDIUM
  LARGE
  ENTERPRISE
}

enum PartnerStatus {
  PROSPECT
  CONTACTED
  QUALIFIED
  NEGOTIATING
  ACTIVE
  INACTIVE
  REJECTED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum AgreementType {
  NDA
  PARTNERSHIP
  RESELLER
  DISTRIBUTOR
  JOINT_VENTURE
}

enum AgreementStatus {
  DRAFT
  REVIEW
  NEGOTIATION
  SIGNED
  ACTIVE
  EXPIRED
  TERMINATED
}

enum OpportunityStage {
  QUALIFICATION
  NEEDS_ANALYSIS
  PROPOSAL
  NEGOTIATION
  CLOSED_WON
  CLOSED_LOST
}

enum ActivityType {
  CALL
  EMAIL
  MEETING
  TRAINING
  PROPOSAL
  DEMO
  FOLLOW_UP
  RESEARCH
}

enum ActivityStatus {
  PLANNED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  RESCHEDULED
}

enum AgentType {
  PARTNER_SCOUT
  PARTNER_RECRUITER
  PARTNER_TRAINER
  OPPORTUNITY_MANAGER
  MARKET_EXPANDER
}

enum AgentStatus {
  IDLE
  WORKING
  ERROR
  OFFLINE
  MAINTENANCE
}

enum TaskType {
  FIND_PARTNERS
  SCORE_PARTNER
  SEND_EMAIL
  SCHEDULE_MEETING
  CREATE_PROPOSAL
  ANALYZE_OPPORTUNITY
  GENERATE_REPORT
  TRAIN_PARTNER
  MARKET_ANALYSIS
}

enum TaskStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
  RETRYING
}

enum MessageType {
  SYSTEM
  USER
  AGENT
  NOTIFICATION
  ERROR
}

enum WorkflowExecutionStatus {
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

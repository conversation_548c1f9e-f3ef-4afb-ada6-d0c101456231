# EcoBuilder 多智能体群系统开发提示词

## 项目概述
开发一个名为 EcoBuilder 的多智能体群系统，用于自动化生态合作伙伴开发的完整流程。系统包含5个专业智能体，每个负责生态合作的不同阶段，通过协同工作实现从合作伙伴发现到市场拓展的全流程自动化。

## 系统架构要求

### 技术栈
- **前端**: React + TypeScript + Tailwind CSS + Vite
- **后端**: Node.js + Express + TypeScript
- **数据库**: PostgreSQL + Prisma ORM
- **AI集成**: OpenAI GPT-4 API / Claude API
- **实时通信**: Socket.io
- **文件存储**: AWS S3 或本地存储
- **部署**: Docker + Docker Compose

### 核心智能体设计

#### 1. PartnerScout Agent (合作伙伴发现智能体)
**职责**: 识别和筛选潜在合作伙伴
**功能模块**:
- 资源分析引擎: 分析现有客户、供应商、行业关系
- 推荐算法: 基于公司生态要求匹配合适伙伴
- 线索评分: 对潜在伙伴进行优先级排序
- CRM集成: 自动录入和更新伙伴信息

**工具集成**:
- CRM系统API (Salesforce/HubSpot)
- 企业信息查询API (企查查/天眼查)
- 社交网络分析工具
- 行业数据库接口

#### 2. PartnerRecruiter Agent (合作伙伴招募智能体)
**职责**: 与潜在合作伙伴建立联系并达成合作
**功能模块**:
- CEO联系策略: 智能生成个性化沟通方案
- 会议安排: 自动化日程协调和会议安排
- 协议生成: 基于模板生成定制化合作协议
- 谈判支持: 提供谈判策略和关键点分析

**工具集成**:
- 邮件自动化系统 (SendGrid/Mailgun)
- 日历集成 (Google Calendar/Outlook)
- 合同管理系统
- 电子签名平台 (DocuSign)

#### 3. PartnerTrainer Agent (合作伙伴培训智能体)
**职责**: 对接销售团队并提供产品培训
**功能模块**:
- 培训内容生成: 自动创建个性化培训材料
- 能力评估: 评估销售团队产品知识水平
- 培训计划: 制定阶段性培训计划
- 认证管理: 销售能力认证和跟踪

**工具集成**:
- 学习管理系统 (LMS)
- 视频会议平台 (Zoom/Teams)
- 培训内容管理系统
- 考试评估平台

#### 4. OpportunityManager Agent (商机管理智能体)
**职责**: 管理销售机会和客户商机
**功能模块**:
- 商机识别: 分析和识别潜在销售机会
- 销售计划: 制定详细的销售推进计划
- 进度跟踪: 实时跟踪销售进展
- 成功案例: 记录和分析成功案例

**工具集成**:
- 销售管道管理系统
- 客户数据平台 (CDP)
- 报价系统
- 项目管理工具

#### 5. MarketExpander Agent (市场拓展智能体)
**职责**: 联合市场活动和商机复制
**功能模块**:
- 活动策划: 设计联合营销活动
- 商机复制: 基于成功案例复制商机
- 市场分析: 分析市场趋势和机会
- ROI跟踪: 跟踪营销活动效果

**工具集成**:
- 营销自动化平台 (HubSpot/Marketo)
- 活动管理系统
- 数据分析平台
- 社交媒体管理工具

## 系统功能要求

### 1. 智能体协调中心
- **工作流引擎**: 定义和执行多智能体协作流程
- **消息总线**: 智能体间通信和数据交换
- **状态管理**: 跟踪每个合作伙伴的当前状态
- **任务调度**: 自动分配和调度任务给相应智能体

### 2. 数据管理系统
- **合作伙伴档案**: 完整的伙伴信息管理
- **交互历史**: 记录所有沟通和交互历史
- **文档管理**: 合同、协议、培训材料等文档管理
- **分析报表**: 生态合作效果分析和报告

### 3. 用户界面设计
- **仪表板**: 生态合作全景视图
- **智能体监控**: 实时查看各智能体工作状态
- **流程可视化**: 合作伙伴发展流程图
- **交互界面**: 与智能体交互的聊天界面

### 4. 集成接口
- **CRM集成**: 与主流CRM系统无缝集成
- **ERP集成**: 与企业资源规划系统对接
- **通信工具**: 邮件、短信、即时通讯集成
- **第三方API**: 企业信息、行业数据等API集成

## 开发阶段规划

### Phase 1: 基础架构搭建 (2周)
1. 项目初始化和环境配置
2. 数据库设计和模型创建
3. 基础API框架搭建
4. 前端项目初始化

### Phase 2: 核心智能体开发 (4周)
1. PartnerScout Agent 开发和测试
2. PartnerRecruiter Agent 开发和测试
3. 智能体间通信机制实现
4. 基础UI界面开发

### Phase 3: 高级功能实现 (3周)
1. PartnerTrainer Agent 开发
2. OpportunityManager Agent 开发
3. MarketExpander Agent 开发
4. 工作流引擎实现

### Phase 4: 集成和优化 (2周)
1. 第三方系统集成
2. 性能优化和安全加固
3. 用户界面完善
4. 全面测试

### Phase 5: 部署和发布 (1周)
1. 生产环境部署
2. 监控和日志系统
3. 用户文档编写
4. 上线发布

## 技术实现细节

### 智能体架构
```typescript
interface Agent {
  id: string;
  name: string;
  type: AgentType;
  status: AgentStatus;
  capabilities: string[];
  tools: Tool[];
  execute(task: Task): Promise<TaskResult>;
  communicate(message: Message): Promise<Response>;
}
```

### 工作流定义
```typescript
interface Workflow {
  id: string;
  name: string;
  stages: WorkflowStage[];
  triggers: Trigger[];
  conditions: Condition[];
}
```

### 数据模型
```typescript
interface Partner {
  id: string;
  companyName: string;
  industry: string;
  size: CompanySize;
  status: PartnerStatus;
  contacts: Contact[];
  agreements: Agreement[];
  opportunities: Opportunity[];
}
```

## 质量保证要求

### 测试策略
- **单元测试**: 每个智能体功能模块 >90% 覆盖率
- **集成测试**: 智能体间协作流程测试
- **端到端测试**: 完整业务流程自动化测试
- **性能测试**: 并发处理能力和响应时间测试

### 安全要求
- **数据加密**: 敏感数据传输和存储加密
- **访问控制**: 基于角色的权限管理
- **审计日志**: 完整的操作审计跟踪
- **API安全**: 接口认证和限流保护

### 监控和运维
- **应用监控**: 实时性能和错误监控
- **业务监控**: 关键业务指标跟踪
- **日志管理**: 结构化日志收集和分析
- **告警机制**: 异常情况自动告警

## 成功指标

### 技术指标
- 系统可用性 >99.5%
- API响应时间 <500ms
- 智能体任务成功率 >95%
- 数据处理准确率 >98%

### 业务指标
- 合作伙伴发现效率提升 >50%
- 合作达成周期缩短 >30%
- 销售培训效果提升 >40%
- 商机转化率提升 >25%

## 扩展性考虑
- 支持新智能体类型的插件化扩展
- 支持自定义工作流配置
- 支持多租户架构
- 支持国际化和本地化

请基于此提示词开始 EcoBuilder 多智能体群系统的全栈开发，包括完整的代码实现、测试用例编写、部署配置和文档编写。

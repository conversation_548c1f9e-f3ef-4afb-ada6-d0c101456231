import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import { createServer } from 'http';
import { Server } from 'socket.io';
import dotenv from 'dotenv';

import { PrismaClient } from '@prisma/client';
import { errorHandler } from './middleware/errorHandler';
import { authMiddleware } from './middleware/auth';
import { rateLimiter } from './middleware/rateLimiter';
import { logger } from './utils/logger';

// Routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import partnerRoutes from './routes/partners';
import agentRoutes from './routes/agents';
import taskRoutes from './routes/tasks';
import workflowRoutes from './routes/workflows';
import fileRoutes from './routes/files';

// Services
import { SocketService } from './services/SocketService';
import { AgentManager } from './services/AgentManager';
import { WorkflowEngine } from './services/WorkflowEngine';
import { TaskScheduler } from './services/TaskScheduler';
import { MessageBus } from './services/MessageBus';
import { DatabaseService } from './services/DatabaseService';
import { AIService } from './services/AIService';

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:5173",
    methods: ["GET", "POST"]
  }
});

// Initialize Prisma
export const prisma = new PrismaClient();

// Initialize services
const databaseService = new DatabaseService();
const messageBus = new MessageBus();
const taskScheduler = new TaskScheduler();
const aiService = new AIService();
const socketService = new SocketService(io);
const workflowEngine = new WorkflowEngine();
const agentManager = new AgentManager(prisma, socketService);

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || "http://localhost:5173",
  credentials: true
}));
app.use(compression());
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
app.use('/api', rateLimiter);

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV
  });
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', authMiddleware, userRoutes);
app.use('/api/partners', authMiddleware, partnerRoutes);
app.use('/api/agents', authMiddleware, agentRoutes);
app.use('/api/tasks', authMiddleware, taskRoutes);
app.use('/api/workflows', authMiddleware, workflowRoutes);
app.use('/api/files', fileRoutes); // File routes don't require auth for demo

// Socket.io connection handling
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);
  
  socket.on('join-room', (room) => {
    socket.join(room);
    logger.info(`Client ${socket.id} joined room: ${room}`);
  });

  socket.on('leave-room', (room) => {
    socket.leave(room);
    logger.info(`Client ${socket.id} left room: ${room}`);
  });

  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });
});

// Error handling
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

const PORT = process.env.PORT || 3000;

// Initialize all services
async function initializeServices() {
  logger.info('Initializing services...');

  try {
    // Initialize database service
    await databaseService.initialize();
    logger.info('✅ Database service initialized');

    // Initialize message bus
    await messageBus.initialize();
    logger.info('✅ Message bus initialized');

    // Initialize task scheduler
    await taskScheduler.initialize();
    logger.info('✅ Task scheduler initialized');

    // Initialize workflow engine
    await workflowEngine.initialize();
    logger.info('✅ Workflow engine initialized');

    // Initialize agent manager
    await agentManager.initializeAgents();
    logger.info('✅ Agent manager initialized');

    logger.info('🎉 All services initialized successfully');
  } catch (error) {
    logger.error('❌ Failed to initialize services:', error);
    throw error;
  }
}

// Graceful shutdown
async function gracefulShutdown() {
  logger.info('Shutting down gracefully...');

  try {
    // Stop accepting new connections
    server.close(() => {
      logger.info('HTTP server closed');
    });

    // Shutdown services in reverse order
    await agentManager.stopAllAgents();
    logger.info('✅ Agent manager stopped');

    await workflowEngine.shutdown();
    logger.info('✅ Workflow engine stopped');

    await taskScheduler.shutdown();
    logger.info('✅ Task scheduler stopped');

    await messageBus.shutdown();
    logger.info('✅ Message bus stopped');

    await databaseService.shutdown();
    await prisma.$disconnect();
    logger.info('✅ Database connections closed');

    logger.info('🎯 Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
}

process.on('SIGTERM', gracefulShutdown);

process.on('SIGINT', gracefulShutdown);

// Start server
async function startServer() {
  try {
    // Initialize all services first
    await initializeServices();

    // Start the HTTP server
    server.listen(PORT, () => {
      logger.info(`🚀 Server running on port ${PORT}`);
      logger.info(`📊 Environment: ${process.env.NODE_ENV}`);
      logger.info(`🔗 Frontend URL: ${process.env.FRONTEND_URL}`);
      logger.info(`🎯 EcoBuilder Multi-Agent System is ready!`);
    });
  } catch (error) {
    logger.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Start the application
startServer();

export {
  app,
  io,
  agentManager,
  workflowEngine,
  taskScheduler,
  messageBus,
  databaseService,
  aiService
};

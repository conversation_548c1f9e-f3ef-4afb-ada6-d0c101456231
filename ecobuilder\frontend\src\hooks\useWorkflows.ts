import { useState, useEffect } from 'react';
import { WorkflowExecution, WorkflowExecutionStatus } from '../../../shared/types';
import { apiClient } from '../services/api';

interface UseWorkflowsReturn {
  workflows: WorkflowExecution[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  executeWorkflow: (workflowId: string, context?: Record<string, any>) => Promise<string>;
  cancelWorkflow: (executionId: string) => Promise<void>;
  getWorkflowStatus: (executionId: string) => Promise<WorkflowExecution>;
}

export const useWorkflows = (): UseWorkflowsReturn => {
  const [workflows, setWorkflows] = useState<WorkflowExecution[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchWorkflows = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.get('/workflows/executions');
      setWorkflows(response.data.data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch workflows');
      console.error('Error fetching workflows:', err);
    } finally {
      setLoading(false);
    }
  };

  const executeWorkflow = async (workflowId: string, context?: Record<string, any>): Promise<string> => {
    try {
      const response = await apiClient.post('/workflows/execute', {
        workflowId,
        context
      });
      const executionId = response.data.data.executionId;
      
      // Add the new execution to the list
      const newExecution: WorkflowExecution = {
        id: executionId,
        workflowId,
        status: WorkflowExecutionStatus.RUNNING,
        context,
        startedAt: new Date()
      };
      setWorkflows(prev => [newExecution, ...prev]);
      
      return executionId;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to execute workflow');
      throw err;
    }
  };

  const cancelWorkflow = async (executionId: string): Promise<void> => {
    try {
      await apiClient.post(`/workflows/executions/${executionId}/cancel`);
      setWorkflows(prev => prev.map(workflow => 
        workflow.id === executionId 
          ? { ...workflow, status: WorkflowExecutionStatus.CANCELLED, completedAt: new Date() }
          : workflow
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to cancel workflow');
      throw err;
    }
  };

  const getWorkflowStatus = async (executionId: string): Promise<WorkflowExecution> => {
    try {
      const response = await apiClient.get(`/workflows/executions/${executionId}`);
      const execution = response.data.data;
      
      // Update the workflow in the list
      setWorkflows(prev => prev.map(workflow => 
        workflow.id === executionId ? execution : workflow
      ));
      
      return execution;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get workflow status');
      throw err;
    }
  };

  useEffect(() => {
    fetchWorkflows();
  }, []);

  return {
    workflows,
    loading,
    error,
    refetch: fetchWorkflows,
    executeWorkflow,
    cancelWorkflow,
    getWorkflowStatus
  };
};

// Mock data for development
const mockWorkflows: WorkflowExecution[] = [
  {
    id: 'exec-1',
    workflowId: 'workflow-partner-onboarding',
    status: WorkflowExecutionStatus.RUNNING,
    context: {
      partnerId: 'partner-123',
      partnerName: 'TechCorp Inc',
      industry: 'technology'
    },
    startedAt: new Date(Date.now() - 3600000) // 1 hour ago
  },
  {
    id: 'exec-2',
    workflowId: 'workflow-market-expansion',
    status: WorkflowExecutionStatus.COMPLETED,
    context: {
      region: 'Europe',
      targetMarkets: ['Germany', 'France', 'UK']
    },
    result: {
      recommendations: [
        'Focus on German fintech market',
        'Partner with local distributors in France',
        'Establish UK office for direct sales'
      ],
      marketSize: **********,
      timeline: '18 months'
    },
    startedAt: new Date(Date.now() - 86400000), // 1 day ago
    completedAt: new Date(Date.now() - 82800000) // 23 hours ago
  },
  {
    id: 'exec-3',
    workflowId: 'workflow-partner-discovery',
    status: WorkflowExecutionStatus.FAILED,
    context: {
      industry: 'healthcare',
      region: 'North America',
      criteria: {
        minRevenue: 10000000,
        maxEmployees: 500
      }
    },
    error: 'External API rate limit exceeded',
    startedAt: new Date(Date.now() - 7200000), // 2 hours ago
    completedAt: new Date(Date.now() - 5400000) // 1.5 hours ago
  },
  {
    id: 'exec-4',
    workflowId: 'workflow-opportunity-analysis',
    status: WorkflowExecutionStatus.CANCELLED,
    context: {
      opportunityId: 'opp-456',
      analysisType: 'comprehensive'
    },
    startedAt: new Date(Date.now() - 10800000), // 3 hours ago
    completedAt: new Date(Date.now() - 9000000) // 2.5 hours ago
  }
];

// Use mock data in development
export const useWorkflowsMock = (): UseWorkflowsReturn => {
  const [workflows, setWorkflows] = useState<WorkflowExecution[]>(mockWorkflows);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchWorkflows = async () => {
    setLoading(true);
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400));
    setWorkflows(mockWorkflows);
    setLoading(false);
  };

  const executeWorkflow = async (workflowId: string, context?: Record<string, any>): Promise<string> => {
    const executionId = `exec-${Date.now()}`;
    const newExecution: WorkflowExecution = {
      id: executionId,
      workflowId,
      status: WorkflowExecutionStatus.RUNNING,
      context,
      startedAt: new Date()
    };
    setWorkflows(prev => [newExecution, ...prev]);
    return executionId;
  };

  const cancelWorkflow = async (executionId: string): Promise<void> => {
    setWorkflows(prev => prev.map(workflow => 
      workflow.id === executionId 
        ? { ...workflow, status: WorkflowExecutionStatus.CANCELLED, completedAt: new Date() }
        : workflow
    ));
  };

  const getWorkflowStatus = async (executionId: string): Promise<WorkflowExecution> => {
    const workflow = workflows.find(w => w.id === executionId);
    if (!workflow) throw new Error('Workflow execution not found');
    return workflow;
  };

  return {
    workflows,
    loading,
    error,
    refetch: fetchWorkflows,
    executeWorkflow,
    cancelWorkflow,
    getWorkflowStatus
  };
};

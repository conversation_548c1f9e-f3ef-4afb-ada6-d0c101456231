import { useState, useEffect } from 'react';
import { Agent, AgentStatus, AgentType } from '../../../shared/types';
import { apiClient } from '../services/api';

interface UseAgentsReturn {
  agents: Agent[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  startAgent: (agentId: string) => Promise<void>;
  stopAgent: (agentId: string) => Promise<void>;
  pauseAgent: (agentId: string) => Promise<void>;
  createAgent: (agentData: Partial<Agent>) => Promise<Agent>;
  updateAgent: (agentId: string, updates: Partial<Agent>) => Promise<Agent>;
  deleteAgent: (agentId: string) => Promise<void>;
}

export const useAgents = (): UseAgentsReturn => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAgents = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.get('/agents');
      setAgents(response.data.data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch agents');
      console.error('Error fetching agents:', err);
    } finally {
      setLoading(false);
    }
  };

  const startAgent = async (agentId: string) => {
    try {
      await apiClient.post(`/agents/${agentId}/start`);
      setAgents(prev => prev.map(agent => 
        agent.id === agentId 
          ? { ...agent, status: AgentStatus.WORKING, isActive: true }
          : agent
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start agent');
      throw err;
    }
  };

  const stopAgent = async (agentId: string) => {
    try {
      await apiClient.post(`/agents/${agentId}/stop`);
      setAgents(prev => prev.map(agent => 
        agent.id === agentId 
          ? { ...agent, status: AgentStatus.OFFLINE, isActive: false }
          : agent
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to stop agent');
      throw err;
    }
  };

  const pauseAgent = async (agentId: string) => {
    try {
      await apiClient.post(`/agents/${agentId}/pause`);
      setAgents(prev => prev.map(agent => 
        agent.id === agentId 
          ? { ...agent, status: AgentStatus.IDLE }
          : agent
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to pause agent');
      throw err;
    }
  };

  const createAgent = async (agentData: Partial<Agent>): Promise<Agent> => {
    try {
      const response = await apiClient.post('/agents', agentData);
      const newAgent = response.data.data;
      setAgents(prev => [...prev, newAgent]);
      return newAgent;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create agent');
      throw err;
    }
  };

  const updateAgent = async (agentId: string, updates: Partial<Agent>): Promise<Agent> => {
    try {
      const response = await apiClient.put(`/agents/${agentId}`, updates);
      const updatedAgent = response.data.data;
      setAgents(prev => prev.map(agent => 
        agent.id === agentId ? updatedAgent : agent
      ));
      return updatedAgent;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update agent');
      throw err;
    }
  };

  const deleteAgent = async (agentId: string): Promise<void> => {
    try {
      await apiClient.delete(`/agents/${agentId}`);
      setAgents(prev => prev.filter(agent => agent.id !== agentId));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete agent');
      throw err;
    }
  };

  useEffect(() => {
    fetchAgents();
  }, []);

  return {
    agents,
    loading,
    error,
    refetch: fetchAgents,
    startAgent,
    stopAgent,
    pauseAgent,
    createAgent,
    updateAgent,
    deleteAgent
  };
};

// Mock data for development
const mockAgents: Agent[] = [
  {
    id: 'agent-1',
    name: 'Partner Scout Alpha',
    type: AgentType.PARTNER_SCOUT,
    status: AgentStatus.WORKING,
    capabilities: ['partner_discovery', 'company_research', 'partner_scoring'],
    tools: [],
    isActive: true,
    lastActiveAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'agent-2',
    name: 'Partner Recruiter Beta',
    type: AgentType.PARTNER_RECRUITER,
    status: AgentStatus.IDLE,
    capabilities: ['email_outreach', 'meeting_scheduling', 'relationship_building'],
    tools: [],
    isActive: true,
    lastActiveAt: new Date(Date.now() - 3600000), // 1 hour ago
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'agent-3',
    name: 'Partner Trainer Gamma',
    type: AgentType.PARTNER_TRAINER,
    status: AgentStatus.IDLE,
    capabilities: ['training_content', 'onboarding', 'knowledge_transfer'],
    tools: [],
    isActive: true,
    lastActiveAt: new Date(Date.now() - 7200000), // 2 hours ago
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'agent-4',
    name: 'Opportunity Manager Delta',
    type: AgentType.OPPORTUNITY_MANAGER,
    status: AgentStatus.ERROR,
    capabilities: ['opportunity_tracking', 'pipeline_management', 'forecasting'],
    tools: [],
    isActive: false,
    lastActiveAt: new Date(Date.now() - 86400000), // 1 day ago
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'agent-5',
    name: 'Market Expander Epsilon',
    type: AgentType.MARKET_EXPANDER,
    status: AgentStatus.MAINTENANCE,
    capabilities: ['market_analysis', 'expansion_planning', 'competitive_intelligence'],
    tools: [],
    isActive: false,
    lastActiveAt: new Date(Date.now() - 172800000), // 2 days ago
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Use mock data in development
export const useAgentsMock = (): UseAgentsReturn => {
  const [agents, setAgents] = useState<Agent[]>(mockAgents);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAgents = async () => {
    setLoading(true);
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    setAgents(mockAgents);
    setLoading(false);
  };

  const startAgent = async (agentId: string) => {
    setAgents(prev => prev.map(agent => 
      agent.id === agentId 
        ? { ...agent, status: AgentStatus.WORKING, isActive: true }
        : agent
    ));
  };

  const stopAgent = async (agentId: string) => {
    setAgents(prev => prev.map(agent => 
      agent.id === agentId 
        ? { ...agent, status: AgentStatus.OFFLINE, isActive: false }
        : agent
    ));
  };

  const pauseAgent = async (agentId: string) => {
    setAgents(prev => prev.map(agent => 
      agent.id === agentId 
        ? { ...agent, status: AgentStatus.IDLE }
        : agent
    ));
  };

  const createAgent = async (agentData: Partial<Agent>): Promise<Agent> => {
    const newAgent: Agent = {
      id: `agent-${Date.now()}`,
      name: agentData.name || 'New Agent',
      type: agentData.type || AgentType.PARTNER_SCOUT,
      status: AgentStatus.IDLE,
      capabilities: agentData.capabilities || [],
      tools: [],
      isActive: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...agentData
    };
    setAgents(prev => [...prev, newAgent]);
    return newAgent;
  };

  const updateAgent = async (agentId: string, updates: Partial<Agent>): Promise<Agent> => {
    const updatedAgent = agents.find(a => a.id === agentId);
    if (!updatedAgent) throw new Error('Agent not found');
    
    const newAgent = { ...updatedAgent, ...updates, updatedAt: new Date() };
    setAgents(prev => prev.map(agent => 
      agent.id === agentId ? newAgent : agent
    ));
    return newAgent;
  };

  const deleteAgent = async (agentId: string): Promise<void> => {
    setAgents(prev => prev.filter(agent => agent.id !== agentId));
  };

  return {
    agents,
    loading,
    error,
    refetch: fetchAgents,
    startAgent,
    stopAgent,
    pauseAgent,
    createAgent,
    updateAgent,
    deleteAgent
  };
};

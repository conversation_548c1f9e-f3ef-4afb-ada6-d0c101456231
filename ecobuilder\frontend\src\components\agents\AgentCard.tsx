import React from 'react';
import { 
  <PERSON>lt<PERSON>con, 
  ExclamationTriangleIcon, 
  CheckC<PERSON>cleIcon, 
  ClockIcon,
  CogIcon,
  PlayIcon,
  PauseIcon,
  StopIcon
} from '@heroicons/react/24/outline';
import { Agent, AgentStatus, AgentType } from '../../../../shared/types';

interface AgentCardProps {
  agent: Agent;
  onStart?: (agentId: string) => void;
  onStop?: (agentId: string) => void;
  onPause?: (agentId: string) => void;
}

const getAgentTypeIcon = (type: AgentType) => {
  switch (type) {
    case AgentType.PARTNER_SCOUT:
      return '🔍';
    case AgentType.PARTNER_RECRUITER:
      return '🤝';
    case AgentType.PARTNER_TRAINER:
      return '📚';
    case AgentType.OPPORTUNITY_MANAGER:
      return '💼';
    case AgentType.MARKET_EXPANDER:
      return '📈';
    default:
      return '🤖';
  }
};

const getAgentTypeName = (type: AgentType) => {
  switch (type) {
    case AgentType.PARTNER_SCOUT:
      return 'Partner Scout';
    case AgentType.PARTNER_RECRUITER:
      return 'Partner Recruiter';
    case AgentType.PARTNER_TRAINER:
      return 'Partner Trainer';
    case AgentType.OPPORTUNITY_MANAGER:
      return 'Opportunity Manager';
    case AgentType.MARKET_EXPANDER:
      return 'Market Expander';
    default:
      return 'Unknown Agent';
  }
};

const getStatusColor = (status: AgentStatus) => {
  switch (status) {
    case AgentStatus.WORKING:
      return 'bg-green-100 text-green-800';
    case AgentStatus.IDLE:
      return 'bg-blue-100 text-blue-800';
    case AgentStatus.ERROR:
      return 'bg-red-100 text-red-800';
    case AgentStatus.OFFLINE:
      return 'bg-gray-100 text-gray-800';
    case AgentStatus.MAINTENANCE:
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusIcon = (status: AgentStatus) => {
  switch (status) {
    case AgentStatus.WORKING:
      return BoltIcon;
    case AgentStatus.IDLE:
      return ClockIcon;
    case AgentStatus.ERROR:
      return ExclamationTriangleIcon;
    case AgentStatus.OFFLINE:
      return StopIcon;
    case AgentStatus.MAINTENANCE:
      return CogIcon;
    default:
      return ClockIcon;
  }
};

export const AgentCard: React.FC<AgentCardProps> = ({ 
  agent, 
  onStart, 
  onStop, 
  onPause 
}) => {
  const StatusIcon = getStatusIcon(agent.status);
  const statusColor = getStatusColor(agent.status);
  const agentIcon = getAgentTypeIcon(agent.type);
  const agentTypeName = getAgentTypeName(agent.type);

  const handleAction = (action: 'start' | 'stop' | 'pause') => {
    switch (action) {
      case 'start':
        onStart?.(agent.id);
        break;
      case 'stop':
        onStop?.(agent.id);
        break;
      case 'pause':
        onPause?.(agent.id);
        break;
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className="text-2xl">{agentIcon}</div>
          <div>
            <h3 className="text-sm font-medium text-gray-900">{agent.name}</h3>
            <p className="text-xs text-gray-500">{agentTypeName}</p>
          </div>
        </div>
        <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColor}`}>
          <StatusIcon className="w-3 h-3 mr-1" />
          {agent.status}
        </div>
      </div>

      {/* Capabilities */}
      <div className="mb-3">
        <p className="text-xs text-gray-500 mb-1">Capabilities:</p>
        <div className="flex flex-wrap gap-1">
          {agent.capabilities.slice(0, 3).map((capability, index) => (
            <span 
              key={index}
              className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
            >
              {capability}
            </span>
          ))}
          {agent.capabilities.length > 3 && (
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
              +{agent.capabilities.length - 3} more
            </span>
          )}
        </div>
      </div>

      {/* Last Active */}
      {agent.lastActiveAt && (
        <div className="mb-3">
          <p className="text-xs text-gray-500">
            Last active: {new Date(agent.lastActiveAt).toLocaleString()}
          </p>
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between pt-3 border-t border-gray-100">
        <div className="flex space-x-2">
          {agent.status === AgentStatus.IDLE && (
            <button
              onClick={() => handleAction('start')}
              className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 transition-colors"
            >
              <PlayIcon className="w-3 h-3 mr-1" />
              Start
            </button>
          )}
          
          {agent.status === AgentStatus.WORKING && (
            <button
              onClick={() => handleAction('pause')}
              className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-yellow-700 bg-yellow-100 hover:bg-yellow-200 transition-colors"
            >
              <PauseIcon className="w-3 h-3 mr-1" />
              Pause
            </button>
          )}
          
          {(agent.status === AgentStatus.WORKING || agent.status === AgentStatus.IDLE) && (
            <button
              onClick={() => handleAction('stop')}
              className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors"
            >
              <StopIcon className="w-3 h-3 mr-1" />
              Stop
            </button>
          )}
        </div>

        {/* Status Indicator */}
        <div className="flex items-center space-x-1">
          <div className={`w-2 h-2 rounded-full ${
            agent.status === AgentStatus.WORKING ? 'bg-green-500 animate-pulse' :
            agent.status === AgentStatus.IDLE ? 'bg-blue-500' :
            agent.status === AgentStatus.ERROR ? 'bg-red-500' :
            'bg-gray-400'
          }`}></div>
          <span className="text-xs text-gray-500">
            {agent.isActive ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>

      {/* Progress Bar for Working Agents */}
      {agent.status === AgentStatus.WORKING && (
        <div className="mt-3">
          <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
            <span>Processing...</span>
            <span>75%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div 
              className="bg-green-500 h-1.5 rounded-full transition-all duration-300"
              style={{ width: '75%' }}
            ></div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {agent.status === AgentStatus.ERROR && (
        <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="w-3 h-3 mr-1" />
            <span className="font-medium">Error detected</span>
          </div>
          <p className="mt-1">Agent encountered an error and needs attention.</p>
        </div>
      )}

      {/* Maintenance Mode */}
      {agent.status === AgentStatus.MAINTENANCE && (
        <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-700">
          <div className="flex items-center">
            <CogIcon className="w-3 h-3 mr-1" />
            <span className="font-medium">Maintenance Mode</span>
          </div>
          <p className="mt-1">Agent is undergoing maintenance and will be back soon.</p>
        </div>
      )}
    </div>
  );
};

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AGI2B - 智能营销服务平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #667eea;
        }

        main {
            margin-top: 80px;
            padding: 2rem 0;
        }

        .hero {
            text-align: center;
            padding: 4rem 0;
            color: white;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .cta-button {
            display: inline-block;
            background: #ff6b6b;
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
        }

        .services {
            background: white;
            padding: 4rem 0;
            margin: 2rem 0;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .services h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #333;
        }

        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .service-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            color: white;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .service-card i {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .service-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .service-features {
            margin-top: 1rem;
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
        }

        .feature-tag {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .features {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 4rem 0;
            margin: 2rem 0;
            border-radius: 20px;
            color: white;
        }

        .features h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .feature-item {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-item i {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #ffd700;
        }

        .feature-item h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
        }

        .chat-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }

        .chat-button {
            background: #25d366;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
            transition: transform 0.3s;
        }

        .chat-button:hover {
            transform: scale(1.1);
        }

        .chat-modal {
            display: none;
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .chat-header {
            background: #25d366;
            color: white;
            padding: 1rem;
            text-align: center;
            font-weight: bold;
        }

        .chat-body {
            height: 350px;
            padding: 1rem;
            overflow-y: auto;
            background: #f5f5f5;
        }

        .chat-input {
            display: flex;
            padding: 1rem;
            background: white;
        }

        .chat-input input {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 20px;
            padding: 0.5rem 1rem;
            outline: none;
        }

        .chat-input button {
            background: #25d366;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            margin-left: 0.5rem;
            cursor: pointer;
        }

        .message {
            margin-bottom: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 15px;
            max-width: 80%;
        }

        .user-message {
            background: #667eea;
            color: white;
            margin-left: auto;
        }

        .bot-message {
            background: white;
            color: #333;
            border: 1px solid #ddd;
        }

        .modal-controls {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .modal-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            background: #667eea;
            color: white;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .modal-btn:hover {
            background: #5a6fd8;
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .chat-modal {
                width: 90%;
                right: 5%;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <div class="logo">AGI2B</div>
            <ul class="nav-links">
                <li><a href="#home">首页</a></li>
                <li><a href="#services">服务</a></li>
                <li><a href="#about">关于我们</a></li>
                <li><a href="#contact">联系我们</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section class="hero" id="home">
            <div class="container">
                <h1>AGI2B 智能营销服务平台</h1>
                <p>为营销人员提供全方位的AI驱动解决方案，助力业务增长</p>
                <a href="#services" class="cta-button">开始体验</a>
            </div>
        </section>

        <section class="services" id="services">
            <div class="container">
                <h2>我们的服务</h2>
                <div class="service-grid">
                    <div class="service-card" onclick="openServiceModal('ecosystem')">
                        <i class="fas fa-network-wired"></i>
                        <h3>生态拓展</h3>
                        <p>智能分析市场机会，构建合作伙伴网络，扩大业务生态圈</p>
                        <div class="service-features">
                            <span class="feature-tag">AI分析</span>
                            <span class="feature-tag">合作匹配</span>
                            <span class="feature-tag">生态构建</span>
                        </div>
                    </div>
                    <div class="service-card" onclick="openServiceModal('sales')">
                        <i class="fas fa-handshake"></i>
                        <h3>销售助理</h3>
                        <p>AI驱动的销售流程优化，客户关系管理，提升转化率</p>
                        <div class="service-features">
                            <span class="feature-tag">流程优化</span>
                            <span class="feature-tag">CRM管理</span>
                            <span class="feature-tag">转化提升</span>
                        </div>
                    </div>
                    <div class="service-card" onclick="openServiceModal('product')">
                        <i class="fas fa-lightbulb"></i>
                        <h3>产品研发</h3>
                        <p>市场需求分析，产品创新建议，研发流程智能化管理</p>
                        <div class="service-features">
                            <span class="feature-tag">需求分析</span>
                            <span class="feature-tag">创新建议</span>
                            <span class="feature-tag">流程管理</span>
                        </div>
                    </div>
                    <div class="service-card" onclick="openServiceModal('presales')">
                        <i class="fas fa-users"></i>
                        <h3>售前支持</h3>
                        <p>智能客户画像，需求分析，方案定制，提升成单率</p>
                        <div class="service-features">
                            <span class="feature-tag">客户画像</span>
                            <span class="feature-tag">需求分析</span>
                            <span class="feature-tag">方案定制</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="features" id="features">
            <div class="container">
                <h2>平台特色</h2>
                <div class="features-grid">
                    <div class="feature-item">
                        <i class="fas fa-robot"></i>
                        <h3>AI智能引擎</h3>
                        <p>基于先进的人工智能技术，提供智能化的营销解决方案</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-chart-line"></i>
                        <h3>数据驱动</h3>
                        <p>深度数据分析，为您的营销决策提供科学依据</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-shield-alt"></i>
                        <h3>安全可靠</h3>
                        <p>企业级安全保障，确保您的数据和隐私安全</p>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-headset"></i>
                        <h3>24/7支持</h3>
                        <p>全天候专业客服支持，随时为您解决问题</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 多模态客服聊天组件 -->
    <div class="chat-widget">
        <button class="chat-button" onclick="toggleChat()">
            <i class="fas fa-comments"></i>
        </button>
        
        <div class="chat-modal" id="chatModal">
            <div class="chat-header">
                AGI2B 智能客服
                <button style="float: right; background: none; border: none; color: white; cursor: pointer;" onclick="toggleChat()">×</button>
            </div>
            <div class="chat-body" id="chatBody">
                <div class="modal-controls">
                    <button class="modal-btn" onclick="sendQuickMessage('我想了解生态拓展服务')">生态拓展</button>
                    <button class="modal-btn" onclick="sendQuickMessage('销售助理功能介绍')">销售助理</button>
                    <button class="modal-btn" onclick="sendQuickMessage('产品研发支持')">产品研发</button>
                </div>
                <div class="message bot-message">
                    您好！我是AGI2B智能客服助手。我可以为您提供文字、语音、图片等多种方式的交流。请问有什么可以帮助您的吗？
                </div>
            </div>
            <div class="chat-input">
                <input type="text" id="chatInput" placeholder="输入您的问题..." onkeypress="handleKeyPress(event)">
                <button onclick="sendMessage()"><i class="fas fa-paper-plane"></i></button>
                <button onclick="startVoiceInput()" title="语音输入"><i class="fas fa-microphone"></i></button>
                <button onclick="uploadImage()" title="上传图片"><i class="fas fa-image"></i></button>
            </div>
        </div>
    </div>

    <script>
        // 聊天功能
        function toggleChat() {
            const modal = document.getElementById('chatModal');
            modal.style.display = modal.style.display === 'block' ? 'none' : 'block';
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            if (message) {
                addMessage(message, 'user');
                input.value = '';
                
                // 模拟AI回复
                setTimeout(() => {
                    const response = generateAIResponse(message);
                    addMessage(response, 'bot');
                }, 1000);
            }
        }

        function sendQuickMessage(message) {
            addMessage(message, 'user');
            setTimeout(() => {
                const response = generateAIResponse(message);
                addMessage(response, 'bot');
            }, 1000);
        }

        function addMessage(text, sender) {
            const chatBody = document.getElementById('chatBody');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            messageDiv.textContent = text;
            chatBody.appendChild(messageDiv);
            chatBody.scrollTop = chatBody.scrollHeight;
        }

        function generateAIResponse(message) {
            const responses = {
                '生态拓展': '我们的生态拓展服务包括：\n• 智能合作伙伴匹配\n• 市场机会分析\n• 生态网络构建\n• 合作模式优化\n\n您希望了解哪个方面的详细信息？',
                '销售助理': '销售助理功能特色：\n• AI客户画像分析\n• 销售流程自动化\n• 智能跟进提醒\n• 成交概率预测\n\n可以帮助您提升30%以上的销售效率！',
                '产品研发': '产品研发支持服务：\n• 市场需求洞察\n• 竞品分析报告\n• 创新方向建议\n• 研发进度管理\n\n让数据驱动您的产品决策！',
                '售前支持': '售前支持解决方案：\n• 客户需求挖掘\n• 个性化方案生成\n• 竞争优势分析\n• 成单率提升策略\n\n专业的售前团队随时为您服务！'
            };

            for (let key in responses) {
                if (message.includes(key)) {
                    return responses[key];
                }
            }

            return '感谢您的咨询！我们的专业团队会根据您的具体需求，为您提供定制化的解决方案。您可以详细描述一下您的业务场景，我会为您推荐最适合的服务。';
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function startVoiceInput() {
            if ('webkitSpeechRecognition' in window) {
                const recognition = new webkitSpeechRecognition();
                recognition.lang = 'zh-CN';
                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    document.getElementById('chatInput').value = transcript;
                };
                recognition.start();
                addMessage('🎤 正在监听您的语音...', 'bot');
            } else {
                addMessage('抱歉，您的浏览器不支持语音输入功能。', 'bot');
            }
        }

        function uploadImage() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(event) {
                const file = event.target.files[0];
                if (file) {
                    addMessage(`📷 已上传图片: ${file.name}`, 'user');
                    addMessage('我已收到您的图片，正在分析中... 我们的AI视觉分析功能可以帮助您进行图片内容识别、产品分析等服务。', 'bot');
                }
            };
            input.click();
        }

        function openServiceModal(service) {
            const serviceInfo = {
                'ecosystem': '生态拓展服务详情',
                'sales': '销售助理服务详情', 
                'product': '产品研发服务详情',
                'presales': '售前支持服务详情'
            };
            
            addMessage(`我想了解${serviceInfo[service]}`, 'user');
            toggleChat();
        }

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>

// Business entity types for EcoBuilder

export interface Partner {
  id: string;
  companyName: string;
  industry: string;
  size: CompanySize;
  revenue?: number;
  website?: string;
  description?: string;
  status: PartnerStatus;
  score?: number;
  priority: Priority;
  createdById: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations
  contacts?: Contact[];
  agreements?: Agreement[];
  opportunities?: Opportunity[];
  activities?: Activity[];
  tasks?: Task[];
}

export interface Contact {
  id: string;
  name: string;
  email: string;
  phone?: string;
  position: string;
  isPrimary: boolean;
  partnerId: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations
  activities?: Activity[];
}

export interface Agreement {
  id: string;
  title: string;
  type: AgreementType;
  status: AgreementStatus;
  content?: string;
  terms?: Record<string, any>;
  value?: number;
  startDate?: Date;
  endDate?: Date;
  signedAt?: Date;
  partnerId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Opportunity {
  id: string;
  title: string;
  description?: string;
  value: number;
  currency: string;
  stage: OpportunityStage;
  probability: number;
  expectedCloseDate?: Date;
  actualCloseDate?: Date;
  partnerId: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations
  activities?: Activity[];
  tasks?: Task[];
}

export interface Activity {
  id: string;
  type: ActivityType;
  subject: string;
  description?: string;
  status: ActivityStatus;
  priority: Priority;
  scheduledAt?: Date;
  completedAt?: Date;
  duration?: number; // in minutes
  outcome?: string;
  partnerId?: string;
  contactId?: string;
  opportunityId?: string;
  agentId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Workflow {
  id: string;
  name: string;
  description?: string;
  definition: WorkflowDefinition;
  isActive: boolean;
  version: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkflowDefinition {
  stages: WorkflowStage[];
  triggers: WorkflowTrigger[];
  conditions: WorkflowCondition[];
}

export interface WorkflowStage {
  id: string;
  name: string;
  agentType: AgentType;
  tasks: TaskDefinition[];
  nextStages: string[];
  conditions?: WorkflowCondition[];
}

export interface WorkflowTrigger {
  type: TriggerType;
  conditions: WorkflowCondition[];
  data?: Record<string, any>;
}

export interface WorkflowCondition {
  field: string;
  operator: ConditionOperator;
  value: any;
  logicalOperator?: LogicalOperator;
}

export interface TaskDefinition {
  type: TaskType;
  config: Record<string, any>;
  priority: Priority;
  retries: number;
}

export interface WorkflowExecution {
  id: string;
  status: WorkflowExecutionStatus;
  context?: Record<string, any>;
  result?: Record<string, any>;
  error?: string;
  startedAt: Date;
  completedAt?: Date;
  workflowId: string;
}

// Enums
export enum CompanySize {
  STARTUP = 'STARTUP',
  SMALL = 'SMALL',
  MEDIUM = 'MEDIUM',
  LARGE = 'LARGE',
  ENTERPRISE = 'ENTERPRISE'
}

export enum PartnerStatus {
  PROSPECT = 'PROSPECT',
  CONTACTED = 'CONTACTED',
  QUALIFIED = 'QUALIFIED',
  NEGOTIATING = 'NEGOTIATING',
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  REJECTED = 'REJECTED'
}

export enum AgreementType {
  NDA = 'NDA',
  PARTNERSHIP = 'PARTNERSHIP',
  RESELLER = 'RESELLER',
  DISTRIBUTOR = 'DISTRIBUTOR',
  JOINT_VENTURE = 'JOINT_VENTURE'
}

export enum AgreementStatus {
  DRAFT = 'DRAFT',
  REVIEW = 'REVIEW',
  NEGOTIATION = 'NEGOTIATION',
  SIGNED = 'SIGNED',
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  TERMINATED = 'TERMINATED'
}

export enum OpportunityStage {
  QUALIFICATION = 'QUALIFICATION',
  NEEDS_ANALYSIS = 'NEEDS_ANALYSIS',
  PROPOSAL = 'PROPOSAL',
  NEGOTIATION = 'NEGOTIATION',
  CLOSED_WON = 'CLOSED_WON',
  CLOSED_LOST = 'CLOSED_LOST'
}

export enum ActivityType {
  CALL = 'CALL',
  EMAIL = 'EMAIL',
  MEETING = 'MEETING',
  TRAINING = 'TRAINING',
  PROPOSAL = 'PROPOSAL',
  DEMO = 'DEMO',
  FOLLOW_UP = 'FOLLOW_UP',
  RESEARCH = 'RESEARCH'
}

export enum ActivityStatus {
  PLANNED = 'PLANNED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  RESCHEDULED = 'RESCHEDULED'
}

export enum UserRole {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
  USER = 'USER'
}

export enum TriggerType {
  MANUAL = 'MANUAL',
  SCHEDULED = 'SCHEDULED',
  EVENT = 'EVENT',
  WEBHOOK = 'WEBHOOK'
}

export enum ConditionOperator {
  EQUALS = 'EQUALS',
  NOT_EQUALS = 'NOT_EQUALS',
  GREATER_THAN = 'GREATER_THAN',
  LESS_THAN = 'LESS_THAN',
  CONTAINS = 'CONTAINS',
  NOT_CONTAINS = 'NOT_CONTAINS',
  IN = 'IN',
  NOT_IN = 'NOT_IN'
}

export enum LogicalOperator {
  AND = 'AND',
  OR = 'OR'
}

export enum WorkflowExecutionStatus {
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

// Re-export from agent types
export { Priority, TaskType, AgentType } from './agent';

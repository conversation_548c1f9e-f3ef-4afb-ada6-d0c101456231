@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
    margin: 0;
    padding: 0;
    min-height: 100vh;
  }
  
  #root {
    min-height: 100vh;
  }
  
  * {
    box-sizing: border-box;
  }
  
  /* Custom focus styles */
  *:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }
  
  /* Scrollbar styles */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
  
  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
  }
}

/* Component styles */
@layer components {
  /* Button components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }
  
  .btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }
  
  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .btn-warning {
    @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
  }
  
  .btn-outline {
    @apply bg-transparent border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
  }
  
  /* Card components */
  .card {
    @apply bg-white shadow rounded-lg;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }
  
  /* Form components */
  .form-group {
    @apply mb-4;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
  }
  
  .form-select {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
  }
  
  .form-textarea {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
  }
  
  .form-checkbox {
    @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
  }
  
  .form-radio {
    @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300;
  }
  
  .form-error {
    @apply mt-1 text-sm text-red-600;
  }
  
  .form-help {
    @apply mt-1 text-sm text-gray-500;
  }
  
  /* Badge components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply bg-blue-100 text-blue-800;
  }
  
  .badge-secondary {
    @apply bg-gray-100 text-gray-800;
  }
  
  .badge-success {
    @apply bg-green-100 text-green-800;
  }
  
  .badge-danger {
    @apply bg-red-100 text-red-800;
  }
  
  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  /* Alert components */
  .alert {
    @apply p-4 rounded-md;
  }
  
  .alert-info {
    @apply bg-blue-50 border border-blue-200 text-blue-700;
  }
  
  .alert-success {
    @apply bg-green-50 border border-green-200 text-green-700;
  }
  
  .alert-warning {
    @apply bg-yellow-50 border border-yellow-200 text-yellow-700;
  }
  
  .alert-error {
    @apply bg-red-50 border border-red-200 text-red-700;
  }
  
  /* Loading components */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
  }
  
  .spinner-sm {
    @apply h-4 w-4;
  }
  
  .spinner-md {
    @apply h-6 w-6;
  }
  
  .spinner-lg {
    @apply h-8 w-8;
  }
  
  /* Table components */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }
  
  .table-row {
    @apply hover:bg-gray-50;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  /* Modal components */
  .modal-overlay {
    @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50;
  }
  
  .modal-container {
    @apply relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white;
  }
  
  /* Dropdown components */
  .dropdown {
    @apply relative inline-block text-left;
  }
  
  .dropdown-menu {
    @apply origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50;
  }
  
  .dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900;
  }
}

/* Utility styles */
@layer utilities {
  /* Text utilities */
  .text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }
  
  /* Layout utilities */
  .container-fluid {
    @apply w-full px-4 mx-auto;
  }
  
  .container-sm {
    @apply max-w-screen-sm mx-auto px-4;
  }
  
  .container-md {
    @apply max-w-screen-md mx-auto px-4;
  }
  
  .container-lg {
    @apply max-w-screen-lg mx-auto px-4;
  }
  
  .container-xl {
    @apply max-w-screen-xl mx-auto px-4;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

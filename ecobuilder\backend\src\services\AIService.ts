import OpenAI from 'openai';
import { Logger } from '../utils/logger';
import { Partner, Opportunity } from '../../../shared/types';

interface AIConfig {
  apiKey: string;
  model: string;
  maxTokens: number;
  temperature: number;
  timeout: number;
}

interface AnalysisResult {
  score: number;
  reasoning: string;
  recommendations: string[];
  confidence: number;
}

interface EmailTemplate {
  subject: string;
  body: string;
  tone: string;
}

export class AIService {
  private openai: OpenAI;
  private logger: Logger;
  private config: AIConfig;

  constructor(config: Partial<AIConfig> = {}) {
    this.logger = new Logger('AIService');
    
    this.config = {
      apiKey: process.env.OPENAI_API_KEY || '',
      model: 'gpt-4',
      maxTokens: 2000,
      temperature: 0.7,
      timeout: 30000,
      ...config
    };

    if (!this.config.apiKey) {
      throw new Error('OpenAI API key is required');
    }

    this.openai = new OpenAI({
      apiKey: this.config.apiKey,
      timeout: this.config.timeout
    });
  }

  /**
   * Generate a response using OpenAI
   */
  async generateResponse(prompt: string, options: Partial<AIConfig> = {}): Promise<string> {
    this.logger.info('Generating AI response', { promptLength: prompt.length });

    try {
      const response = await this.openai.chat.completions.create({
        model: options.model || this.config.model,
        messages: [{ role: 'user', content: prompt }],
        max_tokens: options.maxTokens || this.config.maxTokens,
        temperature: options.temperature || this.config.temperature
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response content received from OpenAI');
      }

      this.logger.info('AI response generated successfully', { 
        responseLength: content.length,
        tokensUsed: response.usage?.total_tokens 
      });

      return content;
    } catch (error) {
      this.logger.error('Failed to generate AI response', error);
      throw error;
    }
  }

  /**
   * Analyze partner fit and compatibility
   */
  async analyzePartnerFit(
    partner: Partner, 
    criteria: Record<string, any>
  ): Promise<AnalysisResult> {
    const prompt = `
      Analyze the compatibility between this potential partner and our criteria:

      PARTNER INFORMATION:
      Company: ${partner.companyName}
      Industry: ${partner.industry}
      Size: ${partner.size}
      Revenue: ${partner.revenue ? `$${partner.revenue}` : 'Not specified'}
      Website: ${partner.website || 'Not specified'}
      Description: ${partner.description || 'Not specified'}

      PARTNERSHIP CRITERIA:
      ${JSON.stringify(criteria, null, 2)}

      Please provide a detailed analysis including:
      1. Compatibility score (0-100)
      2. Detailed reasoning for the score
      3. Specific recommendations for partnership approach
      4. Confidence level in the analysis (0-100)

      Format your response as JSON:
      {
        "score": number,
        "reasoning": "detailed explanation",
        "recommendations": ["recommendation1", "recommendation2", ...],
        "confidence": number
      }
    `;

    try {
      const response = await this.generateResponse(prompt);
      const analysis = JSON.parse(response);
      
      // Validate the response structure
      if (typeof analysis.score !== 'number' || 
          typeof analysis.reasoning !== 'string' ||
          !Array.isArray(analysis.recommendations) ||
          typeof analysis.confidence !== 'number') {
        throw new Error('Invalid analysis response format');
      }

      return analysis;
    } catch (error) {
      this.logger.error('Failed to analyze partner fit', error);
      throw error;
    }
  }

  /**
   * Generate personalized email content
   */
  async generateEmail(
    context: {
      recipientName: string;
      recipientCompany: string;
      senderName: string;
      senderCompany: string;
      purpose: string;
      tone: 'formal' | 'casual' | 'friendly';
      additionalContext?: string;
    }
  ): Promise<EmailTemplate> {
    const prompt = `
      Generate a professional email with the following context:

      TO: ${context.recipientName} at ${context.recipientCompany}
      FROM: ${context.senderName} at ${context.senderCompany}
      PURPOSE: ${context.purpose}
      TONE: ${context.tone}
      ${context.additionalContext ? `ADDITIONAL CONTEXT: ${context.additionalContext}` : ''}

      Please generate:
      1. A compelling subject line
      2. A well-structured email body
      3. Appropriate tone and language

      Format your response as JSON:
      {
        "subject": "email subject",
        "body": "email body with proper formatting",
        "tone": "${context.tone}"
      }
    `;

    try {
      const response = await this.generateResponse(prompt);
      const email = JSON.parse(response);
      
      if (!email.subject || !email.body) {
        throw new Error('Invalid email response format');
      }

      return email;
    } catch (error) {
      this.logger.error('Failed to generate email', error);
      throw error;
    }
  }

  /**
   * Score opportunity potential
   */
  async scoreOpportunity(opportunity: Opportunity): Promise<number> {
    const prompt = `
      Analyze this business opportunity and provide a score from 0 to 100:

      OPPORTUNITY DETAILS:
      Title: ${opportunity.title}
      Description: ${opportunity.description || 'Not specified'}
      Value: $${opportunity.value}
      Currency: ${opportunity.currency}
      Stage: ${opportunity.stage}
      Probability: ${opportunity.probability}%
      Expected Close Date: ${opportunity.expectedCloseDate || 'Not specified'}

      Consider factors like:
      - Deal size and value
      - Probability of success
      - Strategic importance
      - Timeline feasibility
      - Market conditions

      Return only a numeric score between 0 and 100.
    `;

    try {
      const response = await this.generateResponse(prompt);
      const score = parseFloat(response.trim());
      
      if (isNaN(score) || score < 0 || score > 100) {
        throw new Error('Invalid opportunity score received');
      }

      return score;
    } catch (error) {
      this.logger.error('Failed to score opportunity', error);
      throw error;
    }
  }

  /**
   * Recommend next actions based on context
   */
  async recommendActions(context: Record<string, any>): Promise<string[]> {
    const prompt = `
      Based on the following context, recommend the next best actions to take:

      CONTEXT:
      ${JSON.stringify(context, null, 2)}

      Please provide 3-5 specific, actionable recommendations that would help advance the partnership or opportunity.
      
      Format your response as a JSON array of strings:
      ["action1", "action2", "action3", ...]
    `;

    try {
      const response = await this.generateResponse(prompt);
      const actions = JSON.parse(response);
      
      if (!Array.isArray(actions)) {
        throw new Error('Invalid actions response format');
      }

      return actions;
    } catch (error) {
      this.logger.error('Failed to recommend actions', error);
      throw error;
    }
  }

  /**
   * Extract key information from text
   */
  async extractInformation(
    text: string, 
    extractionType: 'company_info' | 'contact_info' | 'opportunity_details'
  ): Promise<Record<string, any>> {
    const prompts = {
      company_info: `
        Extract company information from the following text:
        ${text}

        Return a JSON object with fields: name, industry, size, revenue, website, description
      `,
      contact_info: `
        Extract contact information from the following text:
        ${text}

        Return a JSON object with fields: name, email, phone, position, company
      `,
      opportunity_details: `
        Extract opportunity details from the following text:
        ${text}

        Return a JSON object with fields: title, description, value, timeline, requirements
      `
    };

    try {
      const response = await this.generateResponse(prompts[extractionType]);
      return JSON.parse(response);
    } catch (error) {
      this.logger.error('Failed to extract information', error);
      throw error;
    }
  }

  /**
   * Analyze market trends
   */
  async analyzeMarketTrends(
    industry: string, 
    region: string, 
    timeframe: string = '12 months'
  ): Promise<any> {
    const prompt = `
      Analyze market trends for the ${industry} industry in ${region} over the next ${timeframe}.

      Please provide insights on:
      1. Growth opportunities
      2. Key challenges
      3. Emerging technologies
      4. Competitive landscape
      5. Partnership opportunities

      Format your response as JSON with these sections.
    `;

    try {
      const response = await this.generateResponse(prompt);
      return JSON.parse(response);
    } catch (error) {
      this.logger.error('Failed to analyze market trends', error);
      throw error;
    }
  }

  /**
   * Generate training content
   */
  async generateTrainingContent(
    topic: string, 
    audience: string, 
    format: 'presentation' | 'document' | 'quiz'
  ): Promise<any> {
    const prompt = `
      Generate ${format} content for training on: ${topic}
      Target audience: ${audience}

      Please create comprehensive training material that is engaging and educational.
      
      Format your response as JSON with appropriate structure for a ${format}.
    `;

    try {
      const response = await this.generateResponse(prompt);
      return JSON.parse(response);
    } catch (error) {
      this.logger.error('Failed to generate training content', error);
      throw error;
    }
  }

  /**
   * Summarize long text content
   */
  async summarizeText(text: string, maxLength: number = 500): Promise<string> {
    const prompt = `
      Summarize the following text in approximately ${maxLength} characters or less:

      ${text}

      Provide a concise summary that captures the key points and main ideas.
    `;

    try {
      const response = await this.generateResponse(prompt);
      return response.trim();
    } catch (error) {
      this.logger.error('Failed to summarize text', error);
      throw error;
    }
  }

  /**
   * Translate text to different language
   */
  async translateText(text: string, targetLanguage: string): Promise<string> {
    const prompt = `
      Translate the following text to ${targetLanguage}:

      ${text}

      Provide an accurate and natural translation.
    `;

    try {
      const response = await this.generateResponse(prompt);
      return response.trim();
    } catch (error) {
      this.logger.error('Failed to translate text', error);
      throw error;
    }
  }

  /**
   * Validate and clean data
   */
  async validateData(data: Record<string, any>, schema: Record<string, any>): Promise<any> {
    const prompt = `
      Validate and clean the following data according to the schema:

      DATA:
      ${JSON.stringify(data, null, 2)}

      SCHEMA:
      ${JSON.stringify(schema, null, 2)}

      Return cleaned and validated data, or indicate validation errors.
      Format response as JSON with 'valid' boolean and 'data' or 'errors' fields.
    `;

    try {
      const response = await this.generateResponse(prompt);
      return JSON.parse(response);
    } catch (error) {
      this.logger.error('Failed to validate data', error);
      throw error;
    }
  }

  /**
   * Get service health status
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.generateResponse('Hello, please respond with "OK"');
      return response.toLowerCase().includes('ok');
    } catch (error) {
      this.logger.error('AI service health check failed', error);
      return false;
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    // Cleanup any resources if needed
    this.logger.info('AI service cleanup completed');
  }
}

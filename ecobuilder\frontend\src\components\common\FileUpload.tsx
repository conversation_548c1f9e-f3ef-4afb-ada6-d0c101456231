import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  CloudArrowUpIcon,
  DocumentIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  XMarkIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { apiClient } from '../../services/api';
import toast from 'react-hot-toast';

export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadedAt: Date;
}

export interface FileUploadProps {
  onUploadSuccess?: (files: UploadedFile[]) => void;
  onUploadError?: (error: string) => void;
  acceptedFileTypes?: string[];
  maxFileSize?: number; // in bytes
  maxFiles?: number;
  multiple?: boolean;
  className?: string;
}

interface FileUploadState {
  status: 'idle' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
  uploadedFiles: UploadedFile[];
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onUploadSuccess,
  onUploadError,
  acceptedFileTypes = ['.pdf', '.doc', '.docx', '.csv', '.xlsx', '.txt'],
  maxFileSize = 10 * 1024 * 1024, // 10MB
  maxFiles = 5,
  multiple = true,
  className = ''
}) => {
  const [uploadState, setUploadState] = useState<FileUploadState>({
    status: 'idle',
    progress: 0,
    uploadedFiles: []
  });

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    // Validate file count
    if (!multiple && acceptedFiles.length > 1) {
      const error = '只能上传一个文件';
      setUploadState(prev => ({ ...prev, status: 'error', error }));
      onUploadError?.(error);
      toast.error(error);
      return;
    }

    if (acceptedFiles.length > maxFiles) {
      const error = `最多只能上传 ${maxFiles} 个文件`;
      setUploadState(prev => ({ ...prev, status: 'error', error }));
      onUploadError?.(error);
      toast.error(error);
      return;
    }

    // Validate file sizes
    const oversizedFiles = acceptedFiles.filter(file => file.size > maxFileSize);
    if (oversizedFiles.length > 0) {
      const error = `文件大小不能超过 ${formatFileSize(maxFileSize)}`;
      setUploadState(prev => ({ ...prev, status: 'error', error }));
      onUploadError?.(error);
      toast.error(error);
      return;
    }

    // Start upload
    setUploadState(prev => ({ 
      ...prev, 
      status: 'uploading', 
      progress: 0, 
      error: undefined 
    }));

    try {
      const uploadedFiles: UploadedFile[] = [];
      
      for (let i = 0; i < acceptedFiles.length; i++) {
        const file = acceptedFiles[i];
        
        // Update progress
        const baseProgress = (i / acceptedFiles.length) * 100;
        setUploadState(prev => ({ ...prev, progress: baseProgress }));

        // Upload file
        const response = await apiClient.uploadFile(file, (fileProgress) => {
          const totalProgress = baseProgress + (fileProgress / acceptedFiles.length);
          setUploadState(prev => ({ ...prev, progress: totalProgress }));
        });

        if (response.data.success) {
          const uploadedFile: UploadedFile = {
            id: response.data.data.id || `file_${Date.now()}_${i}`,
            name: file.name,
            size: file.size,
            type: file.type,
            url: response.data.data.url,
            uploadedAt: new Date()
          };
          uploadedFiles.push(uploadedFile);
        }
      }

      // Upload completed successfully
      setUploadState(prev => ({
        ...prev,
        status: 'success',
        progress: 100,
        uploadedFiles
      }));

      onUploadSuccess?.(uploadedFiles);
      toast.success(`成功上传 ${uploadedFiles.length} 个文件`);

      // Reset after 3 seconds
      setTimeout(() => {
        setUploadState(prev => ({ ...prev, status: 'idle', progress: 0 }));
      }, 3000);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '上传失败';
      setUploadState(prev => ({
        ...prev,
        status: 'error',
        error: errorMessage
      }));
      onUploadError?.(errorMessage);
      toast.error(errorMessage);
    }
  }, [maxFileSize, maxFiles, multiple, onUploadSuccess, onUploadError]);

  const { getRootProps, getInputProps, isDragActive, fileRejections } = useDropzone({
    onDrop,
    accept: acceptedFileTypes.reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>),
    maxSize: maxFileSize,
    multiple
  });

  const removeFile = (fileId: string) => {
    setUploadState(prev => ({
      ...prev,
      uploadedFiles: prev.uploadedFiles.filter(file => file.id !== fileId)
    }));
  };

  const resetUpload = () => {
    setUploadState({
      status: 'idle',
      progress: 0,
      uploadedFiles: []
    });
  };

  const getStatusIcon = () => {
    switch (uploadState.status) {
      case 'uploading':
        return <ArrowPathIcon className="h-8 w-8 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircleIcon className="h-8 w-8 text-green-500" />;
      case 'error':
        return <ExclamationCircleIcon className="h-8 w-8 text-red-500" />;
      default:
        return <CloudArrowUpIcon className="h-8 w-8 text-gray-400" />;
    }
  };

  const getStatusMessage = () => {
    switch (uploadState.status) {
      case 'uploading':
        return `上传中... ${Math.round(uploadState.progress)}%`;
      case 'success':
        return `成功上传 ${uploadState.uploadedFiles.length} 个文件`;
      case 'error':
        return uploadState.error || '上传失败';
      default:
        return isDragActive ? '释放文件开始上传' : '点击或拖拽文件到此处上传';
    }
  };

  const getStatusColor = () => {
    switch (uploadState.status) {
      case 'uploading':
        return 'border-blue-300 bg-blue-50';
      case 'success':
        return 'border-green-300 bg-green-50';
      case 'error':
        return 'border-red-300 bg-red-50';
      default:
        return isDragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300 bg-gray-50';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${getStatusColor()}`}
      >
        <input {...getInputProps()} />
        
        <div className="space-y-2">
          {getStatusIcon()}
          <p className="text-sm font-medium text-gray-900">
            {getStatusMessage()}
          </p>
          
          {uploadState.status === 'idle' && (
            <p className="text-xs text-gray-500">
              支持格式：{acceptedFileTypes.join(', ')} | 
              最大文件大小：{formatFileSize(maxFileSize)} | 
              最多 {maxFiles} 个文件
            </p>
          )}
        </div>

        {/* Progress Bar */}
        {uploadState.status === 'uploading' && (
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadState.progress}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Reset Button */}
        {(uploadState.status === 'error' || uploadState.status === 'success') && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              resetUpload();
            }}
            className="mt-2 text-xs text-gray-500 hover:text-gray-700 underline"
          >
            重新上传
          </button>
        )}
      </div>

      {/* File Rejections */}
      {fileRejections.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <h4 className="text-sm font-medium text-red-800 mb-2">以下文件无法上传：</h4>
          <ul className="text-xs text-red-700 space-y-1">
            {fileRejections.map(({ file, errors }) => (
              <li key={file.name}>
                <span className="font-medium">{file.name}</span>: {errors[0]?.message}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Uploaded Files List */}
      {uploadState.uploadedFiles.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-4 py-3 border-b border-gray-200">
            <h3 className="text-sm font-medium text-gray-900">已上传文件</h3>
          </div>
          <ul className="divide-y divide-gray-200">
            {uploadState.uploadedFiles.map((file) => (
              <li key={file.id} className="px-4 py-3 flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <DocumentIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{file.name}</p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(file.size)} • {file.uploadedAt.toLocaleString()}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon className="h-4 w-4 text-green-500" />
                  <button
                    onClick={() => removeFile(file.id)}
                    className="text-gray-400 hover:text-red-500"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

// Utility function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

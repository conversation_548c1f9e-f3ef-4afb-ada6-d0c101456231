// API types for EcoBuilder

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: ApiError;
  meta?: ResponseMeta;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  stack?: string;
}

export interface ResponseMeta {
  timestamp: string;
  requestId: string;
  pagination?: PaginationMeta;
  filters?: Record<string, any>;
  sort?: SortMeta;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface SortMeta {
  field: string;
  order: 'asc' | 'desc';
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
  search?: string;
  filters?: Record<string, any>;
  sort?: string;
  order?: 'asc' | 'desc';
}

// Authentication
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: {
    id: string;
    email: string;
    name: string;
    role: string;
  };
  token: string;
  refreshToken: string;
  expiresAt: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  name: string;
}

// Partner API
export interface CreatePartnerRequest {
  companyName: string;
  industry: string;
  size: string;
  revenue?: number;
  website?: string;
  description?: string;
  priority?: string;
  contacts?: CreateContactRequest[];
}

export interface UpdatePartnerRequest {
  companyName?: string;
  industry?: string;
  size?: string;
  revenue?: number;
  website?: string;
  description?: string;
  status?: string;
  score?: number;
  priority?: string;
}

export interface CreateContactRequest {
  name: string;
  email: string;
  phone?: string;
  position: string;
  isPrimary?: boolean;
}

// Opportunity API
export interface CreateOpportunityRequest {
  title: string;
  description?: string;
  value: number;
  currency?: string;
  stage?: string;
  probability?: number;
  expectedCloseDate?: string;
  partnerId: string;
}

export interface UpdateOpportunityRequest {
  title?: string;
  description?: string;
  value?: number;
  currency?: string;
  stage?: string;
  probability?: number;
  expectedCloseDate?: string;
  actualCloseDate?: string;
}

// Activity API
export interface CreateActivityRequest {
  type: string;
  subject: string;
  description?: string;
  priority?: string;
  scheduledAt?: string;
  duration?: number;
  partnerId?: string;
  contactId?: string;
  opportunityId?: string;
}

export interface UpdateActivityRequest {
  subject?: string;
  description?: string;
  status?: string;
  priority?: string;
  scheduledAt?: string;
  completedAt?: string;
  duration?: number;
  outcome?: string;
}

// Task API
export interface CreateTaskRequest {
  title: string;
  description?: string;
  type: string;
  priority?: string;
  data?: Record<string, any>;
  scheduledAt?: string;
  agentId: string;
  assignedToId?: string;
  partnerId?: string;
  opportunityId?: string;
  parentTaskId?: string;
}

export interface UpdateTaskRequest {
  title?: string;
  description?: string;
  status?: string;
  priority?: string;
  data?: Record<string, any>;
  result?: Record<string, any>;
  error?: string;
  progress?: number;
  scheduledAt?: string;
  assignedToId?: string;
}

// Agent API
export interface CreateAgentRequest {
  name: string;
  type: string;
  capabilities: string[];
  config?: Record<string, any>;
}

export interface UpdateAgentRequest {
  name?: string;
  status?: string;
  capabilities?: string[];
  config?: Record<string, any>;
  isActive?: boolean;
}

export interface AgentExecuteTaskRequest {
  taskId: string;
  agentId: string;
  context?: Record<string, any>;
}

// Workflow API
export interface CreateWorkflowRequest {
  name: string;
  description?: string;
  definition: Record<string, any>;
}

export interface UpdateWorkflowRequest {
  name?: string;
  description?: string;
  definition?: Record<string, any>;
  isActive?: boolean;
}

export interface ExecuteWorkflowRequest {
  workflowId: string;
  context?: Record<string, any>;
  triggeredBy?: string;
}

// Analytics API
export interface AnalyticsQuery {
  startDate: string;
  endDate: string;
  metrics: string[];
  groupBy?: string;
  filters?: Record<string, any>;
}

export interface AnalyticsResponse {
  metrics: Record<string, number>;
  trends: TrendData[];
  breakdown: BreakdownData[];
}

export interface TrendData {
  date: string;
  value: number;
  metric: string;
}

export interface BreakdownData {
  category: string;
  value: number;
  percentage: number;
}

// WebSocket Events
export interface WebSocketEvent {
  type: string;
  data: Record<string, any>;
  timestamp: string;
  userId?: string;
  agentId?: string;
}

export interface TaskUpdateEvent {
  taskId: string;
  status: string;
  progress: number;
  result?: Record<string, any>;
  error?: string;
}

export interface AgentStatusEvent {
  agentId: string;
  status: string;
  currentTask?: string;
  lastActiveAt: string;
}

export interface NotificationEvent {
  id: string;
  type: string;
  title: string;
  message: string;
  priority: string;
  userId?: string;
  data?: Record<string, any>;
}

// File Upload
export interface FileUploadResponse {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  uploadedAt: string;
}

// Search
export interface SearchRequest {
  query: string;
  type?: string[];
  filters?: Record<string, any>;
  limit?: number;
}

export interface SearchResult {
  id: string;
  type: string;
  title: string;
  description?: string;
  score: number;
  data: Record<string, any>;
}

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  took: number;
  suggestions?: string[];
}

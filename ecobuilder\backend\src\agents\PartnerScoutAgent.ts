import { BaseAgent } from './BaseAgent';
import { 
  AgentType, 
  Task, 
  TaskResult, 
  TaskType,
  Message, 
  AgentResponse,
  Partner,
  PartnerStatus,
  CompanySize,
  Priority
} from '../../../shared/types';
import { AIService } from '../services/AIService';
import { DatabaseService } from '../services/DatabaseService';
import { ExternalAPIService } from '../services/ExternalAPIService';

export class PartnerScoutAgent extends BaseAgent {
  private aiService: AIService;
  private dbService: DatabaseService;
  private apiService: ExternalAPIService;

  constructor(
    id: string,
    name: string,
    capabilities: string[] = [
      'partner_discovery',
      'company_research',
      'partner_scoring',
      'market_analysis',
      'data_enrichment'
    ],
    config: Record<string, any> = {}
  ) {
    super(id, name, AgentType.PARTNER_SCOUT, undefined, capabilities, config);
    
    this.aiService = new AIService();
    this.dbService = new DatabaseService();
    this.apiService = new ExternalAPIService();
  }

  protected async executeTask(task: Task): Promise<TaskResult> {
    switch (task.type) {
      case TaskType.FIND_PARTNERS:
        return await this.findPartners(task);
      case TaskType.SCORE_PARTNER:
        return await this.scorePartner(task);
      case TaskType.MARKET_ANALYSIS:
        return await this.performMarketAnalysis(task);
      default:
        throw new Error(`Unsupported task type: ${task.type}`);
    }
  }

  protected async processMessage(message: Message): Promise<AgentResponse> {
    const { content, metadata } = message;
    
    try {
      // Parse the message content
      const request = JSON.parse(content);
      
      switch (request.action) {
        case 'search_partners':
          return await this.handlePartnerSearchRequest(request);
        case 'analyze_partner':
          return await this.handlePartnerAnalysisRequest(request);
        case 'get_recommendations':
          return await this.handleRecommendationRequest(request);
        default:
          return {
            success: false,
            message: `Unknown action: ${request.action}`
          };
      }
    } catch (error) {
      return {
        success: false,
        message: `Failed to process message: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private async findPartners(task: Task): Promise<TaskResult> {
    const { criteria, limit = 50 } = task.data || {};
    
    this.logger.info('Finding partners', { criteria, limit });
    
    try {
      // Step 1: Search external databases
      const externalResults = await this.searchExternalDatabases(criteria);
      
      // Step 2: Enrich company data
      const enrichedPartners = await this.enrichPartnerData(externalResults);
      
      // Step 3: Score and rank partners
      const scoredPartners = await this.scorePartners(enrichedPartners, criteria);
      
      // Step 4: Filter and limit results
      const filteredPartners = scoredPartners
        .filter(partner => partner.score && partner.score >= (criteria.minScore || 0.6))
        .slice(0, limit);
      
      // Step 5: Save to database
      const savedPartners = await this.savePartners(filteredPartners);
      
      return {
        success: true,
        data: {
          partners: savedPartners,
          totalFound: externalResults.length,
          totalScored: scoredPartners.length,
          totalSaved: savedPartners.length
        },
        metadata: {
          criteria,
          searchTimestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error('Failed to find partners', error);
      throw error;
    }
  }

  private async scorePartner(task: Task): Promise<TaskResult> {
    const { partnerId, criteria } = task.data || {};
    
    if (!partnerId) {
      throw new Error('Partner ID is required for scoring');
    }
    
    try {
      // Get partner data
      const partner = await this.dbService.getPartner(partnerId);
      if (!partner) {
        throw new Error(`Partner not found: ${partnerId}`);
      }
      
      // Calculate score using AI
      const score = await this.calculatePartnerScore(partner, criteria);
      
      // Update partner with new score
      await this.dbService.updatePartner(partnerId, { score });
      
      return {
        success: true,
        data: {
          partnerId,
          score,
          criteria
        }
      };
    } catch (error) {
      this.logger.error('Failed to score partner', error);
      throw error;
    }
  }

  private async performMarketAnalysis(task: Task): Promise<TaskResult> {
    const { industry, region, targetMarket } = task.data || {};
    
    try {
      // Analyze market trends
      const marketTrends = await this.analyzeMarketTrends(industry, region);
      
      // Identify key players
      const keyPlayers = await this.identifyKeyPlayers(industry, region);
      
      // Generate insights
      const insights = await this.generateMarketInsights(marketTrends, keyPlayers, targetMarket);
      
      return {
        success: true,
        data: {
          marketTrends,
          keyPlayers,
          insights,
          analysisDate: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error('Failed to perform market analysis', error);
      throw error;
    }
  }

  private async searchExternalDatabases(criteria: any): Promise<any[]> {
    const results: any[] = [];
    
    // Search multiple data sources
    const sources = ['crunchbase', 'linkedin', 'clearbit', 'zoominfo'];
    
    for (const source of sources) {
      try {
        const sourceResults = await this.apiService.searchCompanies(source, criteria);
        results.push(...sourceResults);
      } catch (error) {
        this.logger.warn(`Failed to search ${source}`, error);
      }
    }
    
    // Remove duplicates based on company name and domain
    return this.deduplicateResults(results);
  }

  private async enrichPartnerData(partners: any[]): Promise<any[]> {
    const enrichedPartners = [];
    
    for (const partner of partners) {
      try {
        const enrichedData = await this.apiService.enrichCompanyData(partner);
        enrichedPartners.push({ ...partner, ...enrichedData });
      } catch (error) {
        this.logger.warn(`Failed to enrich data for ${partner.name}`, error);
        enrichedPartners.push(partner);
      }
    }
    
    return enrichedPartners;
  }

  private async scorePartners(partners: any[], criteria: any): Promise<any[]> {
    const scoredPartners = [];
    
    for (const partner of partners) {
      try {
        const score = await this.calculatePartnerScore(partner, criteria);
        scoredPartners.push({ ...partner, score });
      } catch (error) {
        this.logger.warn(`Failed to score partner ${partner.name}`, error);
        scoredPartners.push({ ...partner, score: 0 });
      }
    }
    
    return scoredPartners.sort((a, b) => (b.score || 0) - (a.score || 0));
  }

  private async calculatePartnerScore(partner: any, criteria: any): Promise<number> {
    const prompt = `
      Analyze this potential partner and provide a compatibility score (0-1):
      
      Partner: ${JSON.stringify(partner, null, 2)}
      Criteria: ${JSON.stringify(criteria, null, 2)}
      
      Consider factors like:
      - Industry alignment
      - Company size compatibility
      - Geographic presence
      - Technology stack
      - Market position
      - Financial stability
      - Growth potential
      
      Return only a numeric score between 0 and 1.
    `;
    
    const response = await this.aiService.generateResponse(prompt);
    const score = parseFloat(response.trim());
    
    return isNaN(score) ? 0 : Math.max(0, Math.min(1, score));
  }

  private async savePartners(partners: any[]): Promise<Partner[]> {
    const savedPartners: Partner[] = [];
    
    for (const partnerData of partners) {
      try {
        const partner = await this.dbService.createPartner({
          companyName: partnerData.name || partnerData.companyName,
          industry: partnerData.industry || 'Unknown',
          size: this.mapCompanySize(partnerData.size),
          revenue: partnerData.revenue,
          website: partnerData.website,
          description: partnerData.description,
          status: PartnerStatus.PROSPECT,
          score: partnerData.score,
          priority: this.mapPriority(partnerData.score),
          createdById: 'system' // TODO: Get from context
        });
        
        savedPartners.push(partner);
      } catch (error) {
        this.logger.warn(`Failed to save partner ${partnerData.name}`, error);
      }
    }
    
    return savedPartners;
  }

  private mapCompanySize(size: string): CompanySize {
    const sizeMap: Record<string, CompanySize> = {
      'startup': CompanySize.STARTUP,
      'small': CompanySize.SMALL,
      'medium': CompanySize.MEDIUM,
      'large': CompanySize.LARGE,
      'enterprise': CompanySize.ENTERPRISE
    };
    
    return sizeMap[size?.toLowerCase()] || CompanySize.MEDIUM;
  }

  private mapPriority(score?: number): Priority {
    if (!score) return Priority.LOW;
    if (score >= 0.8) return Priority.URGENT;
    if (score >= 0.6) return Priority.HIGH;
    if (score >= 0.4) return Priority.MEDIUM;
    return Priority.LOW;
  }

  private deduplicateResults(results: any[]): any[] {
    const seen = new Set();
    return results.filter(result => {
      const key = `${result.name?.toLowerCase()}-${result.domain?.toLowerCase()}`;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  // Abstract method implementations
  protected getSupportedTaskTypes(): string[] {
    return [TaskType.FIND_PARTNERS, TaskType.SCORE_PARTNER, TaskType.MARKET_ANALYSIS];
  }

  protected getRequiredCapabilities(task: Task): string[] {
    switch (task.type) {
      case TaskType.FIND_PARTNERS:
        return ['partner_discovery', 'company_research'];
      case TaskType.SCORE_PARTNER:
        return ['partner_scoring'];
      case TaskType.MARKET_ANALYSIS:
        return ['market_analysis'];
      default:
        return [];
    }
  }

  protected async loadTools(): Promise<void> {
    // Load external API tools
    this.addTool({
      id: 'crunchbase-api',
      name: 'Crunchbase API',
      description: 'Company and funding data',
      type: 'API' as any,
      config: { apiKey: this.config.crunchbaseApiKey },
      isEnabled: !!this.config.crunchbaseApiKey
    });
  }

  protected async validateConfiguration(): Promise<void> {
    // Validate required configuration
    if (!this.config.openaiApiKey) {
      throw new Error('OpenAI API key is required');
    }
  }

  protected async setupEventHandlers(): Promise<void> {
    // Setup event handlers for partner discovery
    this.on('partnerFound', this.handlePartnerFound.bind(this));
    this.on('partnerScored', this.handlePartnerScored.bind(this));
  }

  protected canRecover(error: Error): boolean {
    // Can recover from API rate limits and temporary network issues
    return error.message.includes('rate limit') || error.message.includes('timeout');
  }

  protected async recover(error: Error, task?: Task): Promise<void> {
    if (error.message.includes('rate limit')) {
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, 60000));
      this.status = AgentStatus.IDLE;
    }
  }

  protected async cleanup(): Promise<void> {
    // Cleanup resources
    await this.aiService.cleanup?.();
    await this.dbService.cleanup?.();
    await this.apiService.cleanup?.();
  }

  private async handlePartnerFound(event: any): Promise<void> {
    this.logger.info('Partner found', event);
  }

  private async handlePartnerScored(event: any): Promise<void> {
    this.logger.info('Partner scored', event);
  }

  private async handlePartnerSearchRequest(request: any): Promise<AgentResponse> {
    // Implementation for handling partner search requests
    return { success: true, message: 'Partner search completed' };
  }

  private async handlePartnerAnalysisRequest(request: any): Promise<AgentResponse> {
    // Implementation for handling partner analysis requests
    return { success: true, message: 'Partner analysis completed' };
  }

  private async handleRecommendationRequest(request: any): Promise<AgentResponse> {
    // Implementation for handling recommendation requests
    return { success: true, message: 'Recommendations generated' };
  }

  private async analyzeMarketTrends(industry: string, region: string): Promise<any> {
    // Implementation for market trend analysis
    return {};
  }

  private async identifyKeyPlayers(industry: string, region: string): Promise<any[]> {
    // Implementation for identifying key players
    return [];
  }

  private async generateMarketInsights(trends: any, players: any[], targetMarket: any): Promise<any> {
    // Implementation for generating market insights
    return {};
  }
}

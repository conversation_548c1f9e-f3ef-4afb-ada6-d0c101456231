<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcoBuilder - 多智能体生态合作伙伴开发系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #667eea;
        }

        main {
            margin-top: 80px;
            padding: 2rem 0;
        }

        .hero {
            text-align: center;
            padding: 3rem 0;
            color: white;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .status-bar {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 10px;
            margin: 2rem 0;
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: white;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #22c55e;
            animation: pulse 2s infinite;
        }

        .agents-dashboard {
            background: white;
            padding: 3rem 0;
            margin: 2rem 0;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .agents-dashboard h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #333;
        }

        .agents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .agent-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            padding: 2rem;
            border-radius: 15px;
            color: white;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .agent-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .agent-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.5s;
            opacity: 0;
        }

        .agent-card:hover::before {
            animation: shine 0.5s ease-in-out;
        }

        .agent-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .agent-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .agent-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .agent-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .agent-type {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 1rem;
        }

        .agent-description {
            font-size: 0.95rem;
            margin-bottom: 1.5rem;
            opacity: 0.9;
        }

        .agent-metrics {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .metric-label {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .agent-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s;
            flex: 1;
        }

        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .workflow-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 3rem 0;
            margin: 2rem 0;
            border-radius: 20px;
            color: white;
        }

        .workflow-section h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
        }

        .workflow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .workflow-step {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
            flex: 1;
            min-width: 200px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .workflow-step i {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #ffd700;
        }

        .workflow-arrow {
            font-size: 1.5rem;
            color: #ffd700;
        }

        .stats-section {
            background: white;
            padding: 3rem 0;
            margin: 2rem 0;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .stat-card {
            text-align: center;
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes shine {
            0% { opacity: 0; transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { opacity: 1; }
            100% { opacity: 0; transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hero h1 {
                font-size: 2rem;
            }
            
            .workflow-steps {
                flex-direction: column;
            }
            
            .workflow-arrow {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <div class="logo">
                <i class="fas fa-network-wired"></i>
                EcoBuilder
            </div>
            <ul class="nav-links">
                <li><a href="#dashboard">仪表板</a></li>
                <li><a href="#agents">智能体</a></li>
                <li><a href="#workflow">工作流</a></li>
                <li><a href="#analytics">分析</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section class="hero" id="dashboard">
            <div class="container">
                <h1>EcoBuilder 多智能体系统</h1>
                <p>AI驱动的生态合作伙伴开发自动化平台</p>
                
                <div class="status-bar">
                    <div class="status-item">
                        <div class="status-dot"></div>
                        <span>系统运行中</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-robot"></i>
                        <span>5个智能体在线</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-tasks"></i>
                        <span>12个任务执行中</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-handshake"></i>
                        <span>8个合作伙伴活跃</span>
                    </div>
                </div>
            </div>
        </section>

        <section class="agents-dashboard" id="agents">
            <div class="container">
                <h2>智能体控制中心</h2>
                <div class="agents-grid">
                    <div class="agent-card" onclick="openAgentModal('partner-scout')">
                        <div class="agent-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="agent-status">
                            <div class="status-dot"></div>
                            <span>工作中</span>
                        </div>
                        <div class="agent-name">PartnerScout</div>
                        <div class="agent-type">合作伙伴发现智能体</div>
                        <div class="agent-description">
                            智能分析市场机会，从现有资源、客户推荐、销售关系中发现和筛选潜在合作伙伴
                        </div>
                        <div class="agent-metrics">
                            <div class="metric">
                                <div class="metric-value">23</div>
                                <div class="metric-label">发现伙伴</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">8.7</div>
                                <div class="metric-label">平均评分</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">92%</div>
                                <div class="metric-label">匹配准确率</div>
                            </div>
                        </div>
                        <div class="agent-actions">
                            <button class="btn btn-primary">查看详情</button>
                            <button class="btn btn-primary">分配任务</button>
                        </div>
                    </div>

                    <div class="agent-card" onclick="openAgentModal('partner-recruiter')">
                        <div class="agent-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="agent-status">
                            <div class="status-dot"></div>
                            <span>工作中</span>
                        </div>
                        <div class="agent-name">PartnerRecruiter</div>
                        <div class="agent-type">合作伙伴招募智能体</div>
                        <div class="agent-description">
                            安排与合作伙伴CEO的交流会议，进行协议谈判，达成合作协议
                        </div>
                        <div class="agent-metrics">
                            <div class="metric">
                                <div class="metric-value">15</div>
                                <div class="metric-label">会议安排</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">12</div>
                                <div class="metric-label">协议签署</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">80%</div>
                                <div class="metric-label">成功率</div>
                            </div>
                        </div>
                        <div class="agent-actions">
                            <button class="btn btn-primary">查看详情</button>
                            <button class="btn btn-primary">分配任务</button>
                        </div>
                    </div>

                    <div class="agent-card" onclick="openAgentModal('partner-trainer')">
                        <div class="agent-icon">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <div class="agent-status">
                            <div class="status-dot"></div>
                            <span>工作中</span>
                        </div>
                        <div class="agent-name">PartnerTrainer</div>
                        <div class="agent-type">合作伙伴培训智能体</div>
                        <div class="agent-description">
                            对接合作伙伴售前和销售团队，安排产品销售培训，赋能销售团队
                        </div>
                        <div class="agent-metrics">
                            <div class="metric">
                                <div class="metric-value">45</div>
                                <div class="metric-label">培训人员</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">18</div>
                                <div class="metric-label">培训课程</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">95%</div>
                                <div class="metric-label">通过率</div>
                            </div>
                        </div>
                        <div class="agent-actions">
                            <button class="btn btn-primary">查看详情</button>
                            <button class="btn btn-primary">分配任务</button>
                        </div>
                    </div>

                    <div class="agent-card" onclick="openAgentModal('opportunity-manager')">
                        <div class="agent-icon">
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <div class="agent-status">
                            <div class="status-dot"></div>
                            <span>工作中</span>
                        </div>
                        <div class="agent-name">OpportunityManager</div>
                        <div class="agent-type">商机管理智能体</div>
                        <div class="agent-description">
                            对接销售团队讨论潜在客户机会，确认商机并制定销售计划
                        </div>
                        <div class="agent-metrics">
                            <div class="metric">
                                <div class="metric-value">67</div>
                                <div class="metric-label">商机跟踪</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">28</div>
                                <div class="metric-label">成功案例</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">42%</div>
                                <div class="metric-label">转化率</div>
                            </div>
                        </div>
                        <div class="agent-actions">
                            <button class="btn btn-primary">查看详情</button>
                            <button class="btn btn-primary">分配任务</button>
                        </div>
                    </div>

                    <div class="agent-card" onclick="openAgentModal('market-expander')">
                        <div class="agent-icon">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </div>
                        <div class="agent-status">
                            <div class="status-dot"></div>
                            <span>工作中</span>
                        </div>
                        <div class="agent-name">MarketExpander</div>
                        <div class="agent-type">市场拓展智能体</div>
                        <div class="agent-description">
                            对接合作伙伴市场部，安排联合商机复制活动，一起拓展市场
                        </div>
                        <div class="agent-metrics">
                            <div class="metric">
                                <div class="metric-value">12</div>
                                <div class="metric-label">联合活动</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">156</div>
                                <div class="metric-label">新商机</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">35%</div>
                                <div class="metric-label">增长率</div>
                            </div>
                        </div>
                        <div class="agent-actions">
                            <button class="btn btn-primary">查看详情</button>
                            <button class="btn btn-primary">分配任务</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="workflow-section" id="workflow">
            <div class="container">
                <h2>生态合作伙伴开发工作流</h2>
                <div class="workflow-steps">
                    <div class="workflow-step">
                        <i class="fas fa-search"></i>
                        <h3>第一步</h3>
                        <p>选择合作伙伴</p>
                        <small>从现有资源、客户推荐、销售关系中找到合适线索</small>
                    </div>
                    <div class="workflow-arrow">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                    <div class="workflow-step">
                        <i class="fas fa-handshake"></i>
                        <h3>第二步</h3>
                        <p>招募合作伙伴</p>
                        <small>安排CEO交流，达成合作协议</small>
                    </div>
                    <div class="workflow-arrow">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                    <div class="workflow-step">
                        <i class="fas fa-chalkboard-teacher"></i>
                        <h3>第三步</h3>
                        <p>培训销售团队</p>
                        <small>产品销售培训，赋能销售能力</small>
                    </div>
                    <div class="workflow-arrow">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                    <div class="workflow-step">
                        <i class="fas fa-bullseye"></i>
                        <h3>第四步</h3>
                        <p>商机管理</p>
                        <small>讨论潜在客户，确认商机和销售计划</small>
                    </div>
                    <div class="workflow-arrow">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                    <div class="workflow-step">
                        <i class="fas fa-expand-arrows-alt"></i>
                        <h3>第五步</h3>
                        <p>市场拓展</p>
                        <small>联合商机复制活动，拓展市场</small>
                    </div>
                </div>
            </div>
        </section>

        <section class="stats-section">
            <div class="container">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">156</div>
                        <div class="stat-label">合作伙伴</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">89%</div>
                        <div class="stat-label">自动化率</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">2.3M</div>
                        <div class="stat-label">商机价值</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">45%</div>
                        <div class="stat-label">效率提升</div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Agent Detail Modal -->
    <div class="modal" id="agentModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">智能体详情</h2>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div id="modalBody">
                <!-- Dynamic content will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        function openAgentModal(agentId) {
            const modal = document.getElementById('agentModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalBody = document.getElementById('modalBody');
            
            const agentData = {
                'partner-scout': {
                    title: 'PartnerScout - 合作伙伴发现智能体',
                    content: `
                        <h3>核心功能</h3>
                        <ul>
                            <li>🔍 智能分析现有客户和供应商关系</li>
                            <li>🎯 基于公司生态要求匹配潜在伙伴</li>
                            <li>📊 AI评分系统对伙伴进行优先级排序</li>
                            <li>🔗 自动集成CRM系统录入伙伴信息</li>
                        </ul>
                        <h3>集成工具</h3>
                        <ul>
                            <li>CRM系统 (Salesforce/HubSpot)</li>
                            <li>企业信息查询 (企查查/天眼查)</li>
                            <li>社交网络分析工具</li>
                            <li>行业数据库接口</li>
                        </ul>
                        <h3>当前状态</h3>
                        <p>✅ 正在分析23个潜在合作伙伴<br>
                        ⏳ 预计完成时间: 2小时<br>
                        📈 本月发现合作伙伴: 156个</p>
                    `
                },
                'partner-recruiter': {
                    title: 'PartnerRecruiter - 合作伙伴招募智能体',
                    content: `
                        <h3>核心功能</h3>
                        <ul>
                            <li>📧 智能生成个性化CEO沟通方案</li>
                            <li>📅 自动化日程协调和会议安排</li>
                            <li>📄 基于模板生成定制化合作协议</li>
                            <li>🤝 提供谈判策略和关键点分析</li>
                        </ul>
                        <h3>集成工具</h3>
                        <ul>
                            <li>邮件自动化 (SendGrid/Mailgun)</li>
                            <li>日历集成 (Google Calendar/Outlook)</li>
                            <li>合同管理系统</li>
                            <li>电子签名平台 (DocuSign)</li>
                        </ul>
                        <h3>当前状态</h3>
                        <p>✅ 已安排15场CEO会议<br>
                        ⏳ 待签署协议: 8份<br>
                        📈 本月成功签约: 12个合作伙伴</p>
                    `
                },
                'partner-trainer': {
                    title: 'PartnerTrainer - 合作伙伴培训智能体',
                    content: `
                        <h3>核心功能</h3>
                        <ul>
                            <li>📚 自动创建个性化培训材料</li>
                            <li>📝 评估销售团队产品知识水平</li>
                            <li>📋 制定阶段性培训计划</li>
                            <li>🏆 销售能力认证和跟踪</li>
                        </ul>
                        <h3>集成工具</h3>
                        <ul>
                            <li>学习管理系统 (LMS)</li>
                            <li>视频会议平台 (Zoom/Teams)</li>
                            <li>培训内容管理系统</li>
                            <li>考试评估平台</li>
                        </ul>
                        <h3>当前状态</h3>
                        <p>✅ 培训中销售人员: 45人<br>
                        ⏳ 进行中课程: 18个<br>
                        📈 本月认证通过率: 95%</p>
                    `
                },
                'opportunity-manager': {
                    title: 'OpportunityManager - 商机管理智能体',
                    content: `
                        <h3>核心功能</h3>
                        <ul>
                            <li>🎯 分析和识别潜在销售机会</li>
                            <li>📋 制定详细的销售推进计划</li>
                            <li>📊 实时跟踪销售进展</li>
                            <li>🏆 记录和分析成功案例</li>
                        </ul>
                        <h3>集成工具</h3>
                        <ul>
                            <li>销售管道管理系统</li>
                            <li>客户数据平台 (CDP)</li>
                            <li>报价系统</li>
                            <li>项目管理工具</li>
                        </ul>
                        <h3>当前状态</h3>
                        <p>✅ 跟踪商机: 67个<br>
                        ⏳ 进行中项目: 23个<br>
                        📈 本月成功案例: 28个</p>
                    `
                },
                'market-expander': {
                    title: 'MarketExpander - 市场拓展智能体',
                    content: `
                        <h3>核心功能</h3>
                        <ul>
                            <li>🎪 设计联合营销活动</li>
                            <li>🔄 基于成功案例复制商机</li>
                            <li>📈 分析市场趋势和机会</li>
                            <li>💰 跟踪营销活动ROI效果</li>
                        </ul>
                        <h3>集成工具</h3>
                        <ul>
                            <li>营销自动化平台 (HubSpot/Marketo)</li>
                            <li>活动管理系统</li>
                            <li>数据分析平台</li>
                            <li>社交媒体管理工具</li>
                        </ul>
                        <h3>当前状态</h3>
                        <p>✅ 联合活动: 12个<br>
                        ⏳ 新发现商机: 156个<br>
                        📈 本月市场增长率: 35%</p>
                    `
                }
            };
            
            const agent = agentData[agentId];
            modalTitle.textContent = agent.title;
            modalBody.innerHTML = agent.content;
            modal.style.display = 'flex';
        }
        
        function closeModal() {
            document.getElementById('agentModal').style.display = 'none';
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('agentModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
        
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
        
        // Simulate real-time updates
        setInterval(() => {
            const statusDots = document.querySelectorAll('.status-dot');
            statusDots.forEach(dot => {
                dot.style.animation = 'none';
                setTimeout(() => {
                    dot.style.animation = 'pulse 2s infinite';
                }, 10);
            });
        }, 5000);
    </script>
</body>
</html>

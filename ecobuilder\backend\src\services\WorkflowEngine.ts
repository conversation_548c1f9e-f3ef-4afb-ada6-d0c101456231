import { EventEmitter } from 'events';
import { 
  Workflow, 
  WorkflowExecution, 
  WorkflowExecutionStatus,
  WorkflowDefinition,
  WorkflowStage,
  WorkflowCondition,
  Task,
  TaskStatus,
  Agent,
  AgentType
} from '../../../shared/types';
import { Logger } from '../utils/logger';
import { DatabaseService } from './DatabaseService';
import { TaskScheduler } from './TaskScheduler';
import { MessageBus } from './MessageBus';
import { BaseAgent } from '../agents/BaseAgent';

export class WorkflowEngine extends EventEmitter {
  private logger: Logger;
  private dbService: DatabaseService;
  private taskScheduler: TaskScheduler;
  private messageBus: MessageBus;
  private agents: Map<string, BaseAgent> = new Map();
  private activeExecutions: Map<string, WorkflowExecution> = new Map();

  constructor() {
    super();
    this.logger = new Logger('WorkflowEngine');
    this.dbService = new DatabaseService();
    this.taskScheduler = new TaskScheduler();
    this.messageBus = new MessageBus();
    
    this.setupEventHandlers();
  }

  /**
   * Initialize the workflow engine
   */
  async initialize(): Promise<void> {
    this.logger.info('Initializing workflow engine');
    
    try {
      await this.dbService.initialize();
      await this.taskScheduler.initialize();
      await this.messageBus.initialize();
      
      // Load and initialize agents
      await this.loadAgents();
      
      // Resume any interrupted executions
      await this.resumeInterruptedExecutions();
      
      this.logger.info('Workflow engine initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize workflow engine', error);
      throw error;
    }
  }

  /**
   * Execute a workflow
   */
  async executeWorkflow(workflowId: string, context: Record<string, any> = {}): Promise<string> {
    this.logger.info('Starting workflow execution', { workflowId, context });
    
    try {
      // Get workflow definition
      const workflow = await this.dbService.getWorkflow(workflowId);
      if (!workflow) {
        throw new Error(`Workflow not found: ${workflowId}`);
      }

      if (!workflow.isActive) {
        throw new Error(`Workflow is not active: ${workflowId}`);
      }

      // Create execution record
      const execution = await this.dbService.createWorkflowExecution({
        workflowId,
        status: WorkflowExecutionStatus.RUNNING,
        context,
        startedAt: new Date()
      });

      this.activeExecutions.set(execution.id, execution);
      
      // Start execution
      this.executeWorkflowAsync(workflow, execution, context);
      
      return execution.id;
    } catch (error) {
      this.logger.error('Failed to start workflow execution', error);
      throw error;
    }
  }

  /**
   * Execute workflow asynchronously
   */
  private async executeWorkflowAsync(
    workflow: Workflow, 
    execution: WorkflowExecution, 
    context: Record<string, any>
  ): Promise<void> {
    try {
      this.emit('workflowStarted', { workflowId: workflow.id, executionId: execution.id });
      
      // Execute workflow stages
      const result = await this.executeWorkflowStages(workflow.definition, context);
      
      // Update execution as completed
      await this.completeExecution(execution.id, result);
      
      this.emit('workflowCompleted', { 
        workflowId: workflow.id, 
        executionId: execution.id, 
        result 
      });
      
    } catch (error) {
      this.logger.error('Workflow execution failed', { 
        workflowId: workflow.id, 
        executionId: execution.id, 
        error 
      });
      
      await this.failExecution(execution.id, error);
      
      this.emit('workflowFailed', { 
        workflowId: workflow.id, 
        executionId: execution.id, 
        error 
      });
    } finally {
      this.activeExecutions.delete(execution.id);
    }
  }

  /**
   * Execute workflow stages sequentially
   */
  private async executeWorkflowStages(
    definition: WorkflowDefinition, 
    context: Record<string, any>
  ): Promise<Record<string, any>> {
    const results: Record<string, any> = {};
    let currentStages = definition.stages.filter(stage => !stage.conditions || 
      this.evaluateConditions(stage.conditions, context));

    while (currentStages.length > 0) {
      // Execute current stages in parallel
      const stagePromises = currentStages.map(stage => 
        this.executeStage(stage, { ...context, ...results })
      );
      
      const stageResults = await Promise.allSettled(stagePromises);
      
      // Process stage results
      for (let i = 0; i < currentStages.length; i++) {
        const stage = currentStages[i];
        const result = stageResults[i];
        
        if (result.status === 'fulfilled') {
          results[stage.id] = result.value;
          this.logger.info('Stage completed', { stageId: stage.id });
        } else {
          this.logger.error('Stage failed', { stageId: stage.id, error: result.reason });
          throw new Error(`Stage ${stage.id} failed: ${result.reason}`);
        }
      }
      
      // Determine next stages
      currentStages = this.getNextStages(currentStages, definition.stages, { ...context, ...results });
    }
    
    return results;
  }

  /**
   * Execute a single workflow stage
   */
  private async executeStage(stage: WorkflowStage, context: Record<string, any>): Promise<any> {
    this.logger.info('Executing stage', { stageId: stage.id, agentType: stage.agentType });
    
    // Get agent for this stage
    const agent = this.getAgentByType(stage.agentType);
    if (!agent) {
      throw new Error(`No agent available for type: ${stage.agentType}`);
    }

    // Execute stage tasks
    const taskResults = [];
    for (const taskDef of stage.tasks) {
      const task = await this.createTaskFromDefinition(taskDef, context, agent.id);
      const result = await agent.execute(task);
      taskResults.push(result);
      
      if (!result.success) {
        throw new Error(`Task failed: ${result.error}`);
      }
    }
    
    return {
      stageId: stage.id,
      agentId: agent.id,
      taskResults,
      completedAt: new Date().toISOString()
    };
  }

  /**
   * Create a task from workflow task definition
   */
  private async createTaskFromDefinition(
    taskDef: any, 
    context: Record<string, any>, 
    agentId: string
  ): Promise<Task> {
    const task = await this.dbService.createTask({
      title: `Workflow Task: ${taskDef.type}`,
      type: taskDef.type,
      priority: taskDef.priority,
      data: { ...taskDef.config, context },
      agentId,
      maxRetries: taskDef.retries || 3
    });
    
    return task;
  }

  /**
   * Evaluate workflow conditions
   */
  private evaluateConditions(conditions: WorkflowCondition[], context: Record<string, any>): boolean {
    return conditions.every(condition => {
      const value = this.getValueFromContext(condition.field, context);
      return this.evaluateCondition(condition, value);
    });
  }

  /**
   * Evaluate a single condition
   */
  private evaluateCondition(condition: WorkflowCondition, value: any): boolean {
    switch (condition.operator) {
      case 'EQUALS':
        return value === condition.value;
      case 'NOT_EQUALS':
        return value !== condition.value;
      case 'GREATER_THAN':
        return value > condition.value;
      case 'LESS_THAN':
        return value < condition.value;
      case 'CONTAINS':
        return String(value).includes(String(condition.value));
      case 'NOT_CONTAINS':
        return !String(value).includes(String(condition.value));
      case 'IN':
        return Array.isArray(condition.value) && condition.value.includes(value);
      case 'NOT_IN':
        return Array.isArray(condition.value) && !condition.value.includes(value);
      default:
        return false;
    }
  }

  /**
   * Get value from context using dot notation
   */
  private getValueFromContext(field: string, context: Record<string, any>): any {
    return field.split('.').reduce((obj, key) => obj?.[key], context);
  }

  /**
   * Get next stages to execute
   */
  private getNextStages(
    completedStages: WorkflowStage[], 
    allStages: WorkflowStage[], 
    context: Record<string, any>
  ): WorkflowStage[] {
    const completedStageIds = new Set(completedStages.map(s => s.id));
    const nextStageIds = new Set<string>();
    
    // Collect next stage IDs from completed stages
    completedStages.forEach(stage => {
      stage.nextStages.forEach(nextStageId => {
        nextStageIds.add(nextStageId);
      });
    });
    
    // Filter stages that haven't been completed and meet conditions
    return allStages.filter(stage => 
      nextStageIds.has(stage.id) && 
      !completedStageIds.has(stage.id) &&
      (!stage.conditions || this.evaluateConditions(stage.conditions, context))
    );
  }

  /**
   * Get agent by type
   */
  private getAgentByType(agentType: AgentType): BaseAgent | undefined {
    for (const agent of this.agents.values()) {
      if (agent.type === agentType && agent.getStatus() === 'IDLE') {
        return agent;
      }
    }
    return undefined;
  }

  /**
   * Complete workflow execution
   */
  private async completeExecution(executionId: string, result: Record<string, any>): Promise<void> {
    await this.dbService.updateWorkflowExecution(executionId, {
      status: WorkflowExecutionStatus.COMPLETED,
      result,
      completedAt: new Date()
    });
  }

  /**
   * Fail workflow execution
   */
  private async failExecution(executionId: string, error: any): Promise<void> {
    await this.dbService.updateWorkflowExecution(executionId, {
      status: WorkflowExecutionStatus.FAILED,
      error: error instanceof Error ? error.message : String(error),
      completedAt: new Date()
    });
  }

  /**
   * Load and initialize agents
   */
  private async loadAgents(): Promise<void> {
    // This would typically load agents from configuration or database
    // For now, we'll create default agents
    this.logger.info('Loading agents');
    
    // TODO: Implement agent loading from configuration
  }

  /**
   * Resume interrupted executions
   */
  private async resumeInterruptedExecutions(): Promise<void> {
    const runningExecutions = await this.dbService.getRunningWorkflowExecutions();
    
    for (const execution of runningExecutions) {
      this.logger.info('Resuming interrupted execution', { executionId: execution.id });
      // TODO: Implement execution resumption logic
    }
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    this.taskScheduler.on('taskCompleted', this.handleTaskCompleted.bind(this));
    this.taskScheduler.on('taskFailed', this.handleTaskFailed.bind(this));
    this.messageBus.on('agentMessage', this.handleAgentMessage.bind(this));
  }

  /**
   * Handle task completion
   */
  private async handleTaskCompleted(event: any): Promise<void> {
    this.logger.info('Task completed', event);
    // Update workflow execution progress
  }

  /**
   * Handle task failure
   */
  private async handleTaskFailed(event: any): Promise<void> {
    this.logger.error('Task failed', event);
    // Handle task failure in workflow context
  }

  /**
   * Handle agent messages
   */
  private async handleAgentMessage(event: any): Promise<void> {
    this.logger.info('Agent message received', event);
    // Process inter-agent communication
  }

  /**
   * Register an agent with the workflow engine
   */
  registerAgent(agent: BaseAgent): void {
    this.agents.set(agent.id, agent);
    this.logger.info('Agent registered', { agentId: agent.id, type: agent.type });
  }

  /**
   * Unregister an agent
   */
  unregisterAgent(agentId: string): void {
    this.agents.delete(agentId);
    this.logger.info('Agent unregistered', { agentId });
  }

  /**
   * Get workflow execution status
   */
  async getExecutionStatus(executionId: string): Promise<WorkflowExecution | null> {
    return await this.dbService.getWorkflowExecution(executionId);
  }

  /**
   * Cancel workflow execution
   */
  async cancelExecution(executionId: string): Promise<void> {
    const execution = this.activeExecutions.get(executionId);
    if (execution) {
      await this.dbService.updateWorkflowExecution(executionId, {
        status: WorkflowExecutionStatus.CANCELLED,
        completedAt: new Date()
      });
      
      this.activeExecutions.delete(executionId);
      this.emit('workflowCancelled', { executionId });
    }
  }

  /**
   * Shutdown the workflow engine
   */
  async shutdown(): Promise<void> {
    this.logger.info('Shutting down workflow engine');
    
    // Cancel all active executions
    for (const executionId of this.activeExecutions.keys()) {
      await this.cancelExecution(executionId);
    }
    
    // Shutdown agents
    for (const agent of this.agents.values()) {
      await agent.shutdown();
    }
    
    // Cleanup services
    await this.taskScheduler.shutdown();
    await this.messageBus.shutdown();
    await this.dbService.shutdown();
    
    this.removeAllListeners();
  }
}

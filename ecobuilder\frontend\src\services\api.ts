import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse } from '../../../shared/types';

class APIClient {
  private client: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add request ID for tracking
        config.headers['X-Request-ID'] = this.generateRequestId();

        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('API Response Error:', error);

        // Handle specific error cases
        if (error.response?.status === 401) {
          // Unauthorized - clear token and redirect to login
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
        } else if (error.response?.status === 403) {
          // Forbidden
          console.error('Access forbidden');
        } else if (error.response?.status >= 500) {
          // Server error
          console.error('Server error occurred');
        }

        return Promise.reject(error);
      }
    );
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Generic HTTP methods
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.get(url, config);
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.post(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.put(url, data, config);
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.patch(url, data, config);
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.delete(url, config);
  }

  // Authentication methods
  async login(email: string, password: string) {
    return this.post('/auth/login', { email, password });
  }

  async register(email: string, password: string, name: string) {
    return this.post('/auth/register', { email, password, name });
  }

  async logout() {
    return this.post('/auth/logout');
  }

  async refreshToken() {
    return this.post('/auth/refresh');
  }

  // Agent methods
  async getAgents() {
    return this.get('/agents');
  }

  async getAgent(id: string) {
    return this.get(`/agents/${id}`);
  }

  async createAgent(data: any) {
    return this.post('/agents', data);
  }

  async updateAgent(id: string, data: any) {
    return this.put(`/agents/${id}`, data);
  }

  async deleteAgent(id: string) {
    return this.delete(`/agents/${id}`);
  }

  async startAgent(id: string) {
    return this.post(`/agents/${id}/start`);
  }

  async stopAgent(id: string) {
    return this.post(`/agents/${id}/stop`);
  }

  async pauseAgent(id: string) {
    return this.post(`/agents/${id}/pause`);
  }

  // Task methods
  async getTasks(params?: any) {
    return this.get('/tasks', { params });
  }

  async getTask(id: string) {
    return this.get(`/tasks/${id}`);
  }

  async createTask(data: any) {
    return this.post('/tasks', data);
  }

  async updateTask(id: string, data: any) {
    return this.put(`/tasks/${id}`, data);
  }

  async deleteTask(id: string) {
    return this.delete(`/tasks/${id}`);
  }

  async cancelTask(id: string) {
    return this.post(`/tasks/${id}/cancel`);
  }

  async retryTask(id: string) {
    return this.post(`/tasks/${id}/retry`);
  }

  // Workflow methods
  async getWorkflows() {
    return this.get('/workflows');
  }

  async getWorkflow(id: string) {
    return this.get(`/workflows/${id}`);
  }

  async createWorkflow(data: any) {
    return this.post('/workflows', data);
  }

  async updateWorkflow(id: string, data: any) {
    return this.put(`/workflows/${id}`, data);
  }

  async deleteWorkflow(id: string) {
    return this.delete(`/workflows/${id}`);
  }

  async executeWorkflow(data: any) {
    return this.post('/workflows/execute', data);
  }

  async getWorkflowExecutions() {
    return this.get('/workflows/executions');
  }

  async getWorkflowExecution(id: string) {
    return this.get(`/workflows/executions/${id}`);
  }

  async cancelWorkflowExecution(id: string) {
    return this.post(`/workflows/executions/${id}/cancel`);
  }

  // Partner methods
  async getPartners(params?: any) {
    return this.get('/partners', { params });
  }

  async getPartner(id: string) {
    return this.get(`/partners/${id}`);
  }

  async createPartner(data: any) {
    return this.post('/partners', data);
  }

  async updatePartner(id: string, data: any) {
    return this.put(`/partners/${id}`, data);
  }

  async deletePartner(id: string) {
    return this.delete(`/partners/${id}`);
  }

  // Opportunity methods
  async getOpportunities(params?: any) {
    return this.get('/opportunities', { params });
  }

  async getOpportunity(id: string) {
    return this.get(`/opportunities/${id}`);
  }

  async createOpportunity(data: any) {
    return this.post('/opportunities', data);
  }

  async updateOpportunity(id: string, data: any) {
    return this.put(`/opportunities/${id}`, data);
  }

  async deleteOpportunity(id: string) {
    return this.delete(`/opportunities/${id}`);
  }

  // Activity methods
  async getActivities(params?: any) {
    return this.get('/activities', { params });
  }

  async getActivity(id: string) {
    return this.get(`/activities/${id}`);
  }

  async createActivity(data: any) {
    return this.post('/activities', data);
  }

  async updateActivity(id: string, data: any) {
    return this.put(`/activities/${id}`, data);
  }

  async deleteActivity(id: string) {
    return this.delete(`/activities/${id}`);
  }

  // Analytics methods
  async getAnalytics(params?: any) {
    return this.get('/analytics', { params });
  }

  async getDashboardStats() {
    return this.get('/analytics/dashboard');
  }

  async getAgentStats() {
    return this.get('/analytics/agents');
  }

  async getTaskStats() {
    return this.get('/analytics/tasks');
  }

  async getPartnerStats() {
    return this.get('/analytics/partners');
  }

  // File upload
  async uploadFile(file: File, onProgress?: (progress: number) => void) {
    const formData = new FormData();
    formData.append('file', file);

    return this.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  }

  // Health check
  async healthCheck() {
    return this.get('/health');
  }

  // Set auth token
  setAuthToken(token: string): void {
    localStorage.setItem('auth_token', token);
  }

  // Clear auth token
  clearAuthToken(): void {
    localStorage.removeItem('auth_token');
  }

  // Get current auth token
  getAuthToken(): string | null {
    return localStorage.getItem('auth_token');
  }
}

// Create and export a singleton instance
export const apiClient = new APIClient();

// Export the class for testing or custom instances
export { APIClient };

#!/bin/bash

# EcoBuilder Development Startup Script
# This script starts all necessary services for development

set -e

echo "🌱 Starting EcoBuilder Multi-Agent System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_step "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        print_warning "Docker is not installed. You'll need to set up PostgreSQL and Redis manually."
    fi
    
    print_status "Dependencies check completed."
}

# Install dependencies
install_dependencies() {
    print_step "Installing dependencies..."
    
    # Install shared dependencies
    print_status "Installing shared dependencies..."
    cd shared && npm install && cd ..
    
    # Install backend dependencies
    print_status "Installing backend dependencies..."
    cd backend && npm install && cd ..
    
    # Install frontend dependencies
    print_status "Installing frontend dependencies..."
    cd frontend && npm install && cd ..
    
    print_status "All dependencies installed successfully."
}

# Setup environment files
setup_env() {
    print_step "Setting up environment files..."
    
    # Backend .env
    if [ ! -f "backend/.env" ]; then
        print_status "Creating backend .env file..."
        cat > backend/.env << EOF
# Database
DATABASE_URL="postgresql://ecobuilder:password@localhost:5432/ecobuilder"

# Redis
REDIS_URL="redis://localhost:6379"

# API Keys (Replace with your actual keys)
OPENAI_API_KEY="your-openai-api-key-here"
CRUNCHBASE_API_KEY="your-crunchbase-api-key-here"
LINKEDIN_ACCESS_TOKEN="your-linkedin-token-here"
SENDGRID_API_KEY="your-sendgrid-api-key-here"

# Application
NODE_ENV="development"
PORT=3000
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
FRONTEND_URL="http://localhost:5173"

# Optional External APIs
CLEARBIT_API_KEY="your-clearbit-api-key"
ZOOMINFO_API_KEY="your-zoominfo-api-key"
HUBSPOT_API_KEY="your-hubspot-api-key"
AWS_S3_BUCKET="your-s3-bucket"
AWS_SES_REGION="us-east-1"
EOF
        print_warning "Please update backend/.env with your actual API keys!"
    else
        print_status "Backend .env file already exists."
    fi
    
    # Frontend .env
    if [ ! -f "frontend/.env" ]; then
        print_status "Creating frontend .env file..."
        cat > frontend/.env << EOF
REACT_APP_API_URL="http://localhost:3000/api"
REACT_APP_WS_URL="http://localhost:3000"
EOF
    else
        print_status "Frontend .env file already exists."
    fi
}

# Start database services
start_services() {
    print_step "Starting database services..."
    
    if command -v docker &> /dev/null; then
        print_status "Starting PostgreSQL and Redis with Docker..."
        
        # Create docker-compose.yml if it doesn't exist
        if [ ! -f "docker-compose.dev.yml" ]; then
            cat > docker-compose.dev.yml << EOF
version: '3.8'
services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: ecobuilder
      POSTGRES_USER: ecobuilder
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ecobuilder"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
EOF
        fi
        
        docker-compose -f docker-compose.dev.yml up -d
        
        # Wait for services to be ready
        print_status "Waiting for services to be ready..."
        sleep 10
        
    else
        print_warning "Docker not available. Please ensure PostgreSQL and Redis are running manually."
        print_warning "PostgreSQL should be running on localhost:5432 with database 'ecobuilder'"
        print_warning "Redis should be running on localhost:6379"
    fi
}

# Setup database
setup_database() {
    print_step "Setting up database..."
    
    cd backend
    
    # Generate Prisma client
    print_status "Generating Prisma client..."
    npx prisma generate
    
    # Run migrations
    print_status "Running database migrations..."
    npx prisma migrate dev --name init
    
    # Seed database (if seed file exists)
    if [ -f "prisma/seed.ts" ] || [ -f "prisma/seed.js" ]; then
        print_status "Seeding database..."
        npx prisma db seed
    fi
    
    cd ..
    print_status "Database setup completed."
}

# Start development servers
start_dev_servers() {
    print_step "Starting development servers..."
    
    # Create a new tmux session or use screen for multiple terminals
    if command -v tmux &> /dev/null; then
        print_status "Starting servers in tmux session 'ecobuilder'..."
        
        # Kill existing session if it exists
        tmux kill-session -t ecobuilder 2>/dev/null || true
        
        # Create new session
        tmux new-session -d -s ecobuilder
        
        # Backend window
        tmux rename-window -t ecobuilder:0 'backend'
        tmux send-keys -t ecobuilder:0 'cd backend && npm run dev' C-m
        
        # Frontend window
        tmux new-window -t ecobuilder -n 'frontend'
        tmux send-keys -t ecobuilder:1 'cd frontend && npm run dev' C-m
        
        print_status "Servers started in tmux session 'ecobuilder'"
        print_status "To attach to the session: tmux attach -t ecobuilder"
        print_status "To switch between windows: Ctrl+b then 0 (backend) or 1 (frontend)"
        print_status "To detach from session: Ctrl+b then d"
        
    else
        print_warning "tmux not available. Starting servers in background..."
        
        # Start backend
        cd backend
        npm run dev &
        BACKEND_PID=$!
        cd ..
        
        # Start frontend
        cd frontend
        npm run dev &
        FRONTEND_PID=$!
        cd ..
        
        print_status "Backend started with PID: $BACKEND_PID"
        print_status "Frontend started with PID: $FRONTEND_PID"
        
        # Create a simple stop script
        cat > stop-dev.sh << EOF
#!/bin/bash
echo "Stopping development servers..."
kill $BACKEND_PID 2>/dev/null || true
kill $FRONTEND_PID 2>/dev/null || true
echo "Servers stopped."
EOF
        chmod +x stop-dev.sh
        
        print_status "Created stop-dev.sh script to stop the servers"
    fi
}

# Main execution
main() {
    print_status "EcoBuilder Development Setup Starting..."
    
    # Change to project root
    cd "$(dirname "$0")/.."
    
    check_dependencies
    
    # Ask user what to do
    echo ""
    echo "What would you like to do?"
    echo "1) Full setup (install deps, setup env, start services, setup DB, start dev servers)"
    echo "2) Install dependencies only"
    echo "3) Setup environment files only"
    echo "4) Start services only (PostgreSQL, Redis)"
    echo "5) Setup database only"
    echo "6) Start development servers only"
    echo "7) Quick start (assumes everything is already set up)"
    echo ""
    read -p "Enter your choice (1-7): " choice
    
    case $choice in
        1)
            install_dependencies
            setup_env
            start_services
            setup_database
            start_dev_servers
            ;;
        2)
            install_dependencies
            ;;
        3)
            setup_env
            ;;
        4)
            start_services
            ;;
        5)
            setup_database
            ;;
        6)
            start_dev_servers
            ;;
        7)
            start_dev_servers
            ;;
        *)
            print_error "Invalid choice. Exiting."
            exit 1
            ;;
    esac
    
    echo ""
    print_status "🎉 EcoBuilder setup completed!"
    echo ""
    echo "📱 Frontend: http://localhost:5173"
    echo "🔧 Backend API: http://localhost:3000/api"
    echo "📊 Database: PostgreSQL on localhost:5432"
    echo "🔄 Redis: localhost:6379"
    echo ""
    print_warning "Don't forget to update your API keys in backend/.env!"
    echo ""
}

# Run main function
main "$@"

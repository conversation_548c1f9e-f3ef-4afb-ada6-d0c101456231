<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dify生态合作伙伴分析平台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-content {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        /* 文件上传区域 */
        .upload-section {
            margin-bottom: 40px;
        }

        .upload-area {
            border: 3px dashed #e0e0e0;
            border-radius: 16px;
            padding: 60px 40px;
            text-align: center;
            background: #fafafa;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .upload-area:hover {
            border-color: #007bff;
            background: #f0f8ff;
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: #007bff;
            background: #e3f2fd;
        }

        .upload-status {
            display: none;
            align-items: center;
            gap: 20px;
            text-align: left;
        }

        .status-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            flex-shrink: 0;
        }

        .status-icon.uploading {
            background: #e3f2fd;
            color: #1976d2;
        }

        .status-icon.success {
            background: #e8f5e8;
            color: #4caf50;
        }

        .status-icon.error {
            background: #ffebee;
            color: #f44336;
        }

        .status-icon .fa-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* AI分析按钮 */
        .ai-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .ai-button {
            width: 100%;
            max-width: 400px;
            padding: 20px 30px;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin: 0 auto;
        }

        .ai-button.default {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .ai-button.blue {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .ai-button.green {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .ai-button.processing {
            background: linear-gradient(135deg, #6c757d, #545b62);
            cursor: not-allowed;
            opacity: 0.8;
        }

        .ai-button:hover:not(.processing) {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .loading-spinner {
            display: none;
            width: 24px;
            height: 24px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s linear infinite;
        }

        .ai-button.processing .loading-spinner {
            display: inline-block;
        }

        .ai-button.processing .button-icon {
            display: none;
        }

        /* 测试面板样式 */
        .test-panel {
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 2px solid #e3f2fd;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 25px;
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            background: #fafafa;
        }

        .test-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: 600;
        }

        .test-button {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }

        .status-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 250px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .file-info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .upload-area {
                padding: 40px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Dify生态合作伙伴分析平台</h1>
            <p>智能分析，精准匹配，助力生态合作</p>
        </div>

        <!-- 测试面板 (仅在测试模式下显示) -->
        <div class="test-panel" id="testPanel" style="display: none;">
            <h1>🧪 状态更新测试页面</h1>
            
            <!-- 文件上传测试 -->
            <div class="test-section">
                <h2>📁 文件上传状态测试</h2>
                <div class="upload-area" id="testUploadArea" onclick="triggerTestFileInput()">
                    <input type="file" id="testFileInput" style="display: none;" accept=".pdf,.doc,.docx,.csv,.xls,.xlsx" onchange="handleTestFileSelect(event)">

                    <div id="testUploadDefault">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #ccc;"></i>
                        <p style="font-size: 18px; margin: 15px 0;">点击上传或拖拽文件到此处</p>
                        <p style="font-size: 14px; color: #666;">支持格式：PDF、DOC、DOCX、CSV、XLS、XLSX</p>
                    </div>

                    <div class="upload-status" id="testUploadStatus">
                        <div class="status-icon" id="testStatusIcon">
                            <i class="fas fa-spinner"></i>
                        </div>
                        <div>
                            <div id="testStatusText" style="font-size: 18px; font-weight: 600;">正在上传...</div>
                            <div id="testStatusSubtext" style="font-size: 14px; color: #666; margin-top: 5px;">请稍候</div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px;">
                    <button class="test-button btn-primary" onclick="testUploadStatus()">测试上传状态</button>
                    <button class="test-button btn-success" onclick="testUploadSuccess()">测试成功状态</button>
                    <button class="test-button btn-danger" onclick="testUploadError()">测试错误状态</button>
                    <button class="test-button btn-warning" onclick="resetTestUpload()">重置状态</button>
                </div>

                <div id="testUploadedFileInfo" class="file-info" style="display: none;">
                    <strong>📄 已上传文件：</strong>
                    <div id="testFileName" style="margin-top: 5px;"></div>
                    <div id="testFileSize" style="font-size: 12px; color: #666; margin-top: 3px;"></div>
                </div>
            </div>

            <!-- AI按钮测试 -->
            <div class="test-section">
                <h2>🤖 AI分析按钮测试</h2>
                <button class="ai-button default" id="testAiButton" onclick="handleTestAIButtonClick()">
                    <div class="loading-spinner"></div>
                    <i class="fas fa-magic button-icon"></i>
                    <span class="button-text">开始AI生态分析</span>
                </button>

                <div style="margin-top: 20px;">
                    <button class="test-button btn-primary" onclick="testAIProcessing()">测试处理状态</button>
                    <button class="test-button btn-primary" onclick="testAIEvaluation()">测试评估阶段</button>
                    <button class="test-button btn-success" onclick="testAISelection()">测试选择阶段</button>
                    <button class="test-button btn-warning" onclick="resetTestAIButton()">重置按钮</button>
                </div>
            </div>

            <!-- 状态日志 -->
            <div class="test-section">
                <h2>📋 状态日志</h2>
                <div class="status-log" id="testStatusLog"></div>
                <button class="test-button btn-warning" onclick="clearTestLog()">清空日志</button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 文件上传区域 -->
            <div class="upload-section">
                <h2 style="margin-bottom: 20px; color: #333;">📄 上传合作文档</h2>
                <div class="upload-area" id="uploadArea" onclick="triggerFileInput()">
                    <input type="file" id="fileInput" style="display: none;" accept=".pdf,.doc,.docx,.csv,.xls,.xlsx" onchange="handleFileSelect(event)">
                    
                    <div id="uploadDefault">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 64px; color: #ccc; margin-bottom: 20px;"></i>
                        <h3 style="margin-bottom: 10px; color: #333;">上传您的合作文档</h3>
                        <p style="color: #666; margin-bottom: 15px;">支持 PDF、Word、Excel 等格式</p>
                        <p style="font-size: 14px; color: #999;">点击此处选择文件或直接拖拽文件到此区域</p>
                    </div>
                    
                    <div class="upload-status" id="uploadStatus">
                        <div class="status-icon" id="statusIcon">
                            <i class="fas fa-spinner"></i>
                        </div>
                        <div>
                            <div id="statusText" style="font-size: 20px; font-weight: 600;">正在上传文件...</div>
                            <div id="statusSubtext" style="font-size: 16px; color: #666; margin-top: 8px;">请稍候，正在处理您的文件</div>
                        </div>
                    </div>
                </div>
                
                <div id="uploadedFileInfo" class="file-info" style="display: none;">
                    <strong>📄 已上传文件：</strong>
                    <div id="fileName" style="margin-top: 8px; font-size: 16px;"></div>
                    <div id="fileSize" style="font-size: 14px; color: #666; margin-top: 5px;"></div>
                </div>
            </div>

            <!-- AI分析按钮 -->
            <div class="ai-section">
                <h2 style="margin-bottom: 30px; color: #333;">🤖 AI智能分析</h2>
                <button class="ai-button default" id="aiButton" onclick="handleAIButtonClick()">
                    <div class="loading-spinner"></div>
                    <i class="fas fa-magic button-icon"></i>
                    <span class="button-text">开始AI生态分析</span>
                </button>
                <p style="margin-top: 20px; color: #666; font-size: 14px;">
                    AI将分析您的文档，智能匹配最适合的生态合作伙伴
                </p>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let uploadedFile = null;
        let currentStage = 0;
        let testAIStage = 0;

        // 日志函数
        function testLog(message) {
            const logElement = document.getElementById('testStatusLog');
            if (logElement) {
                const timestamp = new Date().toLocaleTimeString();
                logElement.textContent += `[${timestamp}] ${message}\n`;
                logElement.scrollTop = logElement.scrollHeight;
            }
            console.log(message);
        }

        // 文件上传功能
        function triggerFileInput() {
            document.getElementById('fileInput').click();
        }

        function triggerTestFileInput() {
            testLog('触发文件选择器');
            document.getElementById('testFileInput').click();
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                console.log('File selected:', file.name);
                uploadedFile = file;
                showUploadStatus();
                
                setTimeout(() => {
                    showUploadSuccess();
                }, 2000);
            }
        }

        function handleTestFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                testLog(`文件已选择: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`);
                
                // 显示文件信息
                const fileInfo = document.getElementById('testUploadedFileInfo');
                const fileName = document.getElementById('testFileName');
                const fileSize = document.getElementById('testFileSize');
                
                if (fileName) fileName.textContent = file.name;
                if (fileSize) fileSize.textContent = `大小: ${(file.size / 1024).toFixed(1)} KB`;
                if (fileInfo) fileInfo.style.display = 'block';
                
                // 自动测试上传流程
                testUploadStatus();
                setTimeout(() => {
                    testUploadSuccess();
                }, 2000);
            }
        }

        function showUploadStatus() {
            const defaultArea = document.getElementById('uploadDefault');
            const statusArea = document.getElementById('uploadStatus');
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');
            const statusSubtext = document.getElementById('statusSubtext');

            if (defaultArea) defaultArea.style.display = 'none';
            if (statusArea) statusArea.style.display = 'flex';
            
            if (statusIcon) {
                statusIcon.className = 'status-icon uploading';
                statusIcon.innerHTML = '<i class="fas fa-spinner"></i>';
            }
            if (statusText) statusText.textContent = '正在上传文件...';
            if (statusSubtext) statusSubtext.textContent = '请稍候，正在处理您的文件';
        }

        function showUploadSuccess() {
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');
            const statusSubtext = document.getElementById('statusSubtext');
            const fileInfo = document.getElementById('uploadedFileInfo');
            const fileName = document.getElementById('fileName');
            const fileSize = document.getElementById('fileSize');

            if (statusIcon) {
                statusIcon.className = 'status-icon success';
                statusIcon.innerHTML = '<i class="fas fa-check"></i>';
            }
            if (statusText) statusText.textContent = '文件上传成功！';
            if (statusSubtext) statusSubtext.textContent = '文件已成功上传并验证';
            
            if (uploadedFile) {
                if (fileName) fileName.textContent = uploadedFile.name;
                if (fileSize) fileSize.textContent = `大小: ${(uploadedFile.size / 1024).toFixed(1)} KB`;
                if (fileInfo) fileInfo.style.display = 'block';
            }
        }

        // 测试函数
        function testUploadStatus() {
            testLog('开始测试上传状态');
            const defaultArea = document.getElementById('testUploadDefault');
            const statusArea = document.getElementById('testUploadStatus');
            const statusIcon = document.getElementById('testStatusIcon');
            const statusText = document.getElementById('testStatusText');
            const statusSubtext = document.getElementById('testStatusSubtext');

            if (defaultArea) defaultArea.style.display = 'none';
            if (statusArea) statusArea.style.display = 'flex';
            
            if (statusIcon) {
                statusIcon.className = 'status-icon uploading';
                statusIcon.innerHTML = '<i class="fas fa-spinner"></i>';
            }
            if (statusText) statusText.textContent = '正在上传文件...';
            if (statusSubtext) statusSubtext.textContent = '请稍候，正在处理您的文件';
        }

        function testUploadSuccess() {
            testLog('测试上传成功状态');
            const statusIcon = document.getElementById('testStatusIcon');
            const statusText = document.getElementById('testStatusText');
            const statusSubtext = document.getElementById('testStatusSubtext');

            if (statusIcon) {
                statusIcon.className = 'status-icon success';
                statusIcon.innerHTML = '<i class="fas fa-check"></i>';
            }
            if (statusText) statusText.textContent = '文件上传成功！';
            if (statusSubtext) statusSubtext.textContent = '文件已成功上传并验证';
        }

        function testUploadError() {
            testLog('测试上传错误状态');
            const defaultArea = document.getElementById('testUploadDefault');
            const statusArea = document.getElementById('testUploadStatus');
            const statusIcon = document.getElementById('testStatusIcon');
            const statusText = document.getElementById('testStatusText');
            const statusSubtext = document.getElementById('testStatusSubtext');

            if (defaultArea) defaultArea.style.display = 'none';
            if (statusArea) statusArea.style.display = 'flex';
            
            if (statusIcon) {
                statusIcon.className = 'status-icon error';
                statusIcon.innerHTML = '<i class="fas fa-times"></i>';
            }
            if (statusText) statusText.textContent = '上传失败';
            if (statusSubtext) statusSubtext.textContent = '请检查文件格式和大小';
        }

        function resetTestUpload() {
            testLog('重置上传状态');
            const defaultArea = document.getElementById('testUploadDefault');
            const statusArea = document.getElementById('testUploadStatus');
            const fileInfo = document.getElementById('testUploadedFileInfo');
            
            if (defaultArea) defaultArea.style.display = 'block';
            if (statusArea) statusArea.style.display = 'none';
            if (fileInfo) fileInfo.style.display = 'none';
            
            // Reset file input
            const fileInput = document.getElementById('testFileInput');
            if (fileInput) fileInput.value = '';
        }

        // AI按钮功能
        function handleAIButtonClick() {
            const button = document.getElementById('aiButton');
            const buttonText = button.querySelector('.button-text');
            
            if (button.classList.contains('processing')) {
                return;
            }

            currentStage = (currentStage + 1) % 4;
            
            switch(currentStage) {
                case 1:
                    button.classList.remove('default', 'blue', 'green');
                    button.classList.add('processing');
                    buttonText.textContent = '处理中...';
                    break;
                case 2:
                    button.classList.remove('processing', 'default', 'green');
                    button.classList.add('blue');
                    buttonText.textContent = '评估合作伙伴需求';
                    break;
                case 3:
                    button.classList.remove('processing', 'default', 'blue');
                    button.classList.add('green');
                    buttonText.textContent = '选择合作伙伴';
                    break;
                case 0:
                    button.classList.remove('processing', 'blue', 'green');
                    button.classList.add('default');
                    buttonText.textContent = '开始AI生态分析';
                    break;
            }
        }

        function handleTestAIButtonClick() {
            testLog('AI按钮被点击');
            const button = document.getElementById('testAiButton');
            const buttonText = button.querySelector('.button-text');
            
            if (button.classList.contains('processing')) {
                testLog('按钮正在处理中，忽略点击');
                return;
            }

            testAIStage = (testAIStage + 1) % 4;
            
            switch(testAIStage) {
                case 1:
                    testLog('进入处理阶段');
                    button.classList.remove('default', 'blue', 'green');
                    button.classList.add('processing');
                    buttonText.textContent = '处理中...';
                    break;
                case 2:
                    testLog('进入评估阶段');
                    button.classList.remove('processing', 'default', 'green');
                    button.classList.add('blue');
                    buttonText.textContent = '评估合作伙伴需求';
                    break;
                case 3:
                    testLog('进入选择阶段');
                    button.classList.remove('processing', 'default', 'blue');
                    button.classList.add('green');
                    buttonText.textContent = '选择合作伙伴';
                    break;
                case 0:
                    testLog('重置到初始状态');
                    button.classList.remove('processing', 'blue', 'green');
                    button.classList.add('default');
                    buttonText.textContent = '开始AI生态分析';
                    break;
            }
        }

        function testAIProcessing() {
            testLog('手动测试处理状态');
            const button = document.getElementById('testAiButton');
            const buttonText = button.querySelector('.button-text');
            
            button.classList.remove('default', 'blue', 'green');
            button.classList.add('processing');
            buttonText.textContent = '处理中...';
            testAIStage = 1;
        }

        function testAIEvaluation() {
            testLog('手动测试评估阶段');
            const button = document.getElementById('testAiButton');
            const buttonText = button.querySelector('.button-text');
            
            button.classList.remove('default', 'processing', 'green');
            button.classList.add('blue');
            buttonText.textContent = '评估合作伙伴需求';
            testAIStage = 2;
        }

        function testAISelection() {
            testLog('手动测试选择阶段');
            const button = document.getElementById('testAiButton');
            const buttonText = button.querySelector('.button-text');
            
            button.classList.remove('default', 'processing', 'blue');
            button.classList.add('green');
            buttonText.textContent = '选择合作伙伴';
            testAIStage = 3;
        }

        function resetTestAIButton() {
            testLog('手动重置AI按钮');
            const button = document.getElementById('testAiButton');
            const buttonText = button.querySelector('.button-text');
            
            button.classList.remove('processing', 'blue', 'green');
            button.classList.add('default');
            buttonText.textContent = '开始AI生态分析';
            testAIStage = 0;
        }

        function clearTestLog() {
            const logElement = document.getElementById('testStatusLog');
            if (logElement) {
                logElement.textContent = '';
            }
            testLog('日志已清空');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            
            // 检查是否为测试模式
            if (window.location.search.includes('test=true')) {
                const testPanel = document.getElementById('testPanel');
                if (testPanel) {
                    testPanel.style.display = 'block';
                    testLog('测试模式已启用');
                    console.log('测试面板已显示');
                }
            }
            
            // 添加拖拽功能
            const uploadAreas = document.querySelectorAll('.upload-area');
            uploadAreas.forEach(area => {
                area.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });
                
                area.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });
                
                area.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                    
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        const fileInput = this.querySelector('input[type="file"]');
                        if (fileInput) {
                            fileInput.files = files;
                            fileInput.dispatchEvent(new Event('change'));
                        }
                    }
                });
            });
        });
    </script>
</body>
</html>

# EcoBuilder 文件上传系统使用指南

## 概述

EcoBuilder 文件上传系统提供了完整的文件上传、管理和状态反馈功能。系统支持拖拽上传、进度跟踪、实时状态更新，并提供了友好的用户界面。

## 功能特性

### ✅ 核心功能
- **多文件上传**: 支持同时上传多个文件（最多10个）
- **拖拽上传**: 支持拖拽文件到上传区域
- **实时进度**: 显示上传进度条和百分比
- **状态反馈**: 清晰的成功/失败状态提示
- **文件预览**: 上传后可查看文件列表
- **文件管理**: 删除、下载、重命名等操作

### 📁 支持的文件格式
- **文档**: PDF, DOC, DOCX, TXT
- **表格**: CSV, XLS, XLSX  
- **图片**: PNG, JPG, JPEG, GIF
- **最大文件大小**: 20MB
- **最大文件数量**: 10个

### 🎨 用户界面特性
- **响应式设计**: 适配各种屏幕尺寸
- **动画效果**: 平滑的状态转换动画
- **视觉反馈**: 清晰的图标和颜色状态指示
- **统计信息**: 显示上传统计数据

## 组件使用

### React 组件使用示例

```tsx
import { FileUpload } from '../components/common/FileUpload';

function MyComponent() {
  const handleUploadSuccess = (files) => {
    console.log('上传成功:', files);
    // 处理上传成功的文件
  };

  const handleUploadError = (error) => {
    console.error('上传失败:', error);
    // 处理上传错误
  };

  return (
    <FileUpload
      onUploadSuccess={handleUploadSuccess}
      onUploadError={handleUploadError}
      acceptedFileTypes={['.pdf', '.doc', '.docx', '.csv', '.xlsx']}
      maxFileSize={20 * 1024 * 1024} // 20MB
      maxFiles={5}
      multiple={true}
    />
  );
}
```

### 组件属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `onUploadSuccess` | `(files: UploadedFile[]) => void` | - | 上传成功回调 |
| `onUploadError` | `(error: string) => void` | - | 上传失败回调 |
| `acceptedFileTypes` | `string[]` | `['.pdf', '.doc', ...]` | 允许的文件类型 |
| `maxFileSize` | `number` | `10MB` | 最大文件大小（字节） |
| `maxFiles` | `number` | `5` | 最大文件数量 |
| `multiple` | `boolean` | `true` | 是否允许多文件上传 |
| `className` | `string` | `''` | 自定义CSS类名 |

## API 接口

### 上传文件
```http
POST /api/files/upload
Content-Type: multipart/form-data

Body: FormData with 'file' field
```

**响应示例:**
```json
{
  "success": true,
  "message": "文件上传成功",
  "data": {
    "id": "file_123456",
    "name": "document.pdf",
    "size": 1024000,
    "type": "application/pdf",
    "url": "/api/files/file_123456/download",
    "uploadedAt": "2024-01-01T12:00:00Z"
  }
}
```

### 获取文件列表
```http
GET /api/files
```

### 下载文件
```http
GET /api/files/:id/download
```

### 删除文件
```http
DELETE /api/files/:id
```

## 状态管理

### 上传状态
- **idle**: 初始状态，等待用户选择文件
- **uploading**: 正在上传，显示进度条
- **success**: 上传成功，显示成功图标
- **error**: 上传失败，显示错误信息

### 状态转换流程
```
idle → uploading → success/error → idle (重置)
```

## 错误处理

### 常见错误类型
1. **文件类型不支持**: 检查 `acceptedFileTypes` 配置
2. **文件过大**: 检查 `maxFileSize` 限制
3. **文件数量超限**: 检查 `maxFiles` 限制
4. **网络错误**: 检查网络连接和服务器状态
5. **服务器错误**: 检查后端API状态

### 错误处理示例
```tsx
const handleUploadError = (error: string) => {
  // 显示用户友好的错误信息
  if (error.includes('文件类型')) {
    toast.error('请选择支持的文件格式');
  } else if (error.includes('文件大小')) {
    toast.error('文件大小超出限制');
  } else {
    toast.error('上传失败，请重试');
  }
};
```

## 自定义样式

### CSS 类名
- `.upload-area`: 上传区域容器
- `.upload-area.dragover`: 拖拽悬停状态
- `.progress-bar`: 进度条
- `.status-icon`: 状态图标
- `.success-icon`: 成功图标

### 自定义主题
```css
.upload-area {
  border-color: #your-brand-color;
}

.upload-area.dragover {
  background-color: #your-hover-color;
}

.progress-bar {
  background-color: #your-progress-color;
}
```

## 演示页面

访问 `/demo/file-upload-demo.html` 查看完整的文件上传演示，包括：
- 拖拽上传演示
- 进度跟踪演示
- 状态反馈演示
- 文件列表管理
- 统计信息显示

## 最佳实践

### 1. 用户体验
- 提供清晰的文件格式说明
- 显示实时上传进度
- 提供明确的成功/失败反馈
- 支持批量操作

### 2. 性能优化
- 限制文件大小和数量
- 使用分片上传处理大文件
- 实现断点续传功能
- 压缩图片文件

### 3. 安全考虑
- 验证文件类型和大小
- 扫描恶意文件
- 限制上传频率
- 使用安全的文件存储

### 4. 错误处理
- 提供详细的错误信息
- 实现重试机制
- 记录错误日志
- 优雅降级处理

## 故障排除

### 常见问题

**Q: 文件上传失败，显示网络错误**
A: 检查后端服务是否运行，确认API端点正确

**Q: 拖拽上传不工作**
A: 确认浏览器支持HTML5拖拽API，检查事件监听器

**Q: 进度条不显示**
A: 确认使用XMLHttpRequest而非fetch API进行上传

**Q: 文件列表不更新**
A: 检查状态管理和组件重新渲染逻辑

### 调试技巧
1. 打开浏览器开发者工具查看网络请求
2. 检查控制台错误信息
3. 验证文件格式和大小限制
4. 测试不同浏览器的兼容性

## 更新日志

### v1.0.0 (2024-01-01)
- ✅ 基础文件上传功能
- ✅ 拖拽上传支持
- ✅ 实时进度跟踪
- ✅ 状态反馈系统
- ✅ 文件管理界面
- ✅ 响应式设计

### 计划功能
- 🔄 断点续传
- 🔄 文件预览
- 🔄 批量下载
- 🔄 文件夹上传
- 🔄 云存储集成
